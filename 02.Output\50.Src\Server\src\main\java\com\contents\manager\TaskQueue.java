package com.contents.manager;

import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.async.DeferredResult;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.model.ApiResult;

import lombok.Data;

/**
 * 下記の機能を持つ<br>
 * 指令系APから受信したAPIの処理ステータスを管理<br>
 * サーバーとブラウザの間で、WebSocketで送受信ステータス管理<br>
 * 1分毎で、printMetricsを実施されTaskの情報をLogに残す
 */
@Component
@EnableScheduling
public class TaskQueue {
	private final Logger log = LoggerFactory.getLogger(TaskQueue.class);
	// <SubTaskId, TaskId>
	private final Map<String, String> subTask2TaskMap = new ConcurrentHashMap<>();
	private final Map<String, TaskInfo> taskInfoMap = new ConcurrentHashMap<>();

	public void print() {

		for (Iterator<Map.Entry<String, TaskInfo>> ite = taskInfoMap.entrySet().iterator(); ite.hasNext();) {

			Map.Entry<String, TaskInfo> entry = ite.next();

			String key = entry.getKey();
			TaskInfo taskInfo = entry.getValue();

			String value = taskInfo.getSubTaskList().stream()
					.map(s -> MessageFormatUtil.format("SubTaskID: {}", s))
					.collect(Collectors.joining(System.lineSeparator()));

			log.info("★★★★★★★★ TaskQueue Print{}TaskID:{}{}    {}", System.lineSeparator(), key, System.lineSeparator(), value);
		}

	}

	@Data
	private class TaskInfo {
		ApiResult apiResult;
		DeferredResult<ResponseEntity<ApiResult>> deferredResult;
		Set<String> subTaskList = new HashSet<>(0); //該当Taskに紐付けたSubTaskのList

		// 該当Task情報をすべて発送済みか
		boolean sendMsgDone;
	}

	/**
	 * 該当Taskの関連情報を片付ける
	 *
	 * @param taskId Task Id
	 */
	public void clearTaskInfo(String taskId) {

		log.info("clearTaskInfo: id={} start.", taskId);

		TaskInfo info = taskInfoMap.remove(taskId);
		if (info == null) {
			log.info("clearTaskInfo: Task has been removed.");
		} else {
			log.info("clearTaskInfo: Task to be remove.");
		}
		for (Map.Entry<String, String> task : subTask2TaskMap.entrySet()) {
			if (task.getValue().equals(taskId)) {
				log.info("clearTaskInfo: subTaskId={} is removed", task.getKey());
				subTask2TaskMap.remove(task.getKey());
			}
		}
		log.info("clearTaskInfo: id={} complate.", taskId);
	}

	/**
	 * UUIDにPrefixを付けて、TaskIdとして返す
	 *
	 * @param prefix 機能を区別する為のKeyword
	 * @return Task Id
	 */
	public String getTaskId(String prefix) {
		UUID id = UUID.randomUUID();
		if (StringUtils.isEmpty(prefix)) {
			return id.toString();
		}
		return MessageFormatUtil.format("{}_{}", prefix, id);
	}

	/**
	 * TaskのDeferredResultを保存
	 *
	 * @param taskId Task Id
	 * @param deferredResult Deferred Result
	 * @return TaskId
	 */
	public String putDeferredResult(String taskId, DeferredResult deferredResult) {
		log.info("putDeferredResult: id={}", taskId);

		if (taskId == null) {
			String id = getTaskId("AutoCreate");
			return putDeferredResult(id, deferredResult);
		}

		TaskInfo taskInfo = getTaskInfo(taskId);
		taskInfo.setDeferredResult(deferredResult);

		return taskId;
	}

	/**
	 * TaskのAPI Resultを保存。目的は、Task終了する時に、このAPIResultを設定する
	 *
	 * @param taskId Task Id
	 * @param apiResult API Result
	 * @return 受け取ったApiResult
	 */
	public ApiResult putApiResult(String taskId, ApiResult apiResult) {
		log.info("Put ApiResult to que, TaskId: {}", taskId);

		TaskInfo taskInfo = getTaskInfo(taskId);
		taskInfo.setApiResult(apiResult);

		return apiResult;
	}

	/**
	 * SubTaskIdとTaskIDを紐付けて、subTask2TaskMapに一対一の関係を保存
	 *
	 * @param taskId    親のTaskId
	 * @param subTaskId 子のTaskId
	 * @return subTaskIdの子TaskId
	 */
	public String putSubTask(String taskId, String subTaskId) {

		log.info("putSubTask: Put subTaskId to que, TaskId: {}, SubId:{}", taskId, subTaskId);

		if (taskId == null) {
			log.info("putSubTask: return without taskId");
			return null;
		}

		TaskInfo taskInfo = getTaskInfo(taskId);
		taskInfo.getSubTaskList().add(subTaskId);

		subTask2TaskMap.put(subTaskId, taskId);
		return subTaskId;
	}

//	/**
//	 * SubTaskIdとTaskIDを紐付けて、subTask2TaskMapに一対一の関係を保存
//	 *
//	 * @param taskId    親のTaskId
//	 * @param subTaskId 子のTaskId
//	 * @param runnable React.jsへ送信する処理のRunnableインスタンス
//	 * @return subTaskIdの子TaskId
//	 */
//	public String putSubTask(String taskId, String subTaskId, MediaReceiverMutualExclusionInfo mediaReceiverMutualExclusionInfo) {
//
//		log.info("putSubTask: Put subTaskId to que, TaskId: {}, SubId:{}", taskId, subTaskId);
//
//		if (taskId == null) {
//			log.info("putSubTask: return without taskId");
//			return null;
//		}
//
//		TaskInfo taskInfo = getTaskInfo(taskId);
//		taskInfo.getSubTaskList().add(subTaskId);
//
//		subTask2TaskMap.put(subTaskId, taskId);
//
//		return subTaskId;
//	}

	public void runnable(String taskId, String subTaskId) {

	}

	/**
	 * API中の情報をすべて処理して、送信先に送り出したことを知らせる
	 *
	 * @param taskId　Task Id
	 */
	public void taskSendMsgDone(String taskId) {
		if (!taskInfoMap.containsKey(taskId)) {
			return;
		}

		TaskInfo info = taskInfoMap.get(taskId);
		info.setSendMsgDone(true);

		log.info("taskSendMsgDone, taskId:{}", taskId);
		if (info.getSubTaskList().size() == 0) {
			taskDoneBySetDeferredResult(taskId);
		}
	}

	/**
	 * 複数構成のSubTaskに対して、SubTaskの終了処理を実施
	 *
	 * @param subTaskId SubのTaskId
	 */
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public void subTaskDone(String subTaskId) {
		TaskInfo subTaskInfo = taskInfoMap.get(subTaskId);
		if (subTaskInfo != null) {
			//親Task/SubTaskに関係なく、DeferredResultがあれば、DeferredResultを処理。
			if (subTaskInfo.getDeferredResult() != null) {
				log.info("subTaskDone: {} as a main task and done also", subTaskId);
				subTaskInfo.sendMsgDone = true;
				taskDoneBySetDeferredResult(subTaskId);
			}
		}

		// 親のtaskIdがあるかをチェック
		String taskId = subTask2TaskMap.remove(subTaskId);
		if (taskId == null) {
			log.info("subTaskDone: subTaskId={} with no parent task", subTaskId);
			return;
		}

		if (!taskInfoMap.containsKey(taskId)) {
			log.info("subTaskDone: main taskId({}) exists but no taskInfo in taskInfoMap", taskId);
			return;
		}
		TaskInfo taskInfo = taskInfoMap.get(taskId);
		taskInfo.getSubTaskList().remove(subTaskId); // SubTaskIdをMainTaskから除外

		if (taskInfo.getSubTaskList().size() == 0) {
			taskDoneBySetDeferredResult(taskId);
			return;
		}

		// 残るSubTaskがあれば、Logに情報を出す
		StringBuilder sb = new StringBuilder();
		sb.append(MessageFormatUtil.format("subTaskDone: task({}) has subTask(size={}). ", taskId, taskInfo.getSubTaskList().size()));

		taskInfo.getSubTaskList().forEach((sub) -> sb.append(MessageFormatUtil.format("{}, ", sub)));
		log.info(sb.toString());
	}

	/**
	 * Task終了の処理
	 *
	 * @param taskId 　終了するTaskのId
	 */
	private void taskDoneBySetDeferredResult(String taskId) {
		try {
			log.info("mainTaskDone, taskId:{}", taskId);

			TaskInfo taskInfo = taskInfoMap.get(taskId);
			if (taskInfo == null) {
				log.info("mainTaskDone: Task has removed");
				return;
			}

			if (!taskInfo.sendMsgDone) {
				log.info("mainTaskDone: Task is running");
				return;
			}
			taskInfoMap.remove(taskId);
			if (taskInfo.getDeferredResult() == null) {
				log.info("mainTaskDone: no DeferredResult");
				return;
			}

			ApiResult apiResult = taskInfo.getApiResult();
			if (taskInfo.getApiResult() == null) {
				log.info("mainTaskDone: apiResult is null and return success.");
				apiResult = new ApiResult(CommonUtil.API_RESULT_SUCCESS);
			}

			taskInfo.getDeferredResult().setResult(new ResponseEntity<ApiResult>(apiResult, HttpStatus.OK));
		} finally {
			printMetrics();
		}
	}

	/**
	 * 指定間隔で、Task QueueのMetrics情報を出力
	 */
	@Async("scheduleTask")
	@Scheduled(fixedDelay = 60000) // 60秒=1分でMetrics情報を出力
	public void metricsOutput() {
		printMetrics();
	}

	/**
	 * Task Queueで持っているMap情報を出力
	 */
	private void printMetrics() {
		
		StringBuilder sb = new StringBuilder(512);
		String lineSeparator = System.getProperty("line.separator");
		sb.append("■■■ printMetrics:" + lineSeparator);
		sb.append(MessageFormatUtil.format("TaskQueue Metrics. taskInfoMap(size={}), subTask2TaskMap(size={}). ", taskInfoMap.size(), subTask2TaskMap.size()));
		if (taskInfoMap.size() > 0) {
			sb.append("Task in taskInfoMap: ");
			taskInfoMap.forEach((key, value) -> {
				sb.append(MessageFormatUtil.format("{}{}", key, lineSeparator));
				if (value.getSubTaskList().size() > 0) {
					sb.append("SubTaskList: ");
					value.getSubTaskList().forEach((sub) -> sb.append(MessageFormatUtil.format("{}{}", sub, lineSeparator)));
				}
			});
		}
		if (subTask2TaskMap.size() > 0) {
			sb.append("Task in subTask2TaskMap: ");
			subTask2TaskMap.forEach((key, value) -> sb.append(MessageFormatUtil.format("subTaskId={}/mainTaskId={}, ", key, value)));
		}
		if (taskInfoMap.size() > 0 || subTask2TaskMap.size() > 0) {
			log.warn(sb.toString());
		}
	}

	/**
	 * taskIdに該当するTaskInfoを返す。<br>
	 * taskInfoMapに存在しない場合、TaskInfoを新規作成して返す
	 *
	 * @param taskId　Task Id
	 * @return Task Info
	 */
	private TaskInfo getTaskInfo(String taskId) {
		if (taskInfoMap.containsKey(taskId)) {
			return taskInfoMap.get(taskId);
		}

		TaskInfo taskInfo = new TaskInfo();
		taskInfoMap.put(taskId, taskInfo);
		return taskInfo;
	}
}
