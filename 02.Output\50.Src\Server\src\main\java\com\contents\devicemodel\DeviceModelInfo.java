package com.contents.devicemodel;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.contents.devicemodel.madiareceiver.MediaReceiverModel;
import com.contents.devicemodel.monitor.MonitorDeviceModel;
import com.contents.devicemodel.sound.SoundVolumeDeviceModel;
import com.contents.devicemodel.switcher.MatrixSwitcherDeviceModel;
import com.contents.devicemodel.videosource.VideoSourceDeviceModel;

public class DeviceModelInfo {

	/** モニターデバイスモデルリスト */
	private List<MonitorDeviceModel> monitorDeviceModelList;

	/** メディアレシーバーデバイスモデルリスト */
	private List<MediaReceiverModel> mediaReceiverModelList;

	/** 音量コントローラーデバイスモデルリスト */
	private List<SoundVolumeDeviceModel> soundVolumeDeviceModelList;

	/** マトリックススイッチャーデバイスモデルリスト */
	private List<MatrixSwitcherDeviceModel> matrixSwitcherDeviceModelList;

	/** ビデオソースデバイスモデルリスト */
	private List<VideoSourceDeviceModel> videoSourceDeviceModelList;

	public Iterator<DeviceModel> iterator() {
		
		List<DeviceModel> list = toList();
		return list.iterator();
	}
	
	public List<DeviceModel> toList() {
		
		List<DeviceModel> list = new ArrayList<DeviceModel>();
		list.addAll(monitorDeviceModelList);
		list.addAll(mediaReceiverModelList);
		list.addAll(soundVolumeDeviceModelList);
		list.addAll(matrixSwitcherDeviceModelList);
		list.addAll(videoSourceDeviceModelList);
		return list;
	}
	
	/**
	 * モニターデバイスモデルリストを取得します。
	 * @return モニターデバイスモデルリスト
	 */
	public List<MonitorDeviceModel> getMonitorDeviceModelList() {
	    return monitorDeviceModelList;
	}

	/**
	 * モニターデバイスモデルを追加します。
	 * @param monitorDeviceModel モニターデバイスモデル
	 */
	public void appendMonitorDeviceModel(MonitorDeviceModel monitorDeviceModel) {
		
		if (this.monitorDeviceModelList == null)
			this.monitorDeviceModelList = new ArrayList<MonitorDeviceModel>();
		
	    this.monitorDeviceModelList.add(monitorDeviceModel);
	}

	/**
	 * メディアレシーバーデバイスモデルリストを取得します。
	 * @return メディアレシーバーデバイスモデルリスト
	 */
	public List<MediaReceiverModel> getMediaReceiverModelList() {
	    return mediaReceiverModelList;
	}

	/**
	 * メディアレシーバーデバイスモデルを追加します。
	 * @param mediaReceiverModel メディアレシーバーデバイスモデル
	 */
	public void appendMediaReceiverModelList(MediaReceiverModel mediaReceiverModel) {
		
		if (this.mediaReceiverModelList == null)
			this.mediaReceiverModelList = new ArrayList<MediaReceiverModel>();
		
	    this.mediaReceiverModelList.add(mediaReceiverModel);
	}

	/**
	 * 音量コントローラーデバイスモデルリストを取得します。
	 * @return 音量コントローラーデバイスモデルリスト
	 */
	public List<SoundVolumeDeviceModel> getSoundVolumeDeviceModelList() {
	    return soundVolumeDeviceModelList;
	}

	/**
	 * 音量コントローラーデバイスモデルリストを追加します。
	 * @param soundVolumeDeviceModel 音量コントローラーデバイスモデル
	 */
	public void appendSoundVolumeDeviceModelList(SoundVolumeDeviceModel soundVolumeDeviceModel) {
		
		if (this.soundVolumeDeviceModelList == null)
			this.soundVolumeDeviceModelList = new ArrayList<SoundVolumeDeviceModel>();
		
	    this.soundVolumeDeviceModelList.add(soundVolumeDeviceModel);
	}

	/**
	 * マトリックススイッチャーデバイスモデルリストを取得します。
	 * @return マトリックススイッチャーデバイスモデルリスト
	 */
	public List<MatrixSwitcherDeviceModel> getMatrixSwitcherDeviceModelList() {
	    return matrixSwitcherDeviceModelList;
	}

	/**
	 * マトリックススイッチャーデバイスモデルを追加します。
	 * @param matrixSwitcherDeviceModel マトリックススイッチャーデバイスモデル
	 */
	public void appendMatrixSwitcherDeviceModelList(MatrixSwitcherDeviceModel matrixSwitcherDeviceModel) {
		
		if (this.matrixSwitcherDeviceModelList == null)
			this.matrixSwitcherDeviceModelList = new ArrayList<MatrixSwitcherDeviceModel>();
		
	    this.matrixSwitcherDeviceModelList.add(matrixSwitcherDeviceModel);
	}

	/**
	 * ビデオソースデバイスモデルリストを取得します。
	 * @return ビデオソースデバイスモデルリスト
	 */
	public List<VideoSourceDeviceModel> getVideoSourceDeviceModelList() {
	    return videoSourceDeviceModelList;
	}

	/**
	 * ビデオソースデバイスモデルを追加します。
	 * @param videoSourceDeviceModel ビデオソースデバイスモデル
	 */
	public void appendVideoSourceDeviceModelList(VideoSourceDeviceModel videoSourceDeviceModel) {
		
		if (this.videoSourceDeviceModelList == null)
			this.videoSourceDeviceModelList = new ArrayList<VideoSourceDeviceModel>();
		
	    this.videoSourceDeviceModelList.add(videoSourceDeviceModel);
	}
}
