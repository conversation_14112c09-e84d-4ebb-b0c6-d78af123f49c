2025-07-06 11:18:09 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-06 11:18:10 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 18,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 1,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "代",
            "text_color" : "#ffff00",
            "background_color" : "#00ffff"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : "代"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大字数",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中２",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中3",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中4",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中３１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中４１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "機材車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "急",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#faff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急東1",
            "text_color" : "#00ff00",
            "background_color" : "#000000"
          }, {
            "display_text" : "東1",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東２",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東広報車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ffee",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 2,
            "lighting_text_color" : "#ff3300",
            "lighting_background_color" : "#00ffaa",
            "blink_speed" : 3000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ffff00",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-06 15:01:36 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-06 15:01:37 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 18,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 2,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "最大文字",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 0,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          }, {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 0,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "軽い",
            "text_color" : "#0000ff",
            "background_color" : "#eeeeee"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 1,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-06 15:24:16 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:24:17 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 6,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 7,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:25:56 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /saveFrontError
2025-07-06 15:25:56 INFO  com.contents.configuration.RequestLoggingFilter - request. /saveFrontError
{
  "name" : "TypeError",
  "stack" : "TypeError: Cannot assign to read only property 'barTitle' of object '#<Object>'\n    at Weather (http://localhost:8088/contentapp/static/js/bundle.js:4724:18)\n    at renderWithHooks (http://localhost:8088/contentapp/static/js/bundle.js:39360:22)\n    at mountIndeterminateComponent (http://localhost:8088/contentapp/static/js/bundle.js:43025:17)\n    at beginWork (http://localhost:8088/contentapp/static/js/bundle.js:44493:20)\n    at beginWork$1 (http://localhost:8088/contentapp/static/js/bundle.js:50255:18)\n    at performUnitOfWork (http://localhost:8088/contentapp/static/js/bundle.js:49413:16)\n    at workLoopSync (http://localhost:8088/contentapp/static/js/bundle.js:49326:9)\n    at renderRootSync (http://localhost:8088/contentapp/static/js/bundle.js:49295:11)\n    at recoverFromConcurrentError (http://localhost:8088/contentapp/static/js/bundle.js:48703:24)\n    at performConcurrentWorkOnRoot (http://localhost:8088/contentapp/static/js/bundle.js:48604:26)",
  "componentStack" : "\n    at Weather (http://localhost:8088/contentapp/static/js/bundle.js:4724:26)\n    at SplitScreen (http://localhost:8088/contentapp/static/js/bundle.js:3723:88)\n    at div\n    at div\n    at div\n    at Home (http://localhost:8088/contentapp/static/js/bundle.js:6618:86)\n    at Routes (http://localhost:8088/contentapp/static/js/bundle.js:55306:5)\n    at header\n    at div\n    at App\n    at StompSessionProvider (http://localhost:8088/contentapp/static/js/bundle.js:55421:5)\n    at ErrorBoundary (http://localhost:8088/contentapp/static/js/bundle.js:52812:37)\n    at RootComponent (http://localhost:8088/contentapp/static/js/bundle.js:6389:81)\n    at Router (http://localhost:8088/contentapp/static/js/bundle.js:55239:15)\n    at BrowserRouter (http://localhost:8088/contentapp/static/js/bundle.js:54048:5)"
}
2025-07-06 15:27:56 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:27:56 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:38:39 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:38:40 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:43:21 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:43:21 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:48:10 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:48:10 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:50:39 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:50:39 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:53:58 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:53:58 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:55:53 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:55:53 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 15:59:52 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 15:59:52 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 8,
        "display_content_data" : {
          "source_no" : 2,
          "source_name" : "配備状況コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 9,
        "display_content_data" : {
          "source_no" : 3,
          "source_name" : "3 事案コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 12,
        "display_content_data" : {
          "source_no" : 4,
          "source_name" : "4 簡易事案コンテンツ(1-2サイズ)情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 13,
        "display_content_data" : {
          "source_no" : 5,
          "source_name" : "5 簡易事案コンテンツ(1-4サイズ)情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 2,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 3,
        "display_content_data" : {
          "source_no" : 7,
          "source_name" : "7 着信状況コンテンツA情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      }, {
        "display_details_split_no" : 6,
        "display_content_data" : {
          "source_no" : 8,
          "source_name" : "8 着信状況コンテンツB情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 7,
        "display_content_data" : {
          "source_no" : 9,
          "source_name" : "9 気象コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 8,
        "display_content_data" : {
          "source_no" : 2,
          "source_name" : "配備状況コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 9,
        "display_content_data" : {
          "source_no" : 3,
          "source_name" : "3 事案コンテンツ情報更新",
          "source_split_no" : 1,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 12,
        "display_content_data" : {
          "source_no" : 4,
          "source_name" : "4 簡易事案コンテンツ(1-2サイズ)情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 13,
        "display_content_data" : {
          "source_no" : 5,
          "source_name" : "5 簡易事案コンテンツ(1-4サイズ)情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 3,
      "display_details_split_data" : [ {
        "display_details_split_no" : 10,
        "display_content_data" : {
          "source_no" : 10,
          "source_name" : "10 総合度数コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 11,
        "display_content_data" : {
          "source_no" : 11,
          "source_name" : "11 予警報コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      }, {
        "display_details_split_no" : 14,
        "display_content_data" : {
          "source_no" : 12,
          "source_name" : "12 出退コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      }, {
        "display_details_split_no" : 15,
        "display_content_data" : {
          "source_no" : 13,
          "source_name" : "13 当番医コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      } ]
    } ]
  }, {
    "display_type" : "0",
    "display_no" : 1,
    "display_name" : "指令センター表示盤2",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 14,
          "source_name" : "14 予定コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 15,
          "source_name" : "15 引継事項コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      }, {
        "display_details_split_no" : 4,
        "display_content_data" : {
          "source_no" : 6,
          "source_name" : "6 時刻コンテンツ",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 5,
        "display_content_data" : {
          "source_no" : 17,
          "source_name" : "17 救急車稼働率コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 14,
          "source_name" : "14 予定コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 1,
        "display_content_data" : {
          "source_no" : 15,
          "source_name" : "15 引継事項コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      }, {
        "display_details_split_no" : 4,
        "display_content_data" : {
          "source_no" : 16,
          "source_name" : "16 デジタル無線コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      }, {
        "display_details_split_no" : 5,
        "display_content_data" : {
          "source_no" : 17,
          "source_name" : "17 救急車稼働率コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0
        }
      } ]
    }, {
      "display_split_no" : 3,
      "display_details_split_data" : [ {
        "display_details_split_no" : 10,
        "display_content_data" : {
          "source_no" : 8,
          "source_name" : "8 着信状況コンテンツB情報更新",
          "source_split_no" : 1,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 11,
        "display_content_data" : {
          "source_no" : 8,
          "source_name" : "8 着信状況コンテンツB情報更新",
          "source_split_no" : 1,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 14,
        "display_content_data" : {
          "source_no" : 8,
          "source_name" : "8 着信状況コンテンツB情報更新",
          "source_split_no" : 1,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      }, {
        "display_details_split_no" : 15,
        "display_content_data" : {
          "source_no" : 8,
          "source_name" : "8 着信状況コンテンツB情報更新",
          "source_split_no" : 1,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 16:09:48 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 16:09:48 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 16:10:34 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 16:10:34 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 16:15:18 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 16:15:18 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 16:23:44 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-06 16:23:44 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 18,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 1,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "代",
            "text_color" : "#ffff00",
            "background_color" : "#00ffff"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : "代"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大字数",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中２",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中3",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中4",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中３１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中４１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "機材車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "急",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#faff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急東1",
            "text_color" : "#00ff00",
            "background_color" : "#000000"
          }, {
            "display_text" : "東1",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東２",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東広報車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ffee",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 2,
            "lighting_text_color" : "#ff3300",
            "lighting_background_color" : "#00ffaa",
            "blink_speed" : 3000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ffff00",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-06 16:23:50 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-06 16:23:50 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 1,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 1,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "代",
            "text_color" : "#ffff00",
            "background_color" : "#00ffff"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : "代"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "car_name" : [ {
            "display_text" : "救急中１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大字数",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中２",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中3",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中4",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中３１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中４１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "機材車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          }, {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急東1",
            "text_color" : "#00ff00",
            "background_color" : "#000000"
          }, {
            "display_text" : "東1",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東２",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東広報車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 3000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-06 16:25:26 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-06 16:25:26 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 18,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 2,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "最大文字",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 0,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          }, {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 0,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "軽い",
            "text_color" : "#0000ff",
            "background_color" : "#eeeeee"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 1,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 2500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#00ffff",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "3"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-06 16:44:02 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 16:44:02 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-06 16:45:14 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-06 16:45:14 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
