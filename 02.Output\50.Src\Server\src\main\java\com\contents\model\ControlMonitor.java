package com.contents.model;

import java.util.Objects;
import com.contents.model.ControlMonitorDisplayControlData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ControlMonitor
 */
@Validated


public class ControlMonitor   {
  @JsonProperty("display_control_data")
  @Valid
  private List<ControlMonitorDisplayControlData> displayControlData = null;

  public ControlMonitor displayControlData(List<ControlMonitorDisplayControlData> displayControlData) {
    this.displayControlData = displayControlData;
    return this;
  }

  public ControlMonitor addDisplayControlDataItem(ControlMonitorDisplayControlData displayControlDataItem) {
    if (this.displayControlData == null) {
      this.displayControlData = new ArrayList<ControlMonitorDisplayControlData>();
    }
    this.displayControlData.add(displayControlDataItem);
    return this;
  }

  /**
   * Get displayControlData
   * @return displayControlData
   **/
  @Schema(description = "")
      @Valid
    public List<ControlMonitorDisplayControlData> getDisplayControlData() {
    return displayControlData;
  }

  public void setDisplayControlData(List<ControlMonitorDisplayControlData> displayControlData) {
    this.displayControlData = displayControlData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControlMonitor controlMonitor = (ControlMonitor) o;
    return Objects.equals(this.displayControlData, controlMonitor.displayControlData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayControlData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControlMonitor {\n");
    
    sb.append("    displayControlData: ").append(toIndentedString(displayControlData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
