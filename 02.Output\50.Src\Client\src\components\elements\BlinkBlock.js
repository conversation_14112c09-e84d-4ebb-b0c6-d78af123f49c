import React, { useRef, useEffect, useState } from 'react';
import styled, { keyframes, css } from 'styled-components';
import { getHexColor, getTextOrDisplayColor } from '../../utils/Util.js';
import { useTimeout } from 'ahooks';
import Cell from './Cell';
import PropTypes from 'prop-types';

const propTypes = {
    blink_setting: PropTypes.object,
    block: PropTypes.array.isRequired,
};

/**
 * 子供のコンポーネントを点滅実施する
 * 
 * @module BlinkBlock
 * @component
 *
 * @param {*} props
 * @return {*} 
 */
const BlinkBlock = (props) => {
    const [blinkTimeout, setBlinkTimeout] = useState(false);
    const [delay, setDelay] = useState(null);
    const lastTimer = useRef(null);

    useEffect(() => {
        setBlinkTimeout(false);

        //TimerをClearしてから
        if (lastTimer.current) {
            // 既に走っているタイマーを停止
            // lastTimer.current();
            // console.log('BlinkBlock clear timer before setDelay.');
            console.log('BlinkBlock timer is running.');
        }

        if (
            props?.blink_setting?.lighting_status === '3' &&
            props?.blink_setting?.blink_speed > 0 &&
            props?.blink_setting?.blink_time > 0
        ) {
            console.log('BlinkBlock setDelay. oldDelay: ' + delay + '. newDelay:' + props?.blink_setting?.blink_time * 1000);
            setDelay(props?.blink_setting?.blink_time * 1000);
        }
    }, [props?.blink_setting]);
    // 上記の配列中で、Delayを追加してはいけません。

    lastTimer.current = useTimeout(() => {
        setBlinkTimeout(true);
        setDelay(null);
        console.log('BlinkBlock useTimeout. With delay: ' + delay);
    }, delay);

    useEffect(() => {
        return () => {
            // BlinkBlock: clear timer when unmouting.
            if (lastTimer.current) {
                console.log('BlinkBlock clear timer when unmouting.');
                lastTimer.current();
            }
        };
    }, []);

    let lightingStatus = props?.blink_setting?.lighting_status;
    if (blinkTimeout) {
        console.log('BlinkBlock blinkTimeout true. lightingStatus: ' + lightingStatus);
        lightingStatus = '1';
    }
    if (
        props?.blink_setting?.blink_speed <= 0 ||
        props?.blink_setting?.blink_time <= 0
    ) {
        lightingStatus = '1';
    }
    return (
        <>
            {Array.isArray(props.block) &&
                props.block.map((item, index) => {
                    return (
                        <BlockRow
                            key={index}
                            {...item}
                            lightingStatus={lightingStatus}
                            blink_setting={props?.blink_setting}
                        />
                    );
                })}
        </>
    );
};

const animation = (props) => keyframes`
  0% {
    color: ${getTextOrDisplayColor(props)};
    background-color: ${getHexColor(props.background_color)};
  }
  100% {
    color: ${getLightingTextColor(props)};
    background-color: ${getHexColor(props.lighting_background_color)};
  }
`;

const animationRule = css`
  ${animation} ${(props) =>
        props.blink_speed / 1000}s linear infinite none alternate
`;

//点滅を実施するDivを作成
const BlinkHtmlTag = styled.div`
  animation: ${animationRule};
  animation-play-state: running;
  animation-fill-mode: forwards;
  position: relative;
`;

//点滅を実施しないDivを作成
const StaticHtmlTag = styled.div.attrs((props) => ({
    style: {
        color: getTextOrDisplayColor(props),
        backgroundColor: getHexColor(props.background_color),
    },
}))`
  position: relative;
`;

/**
 * 点滅対象の一行。lightingStatusに従って、点滅するかを決める
 * @param {*} props 
 * @returns 一行の点滅対象
 */
const BlockRow = (props) => {
    // props.showInfoにdisplay_colorが有っても、下記設定したtext_colorが優先される
    const divStyle = { ...props.showInfo, ...props.blink_setting };

    if (props.lightingStatus === '1') {
        divStyle.text_color = getTextOrDisplayColor(props.showInfo);
        divStyle.background_color = getHexColor(props.showInfo?.background_color);
    } else if (props.lightingStatus === '2') {
        divStyle.text_color = getLightingTextColor(props);
        divStyle.background_color = getHexColor(
            props.blink_setting?.lighting_background_color
        );
    }

    if (props.lightingStatus === '3') {
        return (
            <div className={props.className}>
                <BlinkHtmlTag {...divStyle}>
                    <Cell display_text={props.showInfo?.display_text} />
                </BlinkHtmlTag>
            </div>
        );
    } else {
        return (
            <div className={props.className}>
                <StaticHtmlTag {...divStyle}>
                    <Cell display_text={props.showInfo?.display_text} />
                </StaticHtmlTag>
            </div>
        );
    }
};

// APIのパラメータに違いをカバーして、色の値を返す
function getLightingTextColor(props) {
    const blink_setting = props?.blink_setting || props?.lighting_setting;

    // 下記Bug対応：
    // lighting_status を 3 (点滅…文字色/背景色 ⇔ 点灯時文字色/点灯時背景色で画面に表示) に設定した場合に lighting_text_color (点滅時の文字色) の設定値が反映されず点滅時に必ず白文字となる。
    // (lighting_background_color (点滅時の背景色) は設定値が反映されて、指定色で点滅する。こちらは問題なし。)
    if (blink_setting) {
        if (blink_setting?.lighting_text_color) {
            return getHexColor(blink_setting?.lighting_text_color);
        } else if (blink_setting?.lighting_display_color) {
            return getHexColor(blink_setting?.lighting_display_color);
        }
    } else {
        if (props?.lighting_text_color) {
            return getHexColor(props?.lighting_text_color);
        } else if (props?.lighting_display_color) {
            return getHexColor(props?.lighting_display_color);
        }

        return '';
    }
}

const space = ' ';

/**
 * BaseのObjectとチェックして、色と背景色をTargetObjectにCopy <br>
 * TargetObjectにTextがなければ、空文字を入れる
 * 
 * @param {*} targetObj 
 * @param {*} baseObj 
 * @returns Target Object
 */
export function checkBlinkInfo(targetObj, baseObj) {
    if (!targetObj) {
        targetObj = {};
    }
    if (targetObj?.display_text === space) {
        return targetObj;
    }

    if (!targetObj?.display_text) {
        targetObj.display_text = space;
        return targetObj;
    }

    if (!targetObj?.text_color && baseObj) {
        targetObj.text_color = baseObj.text_color;
    }
    if (!targetObj?.background_color && baseObj) {
        targetObj.background_color = baseObj.background_color;
    }

    return targetObj;
}

BlinkBlock.propTypes = propTypes;
export default BlinkBlock;
