package com.contents.external.matrix_sw.fdx_s;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

import javax.xml.bind.DatatypeConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.contents.common.MessageFormatUtil;
import com.contents.common.external.initial.Display;
import com.contents.common.external.initial.DisplaySplit;
import com.contents.common.external.initial.MatrixSw;
import com.contents.common.external.initial.VideoSource;
import com.contents.configuration.ExternalConfiguration;
import com.contents.manager.SocketClient;
import com.contents.manager.SocketManager;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;

/**
 * マトリックススイッチャーコントロール
 * */
@Slf4j
@Component
public class MatrixSwControl {
	/** 外部設定情報　*/
	private final ExternalConfiguration extConf;
	//private final SocketManager socketMgr;

	/* PINGタイムアウト設定値 (sec) */
	private static final int PING_TIMEOUT = 1;
	/* ソケット 各種タイムアウト設定値 (msec) */
	private static final int CONNECT_TIMEOUT = 1000;
	private static final int RECEIVE_TIMEOUT = 10 * 1000;
	/** 接続状態 true:接続, false:未接続 */
	private boolean connectStatus = false;

	@Autowired
	public MatrixSwControl(ExternalConfiguration extConf) {
		this.extConf = extConf;
		//healthCheck();
	}

	//    @Autowired
	//    public MatrixSwControl(ExternalConfiguration extConf, SocketManager socketMgr) {
	//        this.extConf = extConf;
	//        this.socketMgr = socketMgr;
	//        healthCheck();
	//    }

	/**
	 * 接続状態
	 * @return 接続状態
	 */
	public boolean isConnect() {
		return this.connectStatus;
	}

	/**
	 * 定期監視
	 */
	public void healthCheck() {
		log.info("Health check start.");

		// ping送信
		MatrixSw matSw = extConf.getExternal().getMatrixSw();
		Ping ping = new Ping(matSw.getIpAddress(), PING_TIMEOUT);
		if (ping.ping()) {
			this.connectStatus = sendAlarmStatus(matSw);
			log.info("ip={}, port={}, connectStatus={}", matSw.getIpAddress(), matSw.getPort(), this.connectStatus);

		} else {
			log.error("Health check ping error. ip={}, port={}", matSw.getIpAddress(), matSw.getPort());
			this.connectStatus = false;
		}

		log.info("Health check end.");
	}

	private boolean sendAlarmStatus(MatrixSw matSw) {
		boolean ret;
		// アラーム状態コマンド送信
		SocketClient socket = SocketManager.getSocketClient(matSw.getIpAddress(), matSw.getPort());
		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
			// アラーム状態
			byte[] message = "@GAA\r\n".getBytes();

			// 受信処理
			AtomicBoolean isError = new AtomicBoolean(false);
			Consumer<String> callback = (msg) -> {
				log.info("callback msg: {}", msg);
				String[] split = msg.trim().split(",");

				try {

					{
						String value = split[0];
						log.info("cmd: {}", value);
					}

					{
						String value = split[1];
						log.info("product: {}", value);
					}

					{
						String value = split[2];
						log.info("version: {}", value);
					}

					{
						String value = split[2];
						log.info("active data count: {}", value);
					}

				} catch (Exception e) {
					log.error("callback msg convert error.", e);
				}

				for (int i = 4; i < split.length; i++) {

					String value = split[i];

					try {
						if (Integer.parseInt(value) > 0) {
							log.error("Error in alarm condition. index={}", i);
							isError.set(true);
						}
					} catch (Exception e) {

						log.info("callback msg index: {}, value: {}", i, value);
						log.error("callback msg convert error.", e);
					}

				}
			};

			boolean sendResult = sendMessage(socket, message, callback);
			socket.close();

			ret = (sendResult && !(isError.get()));

		} else {
			log.error("Socket connection error.");
			ret = false;
		}
		return ret;
	}

	/**
	 * 映像切替
	 * @param sourceNo ソース番号
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean videoSwitch(int sourceNo, int displayNo, int displaySplitNo) {
		log.info("videoSwitch(): sourceNo={}, displayNo={}, displaySplitNo={}", sourceNo, displayNo, displaySplitNo);
		boolean ret;

		// マトリクススイッチャーの入力ポートを取得
		VideoSource videoSource = extConf.getVideoSource(sourceNo);

		if (videoSource != null) {
			// 画面分割(面)情報 取得
			DisplaySplit displaySplit = extConf.getDisplaySplit(extConf.getDisplay(displayNo), displaySplitNo);

			if (displaySplit != null) {
				// マトリクススイッチャーのソケットを用意
				MatrixSw matSw = extConf.getExternal().getMatrixSw();
				SocketClient socket = SocketManager.getSocketClient(matSw.getIpAddress(), matSw.getPort());
				if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
					// 入出力チャンネル切替
					byte[] message = MessageFormatUtil.format(
							"@SSW,{},{}\r\n",
							videoSource.getMatrixSwInputNo(),
							displaySplit.getMatrixSwVideoOutputNo()).getBytes();

					ret = sendMessage(socket, message, null);
					socket.close();
				} else {
					log.error("Socket connection error.");
					ret = false;
				}
			} else {
				// 画面分割(面)が見つからない
				log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
				ret = true;
			}
		} else {
			// ソース番号がマトリクススイッチャーの制御対象外
			log.info("Source number {} is not subject to switching with the matrix switcher.", sourceNo);
			ret = true;
		}

		return ret;
	}

	/**
	 * 音声切替
	 * @param sourceNo ソース番号
	 * @param displayNo 表示盤番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean audioSwitch(int sourceNo, int displayNo) {
		log.info("audioSwitch(): sourceNo={}, displayNo={}", sourceNo, displayNo);
		boolean ret;

		// マトリクススイッチャーの入力ポートを取得
		VideoSource videoSource = extConf.getVideoSource(sourceNo);

		if (videoSource != null) {
			// 表示盤情報 取得
			Display display = extConf.getDisplay(displayNo);

			if (display != null) {
				// マトリクススイッチャーのソケットを用意
				MatrixSw matSw = extConf.getExternal().getMatrixSw();
				
				log.info("★test3");
				
				SocketClient socket = SocketManager.getSocketClient(matSw.getIpAddress(), matSw.getPort());
				if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
					Integer matrixSwSoundOutputNo = display.getMatrixSwSoundOutputNo();
					matrixSwSoundOutputNo = + matrixSwSoundOutputNo + 300;
					Integer matrixSwInputNo = videoSource.getMatrixSwInputNo();
					// オーディオディエンベデッド
					String format = "@SAS,{},{}\r\n";
					
					//String message = String.format(format, matrixSwInputNo, matrixSwSoundOutputNo);
					
					String message = MessageFormatUtil.format(format, matrixSwSoundOutputNo, matrixSwInputNo);
					
					byte[] bytes = message.getBytes();

					ret = sendMessage(socket, bytes, null);
					socket.close();
				} else {
					log.error("Socket connection error.");
					ret = false;
				}		

			} else {
				// 表示盤番号が見つからない
				log.warn("Target display not found. displayNo={}", displayNo);
				ret = true;
			}
		} else {
			// ソース番号がマトリクススイッチャーの制御対象外
			log.info("Source number {} is not subject to switching with the matrix switcher.", sourceNo);
			ret = true;
		}

		return ret;
	}

	/**
	 * メッセージ送信
	 * @param socket Open済みのSocket
	 * @param message 送信メッセージ
	 * @param callback 受信コールバック
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendMessage(
			SocketClient socket, byte[] message, Consumer<String> callback) {
		String msg = new String(message);
		log.info("sendData(String): {}", msg);
		// 受信処理
		Consumer<byte[]> recvProc = (recvBytes) -> {
			String recvStr = new String(recvBytes);
			log.info("recvData(String): {}", recvStr);
			log.info("recvData: " + DatatypeConverter.printHexBinary(recvBytes));

			// エラーチェック
			if (recvStr.startsWith("@ERR")) {
				log.error("Matrix switcher error response.");
				String[] split = recvStr.split(",");
				if (split.length >= 2) {
					switch (split[1]) {
					case "1" -> log.warn("Parameter format or value is incorrect.");
					case "2" -> log.warn("Undefined command or bad command format.");
					case "3" -> log.warn("Command is currently unavailable.");
					case "4" -> log.warn("Failed to read EDID from sink device.");
					default -> log.warn("Unknown error.");
					}
				}
			} else {
				// 受信コールバックの呼び出し
				if (callback != null) {
					callback.accept(recvStr);
				}
			}
		};

		// 送信
		return socket.send(message, recvProc);
	}
}
