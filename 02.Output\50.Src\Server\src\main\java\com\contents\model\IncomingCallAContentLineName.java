package com.contents.model;

import java.util.Objects;
import com.contents.model.BlinkSetting;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * IncomingCallAContentLineName
 */
@Validated


public class IncomingCallAContentLineName   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("blink_setting")
  private BlinkSetting blinkSetting = null;

  public IncomingCallAContentLineName displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 回線名
   * @return displayText
   **/
  @Schema(description = "回線名")
  
  @Size(max=8)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public IncomingCallAContentLineName textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public IncomingCallAContentLineName backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public IncomingCallAContentLineName blinkSetting(BlinkSetting blinkSetting) {
    this.blinkSetting = blinkSetting;
    return this;
  }

  /**
   * Get blinkSetting
   * @return blinkSetting
   **/
  @Schema(description = "")
  
    @Valid
    public BlinkSetting getBlinkSetting() {
    return blinkSetting;
  }

  public void setBlinkSetting(BlinkSetting blinkSetting) {
    this.blinkSetting = blinkSetting;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IncomingCallAContentLineName incomingCallAContentLineName = (IncomingCallAContentLineName) o;
    return Objects.equals(this.displayText, incomingCallAContentLineName.displayText) &&
        Objects.equals(this.textColor, incomingCallAContentLineName.textColor) &&
        Objects.equals(this.backgroundColor, incomingCallAContentLineName.backgroundColor) &&
        Objects.equals(this.blinkSetting, incomingCallAContentLineName.blinkSetting);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor, backgroundColor, blinkSetting);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IncomingCallAContentLineName {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    blinkSetting: ").append(toIndentedString(blinkSetting)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
