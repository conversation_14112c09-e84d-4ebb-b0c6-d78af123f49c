package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * IncomingCallBContentBlinkSetting
 */
@Validated


public class IncomingCallBContentBlinkSetting   {
  @JsonProperty("blink_time")
  private Integer blinkTime = null;

  @JsonProperty("lighting_display_color")
  private String lightingDisplayColor = null;

  @JsonProperty("lighting_status")
  private String lightingStatus = null;

  @JsonProperty("blink_speed")
  private Integer blinkSpeed = null;

  public IncomingCallBContentBlinkSetting blinkTime(Integer blinkTime) {
    this.blinkTime = blinkTime;
    return this;
  }

  /**
   * 点滅表示を行う時間 秒数を設定する 0の場合は点滅しない 0以上の整数を設定する
   * minimum: 0
   * @return blinkTime
   **/
  @Schema(description = "点滅表示を行う時間 秒数を設定する 0の場合は点滅しない 0以上の整数を設定する")
  
  @Min(0)  public Integer getBlinkTime() {
    return blinkTime;
  }

  public void setBlinkTime(Integer blinkTime) {
    this.blinkTime = blinkTime;
  }

  public IncomingCallBContentBlinkSetting lightingDisplayColor(String lightingDisplayColor) {
    this.lightingDisplayColor = lightingDisplayColor;
    return this;
  }

  /**
   * Get lightingDisplayColor
   * @return lightingDisplayColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getLightingDisplayColor() {
    return lightingDisplayColor;
  }

  public void setLightingDisplayColor(String lightingDisplayColor) {
    this.lightingDisplayColor = lightingDisplayColor;
  }

  public IncomingCallBContentBlinkSetting lightingStatus(String lightingStatus) {
    this.lightingStatus = lightingStatus;
    return this;
  }

  /**
   * 該当箇所が点灯などをするかの設定 0~3を設定する
   * @return lightingStatus
   **/
  @Schema(description = "該当箇所が点灯などをするかの設定 0~3を設定する")
  
  @Pattern(regexp="^[0-3]{1}$")   public String getLightingStatus() {
    return lightingStatus;
  }

  public void setLightingStatus(String lightingStatus) {
    this.lightingStatus = lightingStatus;
  }

  public IncomingCallBContentBlinkSetting blinkSpeed(Integer blinkSpeed) {
    this.blinkSpeed = blinkSpeed;
    return this;
  }

  /**
   * 点滅の切り替えを行う速度 ミリ秒を設定する 0の場合は点滅しない 0以上の整数を設定する
   * minimum: 0
   * @return blinkSpeed
   **/
  @Schema(description = "点滅の切り替えを行う速度 ミリ秒を設定する 0の場合は点滅しない 0以上の整数を設定する")
  
  @Min(0)  public Integer getBlinkSpeed() {
    return blinkSpeed;
  }

  public void setBlinkSpeed(Integer blinkSpeed) {
    this.blinkSpeed = blinkSpeed;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IncomingCallBContentBlinkSetting incomingCallBContentBlinkSetting = (IncomingCallBContentBlinkSetting) o;
    return Objects.equals(this.blinkTime, incomingCallBContentBlinkSetting.blinkTime) &&
        Objects.equals(this.lightingDisplayColor, incomingCallBContentBlinkSetting.lightingDisplayColor) &&
        Objects.equals(this.lightingStatus, incomingCallBContentBlinkSetting.lightingStatus) &&
        Objects.equals(this.blinkSpeed, incomingCallBContentBlinkSetting.blinkSpeed);
  }

  @Override
  public int hashCode() {
    return Objects.hash(blinkTime, lightingDisplayColor, lightingStatus, blinkSpeed);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IncomingCallBContentBlinkSetting {\n");
    
    sb.append("    blinkTime: ").append(toIndentedString(blinkTime)).append("\n");
    sb.append("    lightingDisplayColor: ").append(toIndentedString(lightingDisplayColor)).append("\n");
    sb.append("    lightingStatus: ").append(toIndentedString(lightingStatus)).append("\n");
    sb.append("    blinkSpeed: ").append(toIndentedString(blinkSpeed)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
