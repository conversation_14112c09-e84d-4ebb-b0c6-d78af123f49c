package com.contents.common.bean;


/**
 * Source No仕様。
 1 車両コンテンツ情報更新
 2 配備状況コンテンツ情報更新
 3 事案コンテンツ情報更新
 4 簡易事案コンテンツ(1-2サイズ)情報更新
 5 簡易事案コンテンツ(1-4サイズ)情報更新
 6 時刻コンテンツ
 7 着信状況コンテンツA情報更新
 8 着信状況コンテンツB情報更新
 9 気象コンテンツ情報更新
 10 総合度数コンテンツ情報更新
 11 予警報コンテンツ情報更新
 12 出退コンテンツ情報更新
 13 当番医コンテンツ情報更新
 14 予定コンテンツ情報更新
 15 引継事項コンテンツ情報更新
 16 デジタル無線コンテンツ情報更新
 17 救急車稼働率コンテンツ情報更新
 18 拡張車両コンテンツ情報更新（50行表示）
 */
public enum SourceNo {

    /**
     * 現在時刻コンテンツ
     */
    Now(6);

    final int sourceNo;
    SourceNo(int i) {
        this.sourceNo = i;
    }

    public int getSourceNo() {
        return this.sourceNo;
    }
}
