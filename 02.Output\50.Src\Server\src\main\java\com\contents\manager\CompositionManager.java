package com.contents.manager;

import com.contents.common.SpringContextUtil;
import com.contents.composition.CrossStandardComposition;
import com.contents.composition.InitialConceptComposition;
import com.contents.composition.MachineComposition;
import com.contents.properties.FireCommandSystemProperties;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CompositionManager {

	private static CompositionManager _self = null;
	
	private MachineComposition machineComposition;
	
	private static void init() {
		
		try {
			
			if (_self != null)
				return;
			
			_self = new CompositionManager();

			boolean flag = true;
			
			if (flag) {
				SpringContextUtil.registerBean(CrossStandardComposition.class);
				_self.machineComposition = SpringContextUtil.getBean(CrossStandardComposition.class);				
			} else {
				SpringContextUtil.registerBean(InitialConceptComposition.class);
				_self.machineComposition = SpringContextUtil.getBean(InitialConceptComposition.class);				
			}
			



			
			FireCommandSystemProperties properties = SpringContextUtil.getBean(FireCommandSystemProperties.class);
			
			
			
			return;
			
		} catch (Exception e) {
			log.error("InitialConceptComposition class registerBean", e);
			throw e;
		}
	}
	
	public static MachineComposition loadComposition() {
		init();
		return _self.machineComposition;
	}
	
}
