package com.contents.model;

import java.util.Objects;
import com.contents.model.HandoverContentHandoverListing;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * HandoverContent
 */
@Validated


public class HandoverContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("handover_listing")
  @Valid
  private List<HandoverContentHandoverListing> handoverListing = null;

  public HandoverContent handoverListing(List<HandoverContentHandoverListing> handoverListing) {
    this.handoverListing = handoverListing;
    return this;
  }

  public HandoverContent addHandoverListingItem(HandoverContentHandoverListing handoverListingItem) {
    if (this.handoverListing == null) {
      this.handoverListing = new ArrayList<HandoverContentHandoverListing>();
    }
    this.handoverListing.add(handoverListingItem);
    return this;
  }

  /**
   * Get handoverListing
   * @return handoverListing
   **/
  @Schema(description = "")
      @Valid
  @Size(max=6)   public List<HandoverContentHandoverListing> getHandoverListing() {
    return handoverListing;
  }

  public void setHandoverListing(List<HandoverContentHandoverListing> handoverListing) {
    this.handoverListing = handoverListing;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandoverContent handoverContent = (HandoverContent) o;
    return Objects.equals(this.handoverListing, handoverContent.handoverListing);
  }

  @Override
  public int hashCode() {
    return Objects.hash(handoverListing);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandoverContent {\n");
    
    sb.append("    handoverListing: ").append(toIndentedString(handoverListing)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
