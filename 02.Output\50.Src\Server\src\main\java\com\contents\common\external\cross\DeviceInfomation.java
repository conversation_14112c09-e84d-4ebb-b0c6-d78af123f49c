package com.contents.common.external.cross;

import java.util.List;

public class DeviceInfomation {

	/** ディスプレイデバイスリスト */
	private List<DisplayDevice> displayList;
	
	/** メディアレシーバーリスト */
	private List<MediaReceiverDevice> mediaReceiverList;

	/** 音量コントローラーリスト */
	private List<VolumeControllerDevice> volumeControllerList;
	
	/** マトリックススイッチャーリスト */
	private List<MatrixSwitcherDevice> matrixSwitcherList;
	
	/**
	 * ディスプレイデバイスリストを取得します。
	 * @return ディスプレイデバイスリスト
	 */
	public List<DisplayDevice> getDisplayList() {
	    return displayList;
	}

	/**
	 * ディスプレイデバイスリストを設定します。
	 * @param displayList ディスプレイデバイスリスト
	 */
	public void setDisplayList(List<DisplayDevice> displayList) {
	    this.displayList = displayList;
	}

	/**
	 * メディアレシーバーリストを取得します。
	 * @return メディアレシーバーリスト
	 */
	public List<MediaReceiverDevice> getMediaReceiverList() {
	    return mediaReceiverList;
	}

	/**
	 * メディアレシーバーリストを設定します。
	 * @param mediaReceiverList メディアレシーバーリスト
	 */
	public void setMediaReceiverList(List<MediaReceiverDevice> mediaReceiverList) {
	    this.mediaReceiverList = mediaReceiverList;
	}

	/**
	 * 音量コントローラーリストを取得します。
	 * @return 音量コントローラーリスト
	 */
	public List<VolumeControllerDevice> getVolumeControllerList() {
	    return volumeControllerList;
	}

	/**
	 * 音量コントローラーリストを設定します。
	 * @param volumeControllerList 音量コントローラーリスト
	 */
	public void setVolumeControllerList(List<VolumeControllerDevice> volumeControllerList) {
	    this.volumeControllerList = volumeControllerList;
	}

	/**
	 * マトリックススイッチャーリストを取得します。
	 * @return マトリックススイッチャーリスト
	 */
	public List<MatrixSwitcherDevice> getMatrixSwitcherList() {
	    return matrixSwitcherList;
	}

	/**
	 * マトリックススイッチャーリストを設定します。
	 * @param matrixSwitcherList マトリックススイッチャーリスト
	 */
	public void setMatrixSwitcherList(List<MatrixSwitcherDevice> matrixSwitcherList) {
	    this.matrixSwitcherList = matrixSwitcherList;
	}
}
