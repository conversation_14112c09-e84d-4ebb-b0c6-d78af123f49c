package com.contents.common.aspect;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Arrays;

import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.contents.common.MessageFormatUtil;


/**
 * Log用
 */
@Component
@Aspect
@Order(1)
public class LogAdvice {

    /**
     *
     */
    @Pointcut("@within(org.springframework.stereotype.Controller)")
    public void controller() {
    }

    /**
     *
     */
//    @Pointcut("@within(org.springframework.stereotype.Service)")
//    public void service() {
//    }

    /**
     *
     * @param pjp ジョイントポイント
     * @return 戻り値
     * @throws Throwable 例外
     */
    @Around("controller()")
    public Object view(final ProceedingJoinPoint pjp) throws Throwable {
        final RequestAttributes attrs = RequestContextHolder.getRequestAttributes();

        if (attrs == null) {
            return null;
        }
        final HttpSession session =
                (HttpSession)attrs.resolveReference(RequestAttributes.REFERENCE_SESSION);

        final Object retVal;
        try {
			String userId = StringUtils.EMPTY;
			if (session != null && session.getAttribute("userId") != null) {
				userId = session.getAttribute("userId").toString();
			}
			if (StringUtils.isBlank(userId)) {
				userId = "no-login";
			}

			MDC.put("userId", userId);
			if (session != null) {
				MDC.put("sessionId", session.getId());
			} else {
				MDC.put("sessionId", "no-session");
			}
			MDC.put("requestHash", RandomStringUtils.randomAlphabetic(10));
			retVal = trace(pjp);
        } finally {
            MDC.remove("userId");
            MDC.remove("requestHash");
            MDC.remove("sessionId");
        }
        return retVal;
    }

    /**
     *
     * @param pjp ジョイントポイント
     * @return 戻り値
     * @throws Throwable 例外
     */
//    @Around("service()")
//    public Object model(final ProceedingJoinPoint pjp) throws Throwable {
//        final Object retVal = trace(pjp);
//        return retVal;
//    }

    /**
     * @param pjp PJP
     * @return 処理 結果
     * @throws Throwable Throwable
     */
    private Object trace(final ProceedingJoinPoint pjp) throws Throwable {
        final Signature sig = pjp.getSignature();
        final String name = sig.getDeclaringType().getName() + "#" + sig.getName();
        final Object[] args = pjp.getArgs();
        final String param;
        if (args.length == 1) {
            param = String.valueOf(args[0]);
        } else {
            param = (String)Arrays.stream(args).reduce((o1, o2) -> o1 + ", " + o2).orElse(StringUtils.EMPTY);
        }
        Logger.SYS.info(MessageFormatUtil.format("BEGIN {}({})", name, param));
        try {
            final Object retVal = pjp.proceed();
            Logger.SYS.info(MessageFormatUtil.format("END {}({}) : {}", name, param, retVal));
            return retVal;
        } catch (final Throwable t) {
            /* 通常はログ出力されると思うのでDEBUGレベルで出力、try-catchで潰している場合の対策 */
            Logger.SYS.debug(Logger.getStackTrace(t));
            throw t;
        }
    }

    /**
     * Logger
     *
     */
    public enum Logger {

        /** アプリケーションのロガー. */
        APP("app"), SYS("sys");

        private final String name;

        /**
         * @param name Name
         */
        Logger(final String name) {
            this.name = name;
        }

        /**
         * @return Logger
         */
        private org.slf4j.Logger getLog() {
            return LoggerFactory.getLogger(name);
        }

        /**
         * DEBUGレベルでのログ出力.
         *
         * @param message 出力メッセージ
         */
        public void debug(final String message) {
            getLog().debug(message);
        }

        /**
         * INFOレベルでのログ出力.
         *
         * @param message 出力メッセージ
         */
        public void info(final String message) {
            getLog().info(message);
        }

        /**
         * WARNレベルでのログ出力.
         *
         * @param message 出力メッセージ
         */
        public void warn(final String message) {
            getLog().warn(message);
        }

        /**
         * ERRORレベルでのログ出力
         *
         * @param t 例外
         */
        public void error(final Throwable t) {
            getLog().error(getStackTrace(t));
        }

        /**
         * ERRORレベルでのログ出力.
         *
         * @param message 出力メッセージ
         */
        public void error(final String message) {
            getLog().error(message);
        }

        /**
         * ERRORレベルでのログ出力.
         *
         * @param message 出力メッセージ
         * @param t 例外
         */
        public void error(final String message, final Throwable t) {
            getLog().error(message, t);
        }

        /**
         * スタックトレースを文字列にして返す。
         *
         * @param t 元の例外
         * @return スタックトレース
         */
        public static String getStackTrace(final Throwable t) {
            final StringWriter sw = new StringWriter();
            t.printStackTrace(new PrintWriter(sw));
            return sw.toString();
        }
    }
}
