package com.contents.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import com.contents.common.CommonUtil;
import com.contents.common.bean.Result;
import com.contents.common.db.History;
import com.contents.manager.TaskQueue;
import com.contents.model.ApiResult;
import com.contents.model.ControlSound;
import com.contents.service.SystemService;
import com.contents.service.UtilityService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;

@RestController
public class ControlSoundApiController implements ControlSoundApi {

    private static final Logger log = LoggerFactory.getLogger(ControlSoundApiController.class);

    private final ObjectMapper objectMapper;

    private final HttpServletRequest request;

    private final SystemService systemService;

    private final UtilityService utilityService;

    private final TaskQueue taskQueue;

    @Autowired
    public ControlSoundApiController(ObjectMapper objectMapper, HttpServletRequest request, SystemService systemService, UtilityService utilityService, TaskQueue taskQueue) {
        this.objectMapper = objectMapper;
        this.request = request;
        this.systemService = systemService;
        this.utilityService = utilityService;
        this.taskQueue = taskQueue;
    }

    public DeferredResult<ResponseEntity<ApiResult>> controlSoundPost(@Parameter(in = ParameterIn.DEFAULT, description = "", required=true, schema=@Schema()) @Valid @RequestBody ControlSound body) {
        String taskId;
        synchronized (taskQueue) {
            taskId = taskQueue.getTaskId("controlSoundPost");
        }
        
        History history = null;
        // 1, 履歴を保存
        try {
            history = systemService.saveApiBody2History(request, objectMapper.writeValueAsString(body));
        } catch (JsonProcessingException e) {
            log.error("controlSoundPost: save history error");
        }

        DeferredResult<ResponseEntity<ApiResult>> deferredResult
                = utilityService.getResponseEntityDeferredResult(request.getRequestURI(), taskId, history, body);

        String accept = request.getHeader("Accept");
        if (accept == null || !accept.contains("application/json")) {
            deferredResult.setResult(utilityService.getApiResultNotJson(request.getRequestURI()));
            return deferredResult;
        }
        if (body.getDisplaySoundRingData() == null) {
            deferredResult.setResult(utilityService.getApiResultNoApiData(request.getRequestURI()));
            return deferredResult;
        }

        // APIを一つのTaskとして、IDを付けて管理する
        ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_SUCCESS);
        synchronized (taskQueue) {
            taskQueue.putDeferredResult(taskId, deferredResult);
            taskQueue.putApiResult(taskId, apiResult);
        }

        try {
            // 1. 他の制御
            Result innerResult = systemService.executeSoundControl(body.getDisplaySoundRingData(), taskId);
            if (innerResult.getErrMsgList().size() > 0) {
                log.error("controlSoundPost: {}", innerResult.getMsg());
                apiResult.setMsg(innerResult.getMsg());
                apiResult.setResult(CommonUtil.API_RESULT_FAIL);
            }
            synchronized (taskQueue) {
                taskQueue.taskSendMsgDone(taskId);
            }
            return deferredResult;
        } catch (Exception e) {
            log.error("Exception in Task: {}", taskId);
            deferredResult.setResult(utilityService.getApiResultInternalError(request.getRequestURI(), e));
            return deferredResult;
        }
    }

}
