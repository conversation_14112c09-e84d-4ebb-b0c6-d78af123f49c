package com.contents.api;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;

import com.contents.common.MessageFormatUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class UtilityApiController implements UtilityApi {

	@Override
	public String health() {
		return "SECCESS";
	}

	@Value("${info.version}")
	private String appVersion;

	//	@Override
	//	public String version() {
	//
	//		try (InputStream input = this.getClass().getClassLoader().getResourceAsStream("application.properties")) {
	//            Properties prop = new Properties();
	//            if (input != null) {
	//                prop.load(input);
	//                String version = prop.getProperty("app.version");
	//                log.info("App version: {}", version);
	//                return MessageFormatUtil.format("SERVER API VERSION: {}", version);
	//            } else {
	//            	log.warn("Version file not found."); 
	//            }
	//        } catch (Exception ex) {
	//            ex.printStackTrace();
	//        }
	//		return "UNKOWN VERSION";
	//	}

	@Override
	public String version() {
		try {
			String version = appVersion;
			log.info("App version: {}", version);
			return MessageFormatUtil.format("SERVER API VERSION: {}", version);
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return "UNKNOWN VERSION";
	}

}
