package com.contents.common.external.initial;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報) 画面分割(面)
 */
public class DisplaySplit {

    @JsonProperty("display_split_no")
    private Integer displaySplitNo;
    
    @JsonProperty("ip_address")
    private String ipAddress;
    
    @JsonProperty("port")
    private Integer port;
    
    @JsonProperty("matrix_sw_video_output_no")
    private Integer matrixSwVideoOutputNo;
    
    @JsonProperty("hdmi_input1")
    private String hdmiInput1;
    
    @JsonProperty("hdmi_input2")
    private String hdmiInput2;

    @JsonProperty("display_split_no")
    public Integer getDisplaySplitNo() {
        return displaySplitNo;
    }

    @JsonProperty("display_split_no")
    public void setDisplaySplitNo(Integer displaySplitNo) {
        this.displaySplitNo = displaySplitNo;
    }

    @JsonProperty("ip_address")
    public String getIpAddress() {
        return ipAddress;
    }

    @JsonProperty("ip_address")
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    @JsonProperty("port")
    public Integer getPort() {
        return port;
    }

    @JsonProperty("port")
    public void setPort(Integer port) {
        this.port = port;
    }

    @JsonProperty("matrix_sw_video_output_no")
    public Integer getMatrixSwVideoOutputNo() {
        return matrixSwVideoOutputNo;
    }

    @JsonProperty("matrix_sw_video_output_no")
    public void setMatrixSwVideoOutputNo(Integer matrixSwVideoOutputNo) {
        this.matrixSwVideoOutputNo = matrixSwVideoOutputNo;
    }

    @JsonProperty("hdmi_input1")
    public String getHdmiInput1() {
        return hdmiInput1;
    }

    @JsonProperty("hdmi_input1")
    public void setHdmiInput1(String hdmiInput1) {
        this.hdmiInput1 = hdmiInput1;
    }

    @JsonProperty("hdmi_input2")
    public String getHdmiInput2() {
        return hdmiInput2;
    }

    @JsonProperty("hdmi_input2")
    public void setHdmiInput2(String hdmiInput2) {
        this.hdmiInput2 = hdmiInput2;
    }

}