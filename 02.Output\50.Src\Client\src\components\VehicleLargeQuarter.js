import React from 'react';
import CellBox from './elements/CellBox';
import { getCellFace, isValidSource } from '../utils/Util.js';
import BlinkBlock, { checkBlinkInfo } from './elements/BlinkBlock';

/**
 * 拡張車両コンテンツ（四等分表示対応・50行データ）<br>
 * propsは、「3.3車両コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 * CustomVehicle.jsと同様のprops.items構造を使用
 * @module VehicleLargeQuarter
 * @component
 * @param {*} props
 * @return {*} 表示データ
 */
const VehicleLargeQuarter = (props) => {

    if (!isValidSource(props))
        return;

    if (!props.items)
        return;

    const showDelopy = props.is_deployment === 1;
    const columnPosition = props.column_position;
    const gridClass = getGridClass(props);

    return (
        <>
            {isValidSource(props) && (
                <div className={`grid ${gridClass}`}>
                    {props.items.map((item, index) => {
                        return (
                            <Station
                                key={index}
                                {...item}
                                showDelopy={showDelopy}
                                columnPosition={columnPosition}
                            />
                        );
                    })}
                </div>
            )}
        </>
    );
};

/**
 * ソース表示パターンによってグリッドのクラス定義を取得する
 * VehicleLarge用に調整（12行表示、48px字体）
 * @param {*} props
 * @returns クラス定義
 */
const getGridClass = (props) => {

    if (!(props.sourceDispPattern === 1))
        // 四等分模式：VehicleLarge.jsと完全一致の样式
        return 'grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.4rem] leading-[1] gap-x-[2.25rem] gap-y-[0.3rem]';

    if (props.column_position == 'left')
        return 'grid-cols-1 grid-rows-12 grid-flow-col text-[40.3px] leading-[1] gap-y-[4.8px] pr-[16px]';

    if (props.column_position == 'right')
        return 'grid-cols-1 grid-rows-12 grid-flow-col text-[40.3px] leading-[1] gap-y-[4.8px] pl-[16px]';

    return 'grid-cols-1 grid-rows-12 grid-flow-col text-[40.3px] leading-[1] gap-x-[36px] gap-y-[4.8px]';
};

/**
 * 署所(部隊)名または車両種別単位のデータを表示
 * @param {*} entity
 * @returns 表示データ
 */
const Station = (entity) => {
    let gridCol;
    let subTitleSpan = 'col-span-full';
    if (entity.showDelopy) {
        gridCol = 'grid-cols-quarter-extended-vehicle-deploy';
    } else {
        gridCol = 'grid-cols-quarter-extended-vehicle-nodeploy';
    }
    const subTitleProp = getCellFace(
        entity.title,
        `${subTitleSpan} flex flex-col items-center`
    );

    return (
        <>
            {entity.title && (
                <div className={`grid ${gridCol}`}>
                    <CellBox {...subTitleProp}>
                        <span>{entity.title.display_text}</span>
                    </CellBox>
                </div>
            )}
            {!entity.title && (
                <VehicleDetailRow {...entity} />
            )}
        </>
    );
};

/**
 * 車両コンテンツの一行データ
 * @param {*} entity
 * @returns 表示データ
 */
const VehicleDetailRow = (entity) => {

    let showInfoDeployment;
    let showInfoCarName;
    let showInfoTownName;
    let showInfoDisasterType;
    let showInfoAvmDynamicState;

    if (entity.showDelopy && entity.deployment)
        showInfoDeployment = entity.deployment;

    if (entity.car_name)
        showInfoCarName = entity.car_name;

    if (entity.town_name)
        showInfoTownName = entity.town_name;

    if (entity.disaster_type)
        showInfoDisasterType = entity.disaster_type;

    if (entity.avm_dynamic_state)
        showInfoAvmDynamicState = entity.avm_dynamic_state;

    let status = entity.lighting_setting ? entity.lighting_setting.lighting_status : 1;

    //該当Indexのデータがない場合、該当Indexのcar_nameを使って色等のDefaultデータをセットアップ
    let baseObj = [entity.car_name, entity.town_name, entity.disaster_type, entity.avm_dynamic_state, entity.deployment].find(item => item);

    showInfoDeployment = checkBlinkInfo(showInfoDeployment, baseObj);
    showInfoCarName = checkBlinkInfo(showInfoCarName, baseObj);
    showInfoTownName = checkBlinkInfo(showInfoTownName, baseObj);
    showInfoDisasterType = checkBlinkInfo(showInfoDisasterType, baseObj);
    showInfoAvmDynamicState = checkBlinkInfo(showInfoAvmDynamicState, baseObj);

    let showInfoSeperator0 = { ...showInfoDeployment };
    let showInfoSeperator1 = { ...showInfoDeployment };
    let showInfoSeperator2 = { ...showInfoCarName };
    let showInfoSeperator3 = { ...showInfoTownName };
    let showInfoSeperator4 = { ...showInfoDisasterType };
    let showInfoSeperator5 = { ...showInfoAvmDynamicState };

    showInfoSeperator0.display_text = ' ';
    showInfoSeperator1.display_text = ' ';
    showInfoSeperator2.display_text = ' ';
    showInfoSeperator3.display_text = ' ';
    showInfoSeperator4.display_text = ' ';
    showInfoSeperator5.display_text = ' ';

    // Status=3 点滅以外、背景色を表示する必要がないので、クリアする
    if (status !== 3) {
        showInfoSeperator0.background_color = undefined;
        showInfoSeperator1.background_color = undefined;
        showInfoSeperator2.background_color = undefined;
        showInfoSeperator3.background_color = undefined;
        showInfoSeperator4.background_color = undefined;
        showInfoSeperator5.background_color = undefined;
    }

    let gridCol;
    let showBlock = [];
    if (entity.showDelopy) {
        gridCol = 'grid-cols-quarter-extended-vehicle-deploy';

        if (entity.columnPosition == 'left') {
            showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });
        }

        showBlock.push({
            showInfo: showInfoDeployment,
            className: 'col-span-1 col-start-2',
        });
        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoCarName,
            className: 'col-span-4 col-start-4',
        });
        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoTownName,
            className: 'col-span-6 col-start-9',
        });
        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoDisasterType,
            className: 'col-span-2 col-start-16',
        });
        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoAvmDynamicState,
            className: 'col-span-2 col-start-19',
        });

        if (entity.columnPosition == 'right') {
            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });
        }

    } else {
        gridCol = 'grid-cols-quarter-extended-vehicle-nodeploy';

        showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoCarName,
            className: 'col-span-4 col-start-2',
        });
        showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoTownName,
            className: 'col-span-6 col-start-7',
        });
        showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoDisasterType,
            className: 'col-span-2 col-start-14',
        });
        showBlock.push({ showInfo: showInfoSeperator4, className: 'col-span-1' });
        showBlock.push({
            showInfo: showInfoAvmDynamicState,
            className: 'col-span-2 col-start-17',
        });
        if (entity.columnPosition == 'right') {
            showBlock.push({ showInfo: showInfoSeperator5, className: 'col-span-1' });
        }

    }

    return (
        <>
            {/* 一行表示する為、隙間は、前の表示のSpanに付ける。結果的に、前の表示情報のSpanは、仕様より＋1 or 2になる */}
            {entity.showDelopy && (
                <div className={`grid ${gridCol}`}>
                    <BlinkBlock
                        block={showBlock}
                        blink_setting={entity.lighting_setting}
                    />
                </div>
            )}
            {!entity.showDelopy && (
                <div className={`grid ${gridCol}`}>
                    <BlinkBlock
                        block={showBlock}
                        blink_setting={entity.lighting_setting}
                    />
                </div>
            )}
        </>
    );
};

export default VehicleLargeQuarter;
