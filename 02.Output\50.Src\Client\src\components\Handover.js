import React from 'react';
import Title from './elements/Title';
import TextScroll from './elements/TextScroll';
import CellBox from './elements/CellBox';
import { getCellFace, isValidSource } from '../utils/Util.js';

/**
 * 引継事項コンテンツ<br>
 * propsは、「3.16引継事項コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 * フリーテキストの引継事項の内容を表示する。（最大6件）
 * 引継事項が長い場合は流動表示することとし、文字色は任意に設定可能とする。
 *
 * @module HandOver
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const HandOver = (props) => {
  const MAX_ROW = 6;
  return (
    <div className="text-7xl">
      <Title title={'引継事項'} />
      <div className="border-transparent border-x-[1rem] grid grid-cols-1 leading-[1] gap-y-[3.2rem] mt-[3rem]">
        {isValidSource(props) &&
          props.handover_listing?.map((item, index) => {
            //フリーテキストの引継事項の内容を表示する。（最大6件）
            if (index >= MAX_ROW) return undefined;

            let cellInfo = getCellFace(item);
            let cellInfoPropFrame = {
              className: cellInfo.className,
              text_color: cellInfo.text_color,
            };
            return (
              <CellBox {...cellInfoPropFrame} key={index}>
                <TextScroll
                  content={item.display_text}
                  background_color={cellInfo.background_color}
                />
              </CellBox>
            );
          })}
      </div>
    </div>
  );
};

export default HandOver;
