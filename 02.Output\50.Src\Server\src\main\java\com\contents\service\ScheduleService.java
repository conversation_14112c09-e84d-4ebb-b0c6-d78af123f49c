package com.contents.service;

import static com.contents.common.CommonUtil.*;

import java.time.LocalDateTime;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.bean.ContentInfo;
import com.contents.common.bean.ScreenInfo;
import com.contents.common.bean.TimeContent;
import com.contents.common.db.HistoryDao;
import com.contents.manager.SystemSettingManager;
import com.contents.manager.TaskQueue;
import com.contents.model.SyncTimeTime;

import lombok.Data;

/**
 * Schedule必要な処理をここで纏める
 */
@Component
@Data
@EnableScheduling
public class ScheduleService {
	private final SimpMessagingTemplate messagingTemplate;

	private final HistoryDao historyDao;

	private final TaskQueue taskQueue;

	//private Integer apiHistoryMaxKeepMonth;

	public ScheduleService(SimpMessagingTemplate messagingTemplate, HistoryDao historyDao, TaskQueue taskQueue) {
		this.messagingTemplate = messagingTemplate;
		this.historyDao = historyDao;
		this.taskQueue = taskQueue;
	}

	/**
	 * 内部で情報持ちのClass
	 */
	@Data
	private static class CurrentTimeMgtInfo {
		ContentInfo contentInfo;

		// 最後Browserに送った時間
		LocalDateTime lastSend2ClientTime;

		// 変化する時刻情報以外の共通設定
		SyncTimeTime timeContentSetting;
	}

	// Screenと表示情報のマッピング
	private Map<ScreenInfo, CurrentTimeMgtInfo> targetScreenList = new ConcurrentHashMap<>();

	/**
	 * 時刻コンテンツを発信する必要なScreen/Content/他の表示設定情報を登録する
	 *
	 * @param screenInfo　Screen関連情報
	 * @param contentInfo　コンテンツ関連情報
	 * @param timeContentSetting　時刻同期APIで受信した表示設定
	 */
	public void registerTimeContentScreenInfo(ScreenInfo screenInfo, ContentInfo contentInfo, SyncTimeTime timeContentSetting) {
		synchronized (targetScreenList) {
			CurrentTimeMgtInfo mgtInfo = new CurrentTimeMgtInfo();
			mgtInfo.setContentInfo(contentInfo);
			mgtInfo.setTimeContentSetting(timeContentSetting);

			targetScreenList.put(screenInfo, mgtInfo);

			//すぐにClientに通知
			sendCurrentTime2Client(screenInfo, mgtInfo);
		}
	}

	/**
	 * 登録したScreen情報をClearして、配信停止
	 * @param displayNo 表示盤番号
	 * @param splitNo　面番号
	 */
	public void clearTimeContentScreenInfo(Integer displayNo, Integer splitNo) {
		synchronized (targetScreenList) {
			for (ScreenInfo screen : targetScreenList.keySet()) {
				if (screen.getDisplayNo().equals(displayNo)
						&& screen.getSplitNo().equals(splitNo)) {
					targetScreenList.remove(screen);
				}
			}
		}
	}

	/**
	 * 登録したScreen情報をClearして、配信停止
	 * @param displayNo 表示盤番号
	 * @param splitNo　面番号
	 * @param detailSplitNo　面分割番号
	 */
	public void clearTimeContentScreenInfo(Integer displayNo, Integer splitNo, Integer detailSplitNo) {
		synchronized (targetScreenList) {
			for (ScreenInfo screen : targetScreenList.keySet()) {
				if (screen.getDisplayNo().equals(displayNo)
						&& screen.getSplitNo().equals(splitNo)
						&& screen.getDetailSplitNo().equals(detailSplitNo)) {
					targetScreenList.remove(screen);
				}
			}
		}
	}

	/**
	 * 1秒毎でチェックして、時刻が変わるタイミングで、時刻コンテンツをBrowser(Screen)に送る
	 */
	@Async("scheduleTask")
	@Scheduled(fixedRate = 1000) //1秒毎
	protected void checkIfSendTimeContent2Clients() {
		LocalDateTime now = LocalDateTime.now();

		//分を切り替えるタイミング、或いは、未通知の場合、Clientに通知
		for (Map.Entry<ScreenInfo, CurrentTimeMgtInfo> mgtInfoEntry : targetScreenList.entrySet()) {
			//0秒、或いは、前回の通知の分と現在の分が違う場合、Clientに通知
			if (now.getSecond() == 0) {
				sendCurrentTime2Client(mgtInfoEntry.getKey(), mgtInfoEntry.getValue());
				continue;
			}

			LocalDateTime truncatedToMinuteDate = mgtInfoEntry.getValue().getLastSend2ClientTime().truncatedTo(ChronoUnit.MINUTES);
			if (!now.truncatedTo(ChronoUnit.MINUTES).isEqual(truncatedToMinuteDate)) {
				sendCurrentTime2Client(mgtInfoEntry.getKey(), mgtInfoEntry.getValue());
			}
		}
	}

	/**
	 * 時刻コンテンツをBrowser(Screen)に送る
	 * @param screen Screen Info
	 * @param mgtInfo　該当Screenに該当するContent関連情報
	 */
	private void sendCurrentTime2Client(ScreenInfo screen, CurrentTimeMgtInfo mgtInfo) {
		LocalDateTime now = LocalDateTime.now();
		mgtInfo.setLastSend2ClientTime(now);

		TimeContent currentTime = getCurrentTimeContent(now, mgtInfo);
		if (currentTime == null)
			currentTime = getMyServerCurrentTimeContent(now, mgtInfo);
		mgtInfo.getContentInfo().setSourceData(currentTime);

		//WebSocketの受け側のID
		String wsEndpoint = CommonUtil.getWebSocketId(screen.getDisplayNo(), screen.getSplitNo(), screen.getDetailSplitNo());
		String prefix = MessageFormatUtil.format("sendContent2EndPoint({})_sourceNo_{}", wsEndpoint, mgtInfo.getContentInfo().getSourceNo());

		synchronized (taskQueue) {
			mgtInfo.getContentInfo().setId(taskQueue.getTaskId(prefix));
		}

		messagingTemplate.convertAndSendToUser(wsEndpoint, WS_CMD_SET_CONTENT, mgtInfo.getContentInfo());
	}

	/**
	 * 時刻コンテンツを作成して返す
	 * @param now 現在時刻
	 * @param mgtInfo　Contentの関連情報
	 * @return 時間コンテンツ
	 */
	private TimeContent getCurrentTimeContent(LocalDateTime now, CurrentTimeMgtInfo mgtInfo) {

		return null;

		//    	if (mgtInfo == null)
		//        	return null;
		//        
		//        ContentInfo contentInfo = mgtInfo.getContentInfo();
		//        
		//         contentInfo.getSourceData();
		//        
		//        
		//        
		//        TimeContent newTimeContent = new TimeContent();
		//        
		//        
		//        SyncTimeTime timeContentSetting = mgtInfo.getTimeContentSetting();
		//
		//        if (timeContentSetting != null) {
		//        	newTimeContent.setTextColor(mgtInfo.getTimeContentSetting().getTextColor());
		//        	newTimeContent.setBackgroundColor(mgtInfo.getTimeContentSetting().getBackgroundColor());
		//        }
		//        
		//        return newTimeContent;
	}
	/**
	 * 時刻コンテンツを作成して返す(自サーバ時刻)
	 * @param now 現在時刻
	 * @param mgtInfo　Contentの関連情報
	 * @return 時間コンテンツ
	 */
	private TimeContent getMyServerCurrentTimeContent(LocalDateTime now, CurrentTimeMgtInfo mgtInfo) {

		TimeContent currentSystemTimeContent = new TimeContent();
		currentSystemTimeContent.setYear(String.valueOf(now.getYear()));
		currentSystemTimeContent.setMonth(String.valueOf(now.getMonthValue()));
		currentSystemTimeContent.setDay(String.valueOf(now.getDayOfMonth()));

		currentSystemTimeContent.setWeekday(now.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.JAPANESE));

		currentSystemTimeContent.setHour(String.valueOf(now.getHour()));

		currentSystemTimeContent.setMinute(String.format("%02d", now.getMinute()));

		SyncTimeTime timeContentSetting = mgtInfo.getTimeContentSetting();

		if (timeContentSetting != null) {
			currentSystemTimeContent.setTextColor(mgtInfo.getTimeContentSetting().getTextColor());
			currentSystemTimeContent.setBackgroundColor(mgtInfo.getTimeContentSetting().getBackgroundColor());
		}

		return currentSystemTimeContent;
	}

	/**
	 * 午前3時でAPI履歴を削除する処理を実施
	 * 履歴の保存期限は、api.history.max.keep.monthの設定に従う
	 */
	@Async("scheduleTask")
	@Scheduled(cron = "0 0 3 * * ?")
	protected void deleteApiHistory() {
		Integer apiHistoryMaxKeepMonth = SystemSettingManager.getSystemSetting().getApiHistoryMaxKeepMonth();
		Integer deleteCount = historyDao.deleteBeforeMonths(apiHistoryMaxKeepMonth);
	}
}
