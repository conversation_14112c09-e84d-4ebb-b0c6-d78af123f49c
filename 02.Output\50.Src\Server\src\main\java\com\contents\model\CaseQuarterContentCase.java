package com.contents.model;

import java.util.Objects;
import com.contents.model.CaseQuarterContentAwarenessTime;
import com.contents.model.CaseQuarterContentDisasterClass;
import com.contents.model.CaseQuarterContentLatestDynamicStateTime;
import com.contents.model.CaseQuarterContentTownName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseQuarterContentCase
 */
@Validated


public class CaseQuarterContentCase   {
  @JsonProperty("disaster_class")
  private CaseQuarterContentDisasterClass disasterClass = null;

  @JsonProperty("town_name")
  private CaseQuarterContentTownName townName = null;

  @JsonProperty("awareness_time")
  private CaseQuarterContentAwarenessTime awarenessTime = null;

  @JsonProperty("latest_dynamic_state_time")
  private CaseQuarterContentLatestDynamicStateTime latestDynamicStateTime = null;

  public CaseQuarterContentCase disasterClass(CaseQuarterContentDisasterClass disasterClass) {
    this.disasterClass = disasterClass;
    return this;
  }

  /**
   * Get disasterClass
   * @return disasterClass
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentDisasterClass getDisasterClass() {
    return disasterClass;
  }

  public void setDisasterClass(CaseQuarterContentDisasterClass disasterClass) {
    this.disasterClass = disasterClass;
  }

  public CaseQuarterContentCase townName(CaseQuarterContentTownName townName) {
    this.townName = townName;
    return this;
  }

  /**
   * Get townName
   * @return townName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentTownName getTownName() {
    return townName;
  }

  public void setTownName(CaseQuarterContentTownName townName) {
    this.townName = townName;
  }

  public CaseQuarterContentCase awarenessTime(CaseQuarterContentAwarenessTime awarenessTime) {
    this.awarenessTime = awarenessTime;
    return this;
  }

  /**
   * Get awarenessTime
   * @return awarenessTime
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentAwarenessTime getAwarenessTime() {
    return awarenessTime;
  }

  public void setAwarenessTime(CaseQuarterContentAwarenessTime awarenessTime) {
    this.awarenessTime = awarenessTime;
  }

  public CaseQuarterContentCase latestDynamicStateTime(CaseQuarterContentLatestDynamicStateTime latestDynamicStateTime) {
    this.latestDynamicStateTime = latestDynamicStateTime;
    return this;
  }

  /**
   * Get latestDynamicStateTime
   * @return latestDynamicStateTime
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentLatestDynamicStateTime getLatestDynamicStateTime() {
    return latestDynamicStateTime;
  }

  public void setLatestDynamicStateTime(CaseQuarterContentLatestDynamicStateTime latestDynamicStateTime) {
    this.latestDynamicStateTime = latestDynamicStateTime;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseQuarterContentCase caseQuarterContentCase = (CaseQuarterContentCase) o;
    return Objects.equals(this.disasterClass, caseQuarterContentCase.disasterClass) &&
        Objects.equals(this.townName, caseQuarterContentCase.townName) &&
        Objects.equals(this.awarenessTime, caseQuarterContentCase.awarenessTime) &&
        Objects.equals(this.latestDynamicStateTime, caseQuarterContentCase.latestDynamicStateTime);
  }

  @Override
  public int hashCode() {
    return Objects.hash(disasterClass, townName, awarenessTime, latestDynamicStateTime);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseQuarterContentCase {\n");
    
    sb.append("    disasterClass: ").append(toIndentedString(disasterClass)).append("\n");
    sb.append("    townName: ").append(toIndentedString(townName)).append("\n");
    sb.append("    awarenessTime: ").append(toIndentedString(awarenessTime)).append("\n");
    sb.append("    latestDynamicStateTime: ").append(toIndentedString(latestDynamicStateTime)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
