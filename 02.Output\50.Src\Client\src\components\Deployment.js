import React from 'react';
import Title from './elements/Title';
import Cell from './elements/Cell';
import { getCellFace, isValidSource } from '../utils/Util.js';

/**
 * 配備状況コンテンツ<br>
 * propsは、「3.4配備状況コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module Deployment
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Deployment = (props) => {
  return (
    <div className="text-6xl">
      <Title title={'配備状況'} />
      {isValidSource(props) && (
        <div className="border-transparent border-x-[1rem] grid grid-cols-23 text-5xl auto-cols-fr leading-[1.8]">
          {
            // 車両名称の配列を軸で、行を作る
            props.car_name.map((item, index) => {
              return <DeploymentRow key={index} {...props} index={index} />;
            })
          }
        </div>
      )}
    </div>
  );
};

const DeploymentRow = (props) => {
  let cell1 = getCellFace(
    props.deployment ? props.deployment[props.index] : null,
    'col-span-2'
  );
  let cell2 = getCellFace(
    props.car_name ? props.car_name[props.index] : null,
    'col-span-4 col-start-5'
  );
  let cell3 = getCellFace(
    props.arrow ? props.arrow[props.index] : null,
    'col-span-1 col-start-11'
  );
  let cell4 = getCellFace(
    props.move_car_name ? props.move_car_name[props.index] : null,
    'col-span-4 col-start-14'
  );
  let cell5 = getCellFace(
    props.car_type ? props.car_type[props.index] : null,
    'col-span-4 col-start-20'
  );

  return (
    <>
      <Cell {...cell1} />
      <Cell {...cell2} />
      <Cell {...cell3} />
      <Cell {...cell4} />
      <Cell {...cell5} />
    </>
  );
};

export default Deployment;
