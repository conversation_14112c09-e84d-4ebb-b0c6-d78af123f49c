import React, { useState, useEffect } from 'react';
import { useSubscription, useStompClient } from 'react-stomp-hooks';
import PropTypes from 'prop-types';
import { sendResultMsg, getWsEndpoint } from '../utils/Util.js';

const propTypes = {
  displayNo: PropTypes.number,
  splitNo: PropTypes.number,
};

const fileUrl = process.env.PUBLIC_URL; 
const filePath = "/sounds";
const fileName = "/sound";
const fileType = ".mp3";
const sound = new Audio();
let playcount;
let repeatmax;
let result;

// 再生終了イベント
sound.addEventListener("ended", function () {
  console.log( 'soundPlayEnd: ended');
}, false);

/**
 * @module Sound
 * @component 
 * @param {*} props
 * @return {*} Sound
 */
const Sound = (props) => {
 /**
 * public class SoundInfo {
 *     private Integer soundNo; //再生ファイル番号
 *     private Integer repeatCount; //リピート回数
 *     private Object sourceData; //実際のContent内容
 *     private String id; //該当コンテンツのID
 * }
 */
  const [soundInfo, setSoundInfo] = useState();

  const receiveSound = (message) => {
    console.info('receiveSound: ' + message.body);

    let command = JSON.parse(message.body);
    const isString = (val) => typeof val === 'string';
    if (command.sourceData) {
      if (isString(command.sourceData)) {
        command.sourceData = JSON.parse(command.sourceData);
      }
    }

    if (command) {
      // 実行結果をクリア
      result = 0;

      // 再生停止、ループ設定クリア
      sound.pause();
      sound.currentTime = 0;
      sound.loop = false;

      // ファイル番号設定
      //console.log( 'command.sound_no = ' + command.soundNo);
      //console.log( 'command.repeat_count = ' + command.repeatCount);
      let no = command.soundNo;
      let AudPath = `${filePath}${fileName}${no}${fileType}`
      
      // リピート設定
      playcount = 0;
      repeatmax = command.repeatCount;
      if( repeatmax > 0 ){
        sound.loop = true;
      } else {
        sound.loop = false;
      }

      console.info('receiveSound: Repeat='+ repeatmax + ', '+ AudPath);

      // リソース設定して再生
      sound.src = (fileUrl + AudPath);
      sound.play();

/* 必ず正常で応答する場合はここを有効化してイベント内の更新を無効化する
      // 更新
      setSoundInfo((split) => ({
        ...split,
        ...command,
      }));
*/

      // errorイベント
      //  - エラーによりリソースの読み込みができなかった場合に発行される
      sound.onerror = function() {
        console.log( 'sound play error: ' + sound.error.message);
        sound.loop = false;
        // 更新
        result = -1;
        setSoundInfo((split) => ({
          ...split,
          ...command,
        }));
      }

      // playingイベント
      //  - 再生が開始できる状態になったときに発行される
      sound.onplaying = function() {
        playcount++;
        console.log( 'sound playing count(' + playcount +')' );

        // リピートの停止処理
        if( playcount <= repeatmax ){
          console.log( 'sound repeat');
        } else {
          sound.loop = false;
          console.log( 'sound repeat end ' );
        }

        // 更新
        result = 0;
        setSoundInfo((split) => ({
          ...split,
          ...command,
        }));
      }

    } else {
      console.info('receiveSound: command none');
    }

    //console.log( 'receiveSound end.');
  };

  const wsEndpoint = getWsEndpoint(
    props.displayNo,
    props.splitNo,
  );

  useSubscription(wsEndpoint + '/setSound', receiveSound); // Url:  /monitor/0_0/setSound

  const stompClient = useStompClient();

  useEffect(() => {
    if (soundInfo?.id) {
      sendResultMsg(stompClient, soundInfo.id, result);
    }
  }, [soundInfo?.id, stompClient]);

  return;
};

Sound.propTypes = propTypes;
export default Sound;
