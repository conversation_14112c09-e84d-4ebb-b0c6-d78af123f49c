import React from 'react';
import Title from './elements/Title';
import TextScroll from './elements/TextScroll';
import Cell from './elements/Cell';
import CellBox from './elements/CellBox';
import {
  getCellFace,
  isValidSource,
  formateDatetimeText,
} from '../utils/Util.js';

/**
 * 予定コンテンツ<br>
 * propsは、「3.15予定コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module Schedule
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Schedule = (props) => {
  const MAX_ROW = 6;
  return (
    <div className="text-4xl">
      <Title title={'予定'} />
      {isValidSource(props) && (
        <div className="border-transparent border-x-[1rem] grid grid-row-6 grid-cols-[repeat(9,4.7rem)_minmax(0.25rem,1fr)_repeat(10,7.5rem)] leading-[1] gap-y-[3.5rem] mt-[2rem] items-end">
          {props.schedule_ts?.map((item, index) => {
            if (index >= MAX_ROW) {
              return undefined;
            }

            return <ScheduleRow key={index} {...props} index={index} />;
          })}
        </div>
      )}
    </div>
  );
};

const ScheduleRow = (props) => {
  if (!props.schedule_ts[props.index]) {
    return;
  }
  let cell1 = getCellFace(
    props.schedule_ts[props.index],
    'col-span-9 text-5xl self-end'
  );
  cell1.text = formateDatetimeText(cell1.text, '3rem');

  let cell2 = { className: 'col-span-10 col-start-11' };
  let cell2PropFrame;
  if (props.schedule_content && props.schedule_content[props.index]) {
    cell2 = getCellFace(
      props.schedule_content[props.index],
      'col-span-10 col-start-11 text-7xl self-end'
    );
  }
  cell2PropFrame = {
    className: cell2.className,
    text_color: cell2.text_color,
  };

  return (
    <>
      <Cell {...cell1} />
      <CellBox {...cell2PropFrame}>
        <TextScroll
          content={cell2.text}
          background_color={cell2.background_color}
        />
      </CellBox>
    </>
  );
};

export default Schedule;
