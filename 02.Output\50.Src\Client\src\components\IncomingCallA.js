import React from 'react';
import Title from './elements/Title';
import BlinkBlock from './elements/BlinkBlock';
import { isValidSource } from '../utils/Util.js';

/**
 * 着信状況コンテンツA<br>
 * propsは、「3.8着信状況コンテンツA情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module IncomingCallA
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const IncomingCallA = (props) => {
  const MAX_ROW = 28;
  return (
    <>
      <Title title={'着信状況'} />
      <div className="mx-[2rem]">
        <div className="grid grid-cols-[repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)_minmax(0.25rem,1fr)_repeat(4,6.5rem)] items-center leading-[1]">
          {
            // 回線名間の間隔は4remとの仕様ですが、実際には、自動調整に任せる
            isValidSource(props) &&
              props.line_name.map((item, index) => {
                if (index >= MAX_ROW) return undefined;
                
                if (!item.display_text) {
                  return '';
                }

                const colIndex = index % 4;
                let colStart;
                let colSpan = 'col-span-4';
                let font = 'text-6.5xl';
                switch (colIndex) {
                  case 1:
                    colStart = 'col-start-6';
                    break;
                  case 2:
                    colStart = 'col-start-11';
                    break;
                  case 3:
                    colStart = 'col-start-16';
                    break;
                  default:
                    colStart = 'col-start-1';
                    break;
                }

                const isEng = checkEng(item.display_text);
                if (isEng) {
                  font = 'text-4.5xl';
                }

                const showBlock = [
                  { showInfo: item, className: 'my-[1.5rem] w-fit' },
                ];

                return (
                  <div key={index} className={`${colSpan} ${colStart} ${font}`}>
                    <BlinkBlock
                      key={index}
                      block={showBlock}
                      index={index}
                      blink_setting={item.blink_setting}
                    />
                  </div>
                );
              })
          }
        </div>
      </div>
    </>
  );
};

//英語かを返す
function checkEng(num) {
  var reg = /^[A-Za-z]+$/;
  return reg.test(num);
}

export default IncomingCallA;
