package com.contents.devicemodel.sound;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.contents.common.MessageFormatUtil;
import com.contents.common.db.Control;
import com.contents.common.external.cross.DeviceLinkItem;
import com.contents.common.external.cross.DisplayGroupInfo;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.common.external.cross.VolumeControllerDevice;
import com.contents.common.external.cross.VolumeControllerRoutingInfo;
import com.contents.devicemodel.DeviceModel;
import com.contents.devicemodel.ExecutionPlan;
import com.contents.devicemodel.monitor.MonitorDeviceModel;
import com.contents.devicemodel.switcher.MatrixSwitcherDeviceModel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class SoundVolumeDeviceModel extends DeviceModel {

	protected final Integer VOL_CONTROL_ENABLE = 1;

	protected VolumeControllerDevice volumeControllerDevice;

	protected VolumeControllerRoutingInfo volumeControllerRoutingInfo;


	public void bind(VolumeControllerDevice volumeControllerDevice) {
		this.volumeControllerDevice = volumeControllerDevice;
		this.deviceNumber = volumeControllerDevice.getDeviceNumber();
		this.productName = volumeControllerDevice.getProductName();
		this.networkInfo = volumeControllerDevice.getNetworkInfo();
	}

	public void expand(RoutingInformation linkInformation) {

		{

			java.util.Optional<VolumeControllerRoutingInfo> optional = linkInformation.getVolumeControllerRoutingList().stream().filter((link) -> Objects.equals(link.getDeviceNumber(), this.deviceNumber)).findFirst();

			if (!optional.isEmpty()) {
				this.volumeControllerRoutingInfo = optional.get();
			}

		}

	}

	public List<ExecutionPlan> createExecutionPlan(Control control) {

		List<ExecutionPlan> executionPlanList = new ArrayList<ExecutionPlan>();

		if (this.volumeControllerRoutingInfo == null)
			return executionPlanList;

		List<MonitorDeviceModel> monitorDeviceModelList = deviceModelInfo.getMonitorDeviceModelList();
		List<MatrixSwitcherDeviceModel> matrixSwitcherDeviceModelList = deviceModelInfo.getMatrixSwitcherDeviceModelList();

		final Integer displayType = control.getDisplay_type();
		final Integer displayNo = control.getDisplay_no();
		final Integer displaySplitNo = control.getDisplay_split_no();
		final Integer sourceNo = control.getSource_no();
		final Integer inputSwitch = control.getInput_switch();

		final Integer isVolumeControl = control.getIs_vol_control();
		final Integer sourceVolumeControlType = control.getSource_vol_control_type();
		final Integer sourceVolumeControlMute = control.getSource_vol_mute_control();

		try {

			if (isVolumeControl == null) {
				return null;
			}

			MonitorDeviceModel monitorDeviceModel = monitorDeviceModelList.stream().filter((monitor) -> monitor.match(displayType, displayNo, displaySplitNo)).findFirst().orElse(null);

			if (monitorDeviceModel == null)
				return null;

			DisplayGroupInfo displayGroupInfo = monitorDeviceModel.getDisplayGroupInfo();

			if (displayGroupInfo == null)
				return null;

			List<DeviceLinkItem> outputList = this.volumeControllerRoutingInfo.getOutputList();

			if (outputList == null)
				return null;

			DeviceLinkItem outputLink = outputList.stream().filter((output) -> Objects.equals(output.getDeviceNumber(), displayGroupInfo.getDeviceNumber())).findFirst().orElse(null);

			if (outputLink == null) {
				// 対象モニターに自身のデバイスが関与していないので処理終了
				return null;
			}

			boolean mute = !Objects.equals(isVolumeControl, VOL_CONTROL_ENABLE);
			final Integer sourceVolumeLevel = Objects.equals(isVolumeControl, VOL_CONTROL_ENABLE) ? sourceVolumeControlType : 0;

			ExecutionPlan plan = new ExecutionPlan() {
				@Override
				public List<String> transmit() {
					
					synchronized (TRANSMIT_LOCK_OBJECT) {

						List<String> errors = new ArrayList<String>();
						String format = "音量コントローラーの切り替えに失敗しました。displayNo={}, displaySplitNo={}, sourceNo={}, isVolumeControl={}, sourceVolumeLevel={}, sourceVolumeControlMute={}";
						Object[] params = new Object[] { displayNo, displaySplitNo, sourceNo, isVolumeControl, sourceVolumeLevel, sourceVolumeControlMute };
						
						try {
							boolean result = volumeControl(outputLink.getConnectorNumber(), outputLink.getConnectorNumber(), sourceVolumeLevel, mute);
							if (!result) {
								errors.add(MessageFormatUtil.format(format, params));
							}
							return errors;
						} catch (Exception e) {
							log.error(format, params);
							log.error("SoundVolumeDeviceModel transmit volumeControl", e);
							log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
							errors.add(MessageFormatUtil.format(format, params));
						}
						
						return errors;
					}
				}
			};

			executionPlanList.add(plan);

			// TODO 機器構成の要素でエラーが発生した場合に、１回あたりの受信すべてをエラーにするか、活きている機器だけは処理しきるのか要相談
			//return executionPlanList;

		} catch (Exception e) {
			log.error("createExecutionPlan control error.", e);
			String format = "音量コントローラーの切り替えに失敗しました。model={}, ip={}, displayType={}, displayNo={}, displaySplitNo={}, sourceNo={}, inputSwitch={}";
			Object[] params = new Object[] { this.productName, this.networkInfo.getIpAddress(), displayType, displayNo, displaySplitNo, sourceNo, inputSwitch };
			String message = MessageFormatUtil.format(format, params);
			log.error(message);
			log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
			//throw new Exception(message, e);
		}

		return executionPlanList;
	}

	public abstract boolean volumeControl(Integer inputChannel, Integer outputChannel, Integer volumeLevel, boolean mute);

}
