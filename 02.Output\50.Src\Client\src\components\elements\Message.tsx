import React from 'react';

/**
 * エラー画面
 * 
 * @module Message
 * @component
 * @param {propTypes} props
 * @returns 表示画面
 */
const Message = (props: { msg: string; title: string }) => {
  const title = props.title || 'エラー発生';

  return (
    <>
      <div role="alert">
        <div className="bg-red-500 text-white font-bold rounded-t px-4 py-2 text-2xl">
          {title}
        </div>
        <div className="border border-t-0 border-red-400 rounded-b bg-red-100 px-4 py-3 text-red-700 text-4xl">
          <p>{props.msg}</p>
        </div>
      </div>
    </>
  );
};

export default Message;
