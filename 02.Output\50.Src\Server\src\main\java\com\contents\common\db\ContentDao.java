package com.contents.common.db;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

/**
 * コンテンツテーブルを操作するDAO
 */
@Repository
@RepositoryRestResource(path = "content")
public interface ContentDao extends JpaRepository<Content, ContentPK>, Serializable {

	/**
	 * 最新の受信時間で、指定したCount行を返す
	 *
	 * @param count　返す必要な行数
	 * @return 行のリスト
	 */
	@Query(value = "select * from Content t order by receive_time desc limit :count", nativeQuery = true)
	List<Content> findRowByLastReceiveTime(@Param("count") Integer count);


}
