package com.contents.model;

import java.util.Objects;
import com.contents.model.AttendanceContentAttendanceDynamicStateName;
import com.contents.model.AttendanceContentOfficialPositionName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AttendanceContent
 */
@Validated


public class AttendanceContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("official_position_name")
  @Valid
  private List<AttendanceContentOfficialPositionName> officialPositionName = null;

  @JsonProperty("attendance_dynamic_state_name")
  @Valid
  private List<AttendanceContentAttendanceDynamicStateName> attendanceDynamicStateName = null;

  public AttendanceContent officialPositionName(List<AttendanceContentOfficialPositionName> officialPositionName) {
    this.officialPositionName = officialPositionName;
    return this;
  }

  public AttendanceContent addOfficialPositionNameItem(AttendanceContentOfficialPositionName officialPositionNameItem) {
    if (this.officialPositionName == null) {
      this.officialPositionName = new ArrayList<AttendanceContentOfficialPositionName>();
    }
    this.officialPositionName.add(officialPositionNameItem);
    return this;
  }

  /**
   * Get officialPositionName
   * @return officialPositionName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=14)   public List<AttendanceContentOfficialPositionName> getOfficialPositionName() {
    return officialPositionName;
  }

  public void setOfficialPositionName(List<AttendanceContentOfficialPositionName> officialPositionName) {
    this.officialPositionName = officialPositionName;
  }

  public AttendanceContent attendanceDynamicStateName(List<AttendanceContentAttendanceDynamicStateName> attendanceDynamicStateName) {
    this.attendanceDynamicStateName = attendanceDynamicStateName;
    return this;
  }

  public AttendanceContent addAttendanceDynamicStateNameItem(AttendanceContentAttendanceDynamicStateName attendanceDynamicStateNameItem) {
    if (this.attendanceDynamicStateName == null) {
      this.attendanceDynamicStateName = new ArrayList<AttendanceContentAttendanceDynamicStateName>();
    }
    this.attendanceDynamicStateName.add(attendanceDynamicStateNameItem);
    return this;
  }

  /**
   * Get attendanceDynamicStateName
   * @return attendanceDynamicStateName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=14)   public List<AttendanceContentAttendanceDynamicStateName> getAttendanceDynamicStateName() {
    return attendanceDynamicStateName;
  }

  public void setAttendanceDynamicStateName(List<AttendanceContentAttendanceDynamicStateName> attendanceDynamicStateName) {
    this.attendanceDynamicStateName = attendanceDynamicStateName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AttendanceContent attendanceContent = (AttendanceContent) o;
    return Objects.equals(this.officialPositionName, attendanceContent.officialPositionName) &&
        Objects.equals(this.attendanceDynamicStateName, attendanceContent.attendanceDynamicStateName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(officialPositionName, attendanceDynamicStateName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AttendanceContent {\n");
    
    sb.append("    officialPositionName: ").append(toIndentedString(officialPositionName)).append("\n");
    sb.append("    attendanceDynamicStateName: ").append(toIndentedString(attendanceDynamicStateName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
