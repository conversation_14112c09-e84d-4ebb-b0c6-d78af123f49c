package com.contents.model;

import java.util.Objects;
import com.contents.model.ControlMonitorDisplayContentData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ControlMonitorDisplayDetailsSplitData
 */
@Validated


public class ControlMonitorDisplayDetailsSplitData   {
  @JsonProperty("display_details_split_no")
  private Integer displayDetailsSplitNo = null;

  @JsonProperty("display_content_data")
  private ControlMonitorDisplayContentData displayContentData = null;

  public ControlMonitorDisplayDetailsSplitData displayDetailsSplitNo(Integer displayDetailsSplitNo) {
    this.displayDetailsSplitNo = displayDetailsSplitNo;
    return this;
  }

  /**
   * 表示盤に表示可能なコンテンツの最小サイズ毎に付与される番号 指令センター表示盤の場合に分割単位に番号を設定 （4面マルチ表示盤：0~15、単面表示盤：0~3） 署所表示盤の場合には固定で0を設定（分割無し）
   * minimum: 0
   * maximum: 15
   * @return displayDetailsSplitNo
   **/
  @Schema(description = "表示盤に表示可能なコンテンツの最小サイズ毎に付与される番号 指令センター表示盤の場合に分割単位に番号を設定 （4面マルチ表示盤：0~15、単面表示盤：0~3） 署所表示盤の場合には固定で0を設定（分割無し）")
  
  @Min(0) @Max(15)   public Integer getDisplayDetailsSplitNo() {
    return displayDetailsSplitNo;
  }

  public void setDisplayDetailsSplitNo(Integer displayDetailsSplitNo) {
    this.displayDetailsSplitNo = displayDetailsSplitNo;
  }

  public ControlMonitorDisplayDetailsSplitData displayContentData(ControlMonitorDisplayContentData displayContentData) {
    this.displayContentData = displayContentData;
    return this;
  }

  /**
   * Get displayContentData
   * @return displayContentData
   **/
  @Schema(description = "")
  
    @Valid
    public ControlMonitorDisplayContentData getDisplayContentData() {
    return displayContentData;
  }

  public void setDisplayContentData(ControlMonitorDisplayContentData displayContentData) {
    this.displayContentData = displayContentData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControlMonitorDisplayDetailsSplitData controlMonitorDisplayDetailsSplitData = (ControlMonitorDisplayDetailsSplitData) o;
    return Objects.equals(this.displayDetailsSplitNo, controlMonitorDisplayDetailsSplitData.displayDetailsSplitNo) &&
        Objects.equals(this.displayContentData, controlMonitorDisplayDetailsSplitData.displayContentData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayDetailsSplitNo, displayContentData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControlMonitorDisplayDetailsSplitData {\n");
    
    sb.append("    displayDetailsSplitNo: ").append(toIndentedString(displayDetailsSplitNo)).append("\n");
    sb.append("    displayContentData: ").append(toIndentedString(displayContentData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
