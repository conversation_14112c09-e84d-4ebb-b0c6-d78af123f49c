package com.contents.common.external.initial;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報) マトリクススイッチャ
 */
public class MatrixSw implements DeviceInterface {

    @JsonProperty("ip_address")
    private String ipAddress;
    
    @JsonProperty("port")
    private Integer port;
    
    @JsonProperty("product_name")
    private String productName;
    
    @JsonProperty("video_source_list")
    private List<VideoSource> videoSourceList = null;

    @JsonProperty("ip_address")
    public String getIpAddress() {
        return ipAddress;
    }

    @JsonProperty("ip_address")
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    @JsonProperty("port")
    public Integer getPort() {
        return port;
    }

    @JsonProperty("port")
    public void setPort(Integer port) {
        this.port = port;
    }

    @JsonProperty("product_name")
    public String getProductName() {
        return productName;
    }

    @JsonProperty("product_name")
    public void setProductName(String productName) {
        this.productName = productName;
    }

    @JsonProperty("video_source_list")
    public List<VideoSource> getVideoSourceList() {
        return videoSourceList;
    }

    @JsonProperty("video_source_list")
    public void setVideoSourceList(List<VideoSource> videoSourceList) {
        this.videoSourceList = videoSourceList;
    }

}