package com.contents.composition;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;

import com.contents.common.MessageFormatUtil;
import com.contents.common.db.Control;
import com.contents.common.external.initial.MediaReceiverPc;
import com.contents.configuration.ExternalConfiguration;
import com.contents.external.matrix_sw.fdx_s.MatrixSwControl;
import com.contents.external.monitor.lcd_un.MultiMonitorControl;
import com.contents.external.monitor.pn_hs.SingleMonitorControl;
import com.contents.external.sound_volume.SoundVolumeControl;

import lombok.extern.slf4j.Slf4j;

/**
 * 新指令台初期構想構成クラス
 */

@Slf4j
public class InitialConceptComposition implements MachineComposition {

	private final ExternalConfiguration externalConfiguration;

	private final SingleMonitorControl singleMonCtrl;
	private final SoundVolumeControl soundVolCtrl;
	private final MultiMonitorControl multiMonCtrl;
	private final MatrixSwControl matSwCtrl;

	private final int DISPLAY_TYPE_MULTI_MONITOR = 0;
	private final int DISPLAY_TYPE_SINGLE_MONITOR = 1;
	private final int IS_VOL_CONTROL_ENABLE = 1;

	@Autowired
	public InitialConceptComposition(
			ExternalConfiguration externalConfiguration,
			SingleMonitorControl singleMonCtrl,
			SoundVolumeControl soundVolCtrl,
			MultiMonitorControl multiMonCtrl,
			MatrixSwControl matSwCtrl) {
		
		this.externalConfiguration = externalConfiguration;
		this.singleMonCtrl = singleMonCtrl;
		this.soundVolCtrl = soundVolCtrl;
		this.multiMonCtrl = multiMonCtrl;
		this.matSwCtrl = matSwCtrl;
	}

	/**
	 * 周期処理
	 */
	public void manageExec() {
		log.info("ExternalManager start.");

		// 音量コントローラ
		soundVolCtrl.healthCheck();
		// 単面モニタ
		singleMonCtrl.healthCheck();
		// 4面マルチモニタ
		multiMonCtrl.healthCheck();
		// マトリックススイッチャー
		matSwCtrl.healthCheck();

		log.info("ExternalManager end.");
	}

	/**
	 * 接続状態取得
	 * @return 処理結果 true:正常, false:異常
	 */
	public boolean checkHealth() {
		boolean isConnect;
		boolean ret;

		// 音量コントローラ
		isConnect = soundVolCtrl.isConnect();
		log.info("Sound volume controller connection status.　[{}]", isConnect);
		ret = isConnect;

		// 単面モニター
		isConnect = singleMonCtrl.isConnect();
		log.info("Single monitor controller connection status.　[{}]", isConnect);
		ret = (ret && isConnect);

		// 4面マルチモニタ
		isConnect = multiMonCtrl.isConnect();
		log.info("Multi monitor controller connection status.　[{}]", isConnect);
		ret = (ret && isConnect);

		// マトリックススイッチャー
		isConnect = matSwCtrl.isConnect();
		log.info("Matrix switcher controller connection status.　[{}]", isConnect);
		ret = (ret && isConnect);

		return ret;
	}

	/**
	* [3.1表示制御情報更新]API用の関数。<br>
	* APIの内容を丸ごとでパラメータとして処理
	* @param controlList 制御パラメータリスト
	* @return ErrorList 機器制御エラーの対象機器名の配列
	*/
	@Override
	public List<String> machineControl(List<Control> controlList) {

		boolean result;
		List<String> errorStringList = new ArrayList<>();

		for (Control control : controlList) {

			Integer displayType = control.getDisplay_type();
			Integer displayNo = control.getDisplay_no();
			Integer displaySplitNo = control.getDisplay_split_no();
			Integer inputSwitch = control.getInput_switch();
			Integer sourceNo = control.getSource_no();
			Integer sourceDispPat = control.getSource_disp_pat();
			Integer isVolumeControl = control.getIs_vol_control();
			Integer sourceVolControlType = control.getSource_vol_control_type();
			Integer sourceVolMuteControl = control.getSource_vol_mute_control();

			if (displayType == DISPLAY_TYPE_MULTI_MONITOR) {
				/* ４面マルチモニター */
				if (inputSwitch != null) {
					result = multiMonCtrl.selectInput(displayNo, displaySplitNo, inputSwitch);
					if (!result) {
						// エラー時はErrorListに詳細情報を追加
						String format = "MultiMonitorControl.selectInput(displayNo={}, displaySplitNo={}, inputSwitch={})";
						String errmsg = MessageFormatUtil.format(format, displayNo, displaySplitNo, inputSwitch);
						log.warn(errmsg);
						errorStringList.add(errmsg);
					}
				}

				if (sourceDispPat != null) {
					if (sourceDispPat == 3) {
						// ソース表示パターンが 4面表示 の場合
						// 簡単タイルマトリクス設定ON
						result = multiMonCtrl.setTileMatrix(displayNo, displaySplitNo, true);
						if (!result) {
							// エラー時はErrorListに詳細情報を追加
							String format = "MultiMonitorControl.setTileMatrix(displayNo={}, displaySplitNo={}, sourceDispPat={})";
							String errmsg = MessageFormatUtil.format(format, displayNo, displaySplitNo, true);
							log.warn(errmsg);
							errorStringList.add(errmsg);
						}
					} else {
						// 簡単タイルマトリクス設定OFF
						result = multiMonCtrl.setTileMatrix(displayNo, displaySplitNo, false);
						if (!result) {
							// エラー時はErrorListに詳細情報を追加
							String format = "MultiMonitorControl.setTileMatrix(displayNo={}, displaySplitNo={}, sourceDispPat={})";
							String errmsg = MessageFormatUtil.format(format, displayNo, displaySplitNo, false);
							log.warn(errmsg);
							errorStringList.add(errmsg);
						}
					}
				}

			} else if (displayType == DISPLAY_TYPE_SINGLE_MONITOR) {
				/* 単面モニター */
				if (inputSwitch != null) {
					// 入力切替
					result = singleMonCtrl.selectInput(displayNo, displaySplitNo, inputSwitch);
					if (!result) {
						// エラー時はErrorListに詳細情報を追加
						String format = "SingleMonitorControl.selectInput(displayNo={}, displaySplitNo={}, inputSwitch={})";
						String errmsg = MessageFormatUtil.format(format, displayNo, displaySplitNo, inputSwitch);
						log.warn(errmsg);
						errorStringList.add(errmsg);
					}
				}
			}

			if (sourceNo != null) {
				/* マトリクススイッチャー */
				// 映像切替
				result = matSwCtrl.videoSwitch(sourceNo, displayNo, displaySplitNo);
				if (!result) {
					// エラー時はErrorListに詳細情報を追加
					String format = "MatrixSwControl.videoSwitch(sourceNo={}, displayNo={}, displaySplitNo={})";
					String errmsg = MessageFormatUtil.format(format, sourceNo, displayNo, displaySplitNo);
					log.warn(errmsg);
					errorStringList.add(errmsg);
				}
			}

			if (isVolumeControl != null) {
				if (isVolumeControl == IS_VOL_CONTROL_ENABLE) {
					/* マトリクススイッチャー */
					if (sourceNo != null) {
						// 音声切替

						{
							result = matSwCtrl.audioSwitch(sourceNo, displayNo);
							if (!result) {
								// エラー時はErrorListに詳細情報を追加
								String format = "MatrixSwControl.audioSwitch(sourceNo={}, displayNo={}, isVolumeControl={})";
								String errmsg = MessageFormatUtil.format(format, sourceNo, displayNo, isVolumeControl);
								log.warn(errmsg);
								errorStringList.add(errmsg);
							}
						}
					}

					/* 音量コントローラー */
					if (sourceVolControlType != null) {
						result = soundVolCtrl.volumeControl(
								displayNo,
								sourceVolControlType,
								(sourceVolMuteControl != null) ? sourceVolMuteControl : 0);
						if (!result) {
							// エラー時はErrorListに詳細情報を追加
							String format = "SoundVolumeControl.volumeControl(displayNo={}, sourceVolControlType={}, sourceVolMuteControl={})";
							String errmsg = MessageFormatUtil.format(format, displayNo, sourceVolControlType, sourceVolMuteControl);
							log.warn(errmsg);
							errorStringList.add(errmsg);
						}
					}
				} else {
					/* 音量コントローラー */
					result = soundVolCtrl.volumeControl(displayNo, 0, 1);
					if (!result) {
						// エラー時はErrorListに詳細情報を追加
						String format = "SoundVolumeControl.volumeControl(displayNo={}, sourceVolControlType={}, sourceVolMuteControl={})";
						String errmsg = MessageFormatUtil.format(format, displayNo, 0, 1);
						log.warn(errmsg);
						errorStringList.add(errmsg);
					}
				}
			}
		}

		return errorStringList;
	}

	@Override
	public Integer getUsbSpeakerDisplaySplitNo(Integer displayType, Integer displayNo) {

		// 表示盤番号からスピーカー接続された面番号を探す
		MediaReceiverPc pcInfo = this.externalConfiguration.getExternal().getMediaReceiverPcList().stream()
				.filter(d -> Objects.equals(d.getDisplayNo(), displayNo))
				.filter(d -> Objects.equals(d.getUsbSpeakerFlag(), true))
				.findFirst()
				.orElse(null);
		// 面番号設定
		if (pcInfo != null) {
			return pcInfo.getDisplaySplitNo();
		}

		return null;
	}

}
