package com.contents.manager;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;

import javax.xml.bind.DatatypeConverter;

import lombok.extern.slf4j.Slf4j;

/**
 * Socket通信クライアント
 */
@Slf4j
public class SocketClient {
	
	private final Lock LOCK_OBJECT = new ReentrantLock();
	
	public static final int RECEIVE_BUFFER_SIZE = 256;
	private final String ip;
	private final int port;

	private Socket socket = null;
	private OutputStream out = null;
	private InputStream in = null;

	private boolean using = false;

	/**
	 * IP取得
	 * @return IP
	 */
	public String getIp() {
		return this.ip;
	}

	/**
	 * ポート番号取得
	 * @return Port
	 */
	public int getPort() {
		return this.port;
	}

	/**
	 * 使用状態取得
	 * @return using true:使用中, false:未使用
	 */
	public boolean isUsing() {
		return using;
	}

	/**
	 * SocketClient使用開始
	 */
	public void use() {
		using = true;
	}

	/**
	 * Socket通信クライアント
	 * @param ip 接続先IP
	 * @param port 接続先Port
	 */
	public SocketClient(String ip, int port) {
		this.ip = ip;
		this.port = port;
	}

	/**
	 * Socket接続
	 * @param connTout 接続タイムアウト(ms) 0を指定で無限
	 * @param rcvTout 受信タイムアウト(ms) 0を指定で無限
	 * @return Socket接続結果 true:成功, false:失敗
	 */
	public boolean open(int connTout, int rcvTout) {
		log.info("Open a socket. ip={}, port={}", ip, port);
		try {
			InetSocketAddress endpoint= new InetSocketAddress(ip, port);
			socket = new Socket();
			socket.connect(endpoint, connTout);
			socket.setSoTimeout(rcvTout);
			out = socket.getOutputStream();
			in = socket.getInputStream();
			using = true;
			return true;
		} catch (SocketTimeoutException ste) {
			log.error("Socket timeout. ip={}, port={}, msg={}", ip, port, ste.getMessage());
			close();
			return false;
		} catch (IOException ioe) {
			log.error("Socket error." + ioe.getMessage());
			close();
			return false;
		} catch (RuntimeException e) {
			e.printStackTrace();
			close();
			return false;
		}
	}

	/**
	 * Socket接続後応答待ち
	 * @param connTout 接続タイムアウト(ms) 0を指定で無限
	 * @param rcvTout 受信タイムアウト(ms) 0を指定で無限
	 * @param callback データ受信時の処理
	 * @return Socket接続結果 true:成功, false:失敗
	 */
	public boolean openAndReplyWait(int connTout, int rcvTout, Consumer<byte[]> callback) {
		log.info("Open a socket. ip={}, port={}", ip, port);
		try {
			InetSocketAddress endpoint= new InetSocketAddress(ip, port);
			socket = new Socket();
			socket.connect(endpoint, connTout);
			socket.setSoTimeout(rcvTout);
			out = socket.getOutputStream();
			in = socket.getInputStream();
			using = true;

			//受信
			if (callback != null) {
				// 受信処理
				byte[] readBuff = new byte[RECEIVE_BUFFER_SIZE];
				int inputLen = in.read(readBuff, 0, RECEIVE_BUFFER_SIZE);

				// 受信データの格納
				byte[] recvData = new byte[inputLen];
				System.arraycopy(readBuff, 0, recvData, 0, inputLen);

				// 受信コールバックの呼び出し
				callback.accept(recvData);
			}
			return true;
		} catch (SocketTimeoutException ste) {
			log.error("Socket timeout. ip={}, port={}, msg={}", ip, port, ste.getMessage());
			close();
			return false;
		} catch (IOException ioe) {
			log.error("Socket error." + ioe.getMessage());
			close();
			return false;
		} catch (RuntimeException e) {
			e.printStackTrace();
			close();
			return false;
		}
	}

	/**
	 * Socket切断
	 */
	public void close() {
		log.info("Close the socket. ip={}, port={}", ip, port);
		try {
			if (out != null) {
				out.close();
			}
			if (in != null) {
				in.close();
			}
			if (socket != null) {
				socket.close();
			}
			// Socket接続先のClose処理を考慮して少し待つ
			Thread.sleep(100);
		} catch (IOException e) {
			e.printStackTrace();
		} catch (InterruptedException e) {
			log.warn("An interrupt occurred while waiting for socket close.");
		}
		using = false;
	}

	/**
	 * Socket通信によるデータ送信
	 * (Socekt接続 (Open) 済みであること)
	 * @param sendData 送信データ
	 * @param callback データ受信時の処理
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean send(byte[] sendData, Consumer<byte[]> callback) {
		
		synchronized (LOCK_OBJECT) {
			
			try {
				// 送信
				out.write(sendData);
				log.info("sendData: " + DatatypeConverter.printHexBinary(sendData));

				//受信
				if (callback != null) {
					// 受信処理
					byte[] readBuff = new byte[RECEIVE_BUFFER_SIZE];
					int inputLen = in.read(readBuff, 0, RECEIVE_BUFFER_SIZE);

					// 受信データの格納
					byte[] recvData = new byte[inputLen];
					System.arraycopy(readBuff, 0, recvData, 0, inputLen);

					log.info("RecvData: {}", DatatypeConverter.printHexBinary(recvData));
					
					// 受信コールバックの呼び出し
					callback.accept(recvData);
				}
				
				return true;
				
			} catch (SocketTimeoutException ste) {
				log.error("Socket timeout. ip={}, port={}, msg={}", ip, port, ste.getMessage());
				close();
				return false;
			} catch (IOException e) {
				log.error("Socket error", e);
				close();
				return false;
			} finally {
				
			}
		}
	}
}

