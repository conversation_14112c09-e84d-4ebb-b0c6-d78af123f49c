package com.contents.common.bean;

import com.contents.model.SyncTimeTime;
import lombok.Data;

/**
 * ブラウザに送る時間情報
 */
@Data
public class TimeContent extends SyncTimeTime {
    /**
     * 日付の年
     */
    private String year;
    /**
     * 日付の月
     */
    private String month;
    /**
     * 日付の日
     */
    private String day;
    /**
     * 日付の曜日
     */
    private String weekday;
    /**
     * 時間
     */
    private String hour;
    /**
     * 分
     */
    private String minute;
}
