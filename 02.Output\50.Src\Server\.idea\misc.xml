<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CheckstyleConfigurable">
    <option name="suppFilterFilename" value="" />
    <option name="suppCommentFilter" value="false" />
    <option name="offComment" value="CHECKSTYLE\:OFF" />
    <option name="onComment" value="CHECKSTYLE\:ON" />
    <option name="checkFormat" value=".*" />
    <option name="messageFormat" value="" />
    <option name="checkCPP" value="true" />
    <option name="checkC" value="true" />
    <option name="suppNearbyCommentFilter" value="false" />
    <option name="snCommentFormat" value="SUPPRESS CHECKSTYLE (\w+)" />
    <option name="snCheckFormat" value="$1" />
    <option name="snMessageFormat" value="" />
    <option name="snInfluenceFormat" value="0" />
    <option name="snCheckCPP" value="true" />
    <option name="snCheckC" value="true" />
    <option name="pathToUserRulesConfiguration" value="" />
    <option name="pathToJarWithRules" value="" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="FindBugsConfigurable">
    <option name="make" value="true" />
    <option name="effort" value="default" />
    <option name="priority" value="Medium" />
    <option name="excludeFilter" value="" />
  </component>
  <component name="FrameworkDetectionExcludesConfiguration">
    <file type="web" url="file://$PROJECT_DIR$" />
    <type id="android" />
  </component>
  <component name="JavadocGenerationManager">
    <option name="OUTPUT_DIRECTORY" value="$PROJECT_DIR$/../../90.Release用成果物/20.プログラム仕様書/ContentServer" />
    <option name="OTHER_OPTIONS" value="-encoding UTF-8 -charset UTF-8" />
    <option name="LOCALE" value="ja-JP" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/../Temp/Gen0814_4/Gen0814_5/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" project-jdk-name="corretto-17" project-jdk-type="JavaSDK" />
  <component name="SuppressionsComponent">
    <option name="suppComments" value="[]" />
  </component>
</project>