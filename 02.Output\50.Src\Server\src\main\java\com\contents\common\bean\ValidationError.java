package com.contents.common.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * APIのパラメータがValidationによって、エラー発生した時の情報
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValidationError {
    /**
     * ErrorのField
     */
    String field;
    /**
     * Errorの値
     */
    String rejectedValue;
    /**
     * 日本語のエラーメッセージ
     */
    String message;
}
