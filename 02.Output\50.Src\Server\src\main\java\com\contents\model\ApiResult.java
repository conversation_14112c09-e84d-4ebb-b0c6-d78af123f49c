package com.contents.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Objects;

/**
 * ApiResult
 */
@Validated
@Data
public class ApiResult   {
  @JsonProperty("result")
  private Integer result = null;

  @JsonProperty("msg")
  private String msg = "成功";

  public ApiResult result(Integer result) {
    this.result = result;
    return this;
  }

  /**
   * 処理結果:0：成功、-1：失敗
   * minimum: -1
   * maximum: 0
   * @return result
   **/
  @Schema(description = "処理結果:0：成功、-1：失敗")
  
  @Min(-1) @Max(0)   public Integer getResult() {
    return result;
  }

  public void setResult(Integer result) {
    this.result = result;
  }

  public ApiResult msg(String msg) {
    this.msg = msg;
    return this;
  }

  /**
   * Result=-1の場合の補足メッセージ
   * @return msg
   **/
  @Schema(description = "Result=-1の場合の補足メッセージ")
  
    public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ApiResult apiResult = (ApiResult) o;
    return Objects.equals(this.result, apiResult.result) &&
        Objects.equals(this.msg, apiResult.msg);
  }

  @Override
  public int hashCode() {
    return Objects.hash(result, msg);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApiResult {\n");
    
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    msg: ").append(toIndentedString(msg)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  public ApiResult(Integer result, String msg) {
    this.result = result;
    this.msg = msg;
  }
  public ApiResult(Integer result) {
    this.result = result;
    if (result != 0) {
      msg = "";
    }
  }
}
