package com.contents.configuration.websocket;

import com.contents.common.bean.ScreenInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.messaging.SubProtocolWebSocketHandler;

/**
 * WebsocketのSubProtocolWebSocketHandler。STOMPのDisconnect処理で、該当MonitorのステータスをOfflineに設定
 */
@Slf4j
public class CustomSubProtocolWebSocketHandler extends SubProtocolWebSocketHandler {

    @Autowired
    private MonitorHandler monitorHandler;

    public CustomSubProtocolWebSocketHandler(MessageChannel clientInboundChannel, SubscribableChannel clientOutboundChannel) {
        super(clientInboundChannel, clientOutboundChannel);
    }


    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("New websocket({}) connection was established", session.getId());
        super.afterConnectionEstablished(session);
    }

    /**
     * STOMPのDisconnect処理で、SessionIDでMonitorServiceから表示盤番号/面番号を取得して、
     * 該当MonitorのステータスをOfflineに設定
     * @param session
     * @param closeStatus
     * @throws Exception
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        log.info("websocket connection({}): was closed", session.getId());

        ScreenInfo info =  monitorHandler.getMonitor(session.getId());
        if (info != null) {
            monitorHandler.remove(session.getId());
        }

        super.afterConnectionClosed(session,closeStatus);
    }
}

