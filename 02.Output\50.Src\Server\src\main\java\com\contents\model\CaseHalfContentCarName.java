package com.contents.model;

import java.util.Objects;
import com.contents.model.ChangeSetting;
import com.contents.model.DeploymentContentCarName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseHalfContentCarName
 */
@Validated


public class CaseHalfContentCarName   {
  @JsonProperty("display_data")
  @Valid
  private List<DeploymentContentCarName> displayData = null;

  @JsonProperty("change_setting")
  private ChangeSetting changeSetting = null;

  public CaseHalfContentCarName displayData(List<DeploymentContentCarName> displayData) {
    this.displayData = displayData;
    return this;
  }

  public CaseHalfContentCarName addDisplayDataItem(DeploymentContentCarName displayDataItem) {
    if (this.displayData == null) {
      this.displayData = new ArrayList<DeploymentContentCarName>();
    }
    this.displayData.add(displayDataItem);
    return this;
  }

  /**
   * Get displayData
   * @return displayData
   **/
  @Schema(description = "")
      @Valid
    public List<DeploymentContentCarName> getDisplayData() {
    return displayData;
  }

  public void setDisplayData(List<DeploymentContentCarName> displayData) {
    this.displayData = displayData;
  }

  public CaseHalfContentCarName changeSetting(ChangeSetting changeSetting) {
    this.changeSetting = changeSetting;
    return this;
  }

  /**
   * Get changeSetting
   * @return changeSetting
   **/
  @Schema(description = "")
  
    @Valid
    public ChangeSetting getChangeSetting() {
    return changeSetting;
  }

  public void setChangeSetting(ChangeSetting changeSetting) {
    this.changeSetting = changeSetting;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseHalfContentCarName caseHalfContentCarName = (CaseHalfContentCarName) o;
    return Objects.equals(this.displayData, caseHalfContentCarName.displayData) &&
        Objects.equals(this.changeSetting, caseHalfContentCarName.changeSetting);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayData, changeSetting);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseHalfContentCarName {\n");
    
    sb.append("    displayData: ").append(toIndentedString(displayData)).append("\n");
    sb.append("    changeSetting: ").append(toIndentedString(changeSetting)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
