package com.contents.composition;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.springframework.core.io.ClassPathResource;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.SpringContextUtil;
import com.contents.common.db.Control;
import com.contents.common.external.cross.DeviceConfiguration;
import com.contents.common.external.cross.DeviceInfomation;
import com.contents.common.external.cross.DisplayDevice;
import com.contents.common.external.cross.ExternalDeviceInformation;
import com.contents.common.external.cross.MatrixSwitcherDevice;
import com.contents.common.external.cross.MediaReceiverDevice;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.common.external.cross.VideoSourceInfo;
import com.contents.common.external.cross.VolumeControllerDevice;
import com.contents.devicemodel.DeviceModel;
import com.contents.devicemodel.DeviceModelInfo;
import com.contents.devicemodel.ExecutionPlan;
import com.contents.devicemodel.madiareceiver.MediaReceiverModel;
import com.contents.devicemodel.monitor.MonitorDeviceModel;
import com.contents.devicemodel.sound.SoundVolumeDeviceModel;
import com.contents.devicemodel.switcher.MatrixSwitcherDeviceModel;
import com.contents.devicemodel.videosource.VideoSourceDeviceModel;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 新指令台クロス標準構成クラス
 */

@Slf4j
public class CrossStandardComposition implements MachineComposition {

	//	private final int DISPLAY_TYPE_MULTI_MONITOR = 0;
	//	private final int DISPLAY_TYPE_SINGLE_MONITOR = 1;
	//	private final int IS_VOL_CONTROL_ENABLE = 1;

	private DeviceModelInfo deviceModelInfo;

	private DeviceConfiguration deviceConfiguration;

	public CrossStandardComposition() {

		deviceConfiguration = loadDeviceConfiguration();

		initializeDevice();
	}

	private DeviceConfiguration loadDeviceConfiguration() {

		try {

			String tomcatBaseDir = System.getProperty("catalina.base");

			String tomcatConfDir = tomcatBaseDir + "/conf";

			final String targetFile = "device_configuration.json";
			String deviceConfiguration = tomcatConfDir + "/" + targetFile;
			File file = new File(deviceConfiguration);

			ObjectMapper mapper = new ObjectMapper();
			DeviceConfiguration obj = null;

			if (file.exists()) {
				obj = mapper.readValue(file, DeviceConfiguration.class);
			} else {
				file = new ClassPathResource(targetFile).getFile();
				obj = mapper.readValue(file, DeviceConfiguration.class);
			}

			if (obj != null) {

				log.info("load object complate device_configuration.json");
				return obj;

			} else {
				log.warn("load object error device_configuration.json");
			}

		} catch (IOException e) {
			log.warn("read file error device_configuration.json", e);
		}

		return null;
	}

	private void initializeDevice() {

		log.info("★ initializeDevice start");

		DeviceInfomation deviceInfomation = deviceConfiguration.getDeviceInfomation();

		RoutingInformation linkInformation = deviceConfiguration.getRoutingInformation();

		DeviceModelInfo newDeviceModelInfo = new DeviceModelInfo();

		ExternalDeviceInformation externalDeviceInformation = deviceConfiguration.getExternalDeviceInformation();

		{
			for (Iterator<MatrixSwitcherDevice> ite = deviceInfomation.getMatrixSwitcherList().iterator(); ite.hasNext();) {

				MatrixSwitcherDevice device = ite.next();
				String productName = device.getProductName();

				try {
					
					MatrixSwitcherDeviceModel model = SpringContextUtil.getDevice(productName, MatrixSwitcherDeviceModel.class);
					model.bind(device);
					newDeviceModelInfo.appendMatrixSwitcherDeviceModelList(model);
					
					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.info(format, productName, json);

				} catch (Exception e) {

					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.error(format, productName, json);
					throw e;
				}
			}
		}

		{
			for (Iterator<MediaReceiverDevice> ite = deviceInfomation.getMediaReceiverList().iterator(); ite.hasNext();) {

				MediaReceiverDevice device = ite.next();
				String productName = device.getProductName();

				try {
					
					MediaReceiverModel model = SpringContextUtil.getDevice(productName, MediaReceiverModel.class);
					model.bind(device);
					newDeviceModelInfo.appendMediaReceiverModelList(model);
					
					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.info(format, productName, json);

				} catch (Exception e) {

					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.error(format, productName, json);
					throw e;
				}

			}
		}

		{
			for (Iterator<DisplayDevice> ite = deviceInfomation.getDisplayList().iterator(); ite.hasNext();) {

				DisplayDevice device = ite.next();
				String productName = device.getProductName();

				try {

					MonitorDeviceModel model = SpringContextUtil.getDevice(productName, MonitorDeviceModel.class);
					model.bind(device);
					newDeviceModelInfo.appendMonitorDeviceModel(model);
					
					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.info(format, productName, json);

				} catch (Exception e) {

					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.error(format, productName, json);
					throw e;
				}

			}
		}

		{
			for (Iterator<VolumeControllerDevice> ite = deviceInfomation.getVolumeControllerList().iterator(); ite.hasNext();) {

				VolumeControllerDevice device = ite.next();
				String productName = device.getProductName();

				try {

					SoundVolumeDeviceModel model = SpringContextUtil.getDevice(productName, SoundVolumeDeviceModel.class);
					model.bind(device);
					newDeviceModelInfo.appendSoundVolumeDeviceModelList(model);
					
					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.info(format, productName, json);

				} catch (Exception e) {

					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.error(format, productName, json);
					throw e;
				}
			}
		}

		{
			for (Iterator<VideoSourceInfo> ite = externalDeviceInformation.getVideoSourceList().iterator(); ite.hasNext();) {

				VideoSourceInfo device = ite.next();
				String productName = device.getProductName();

				try {

					VideoSourceDeviceModel model = new VideoSourceDeviceModel();
					model.bind(device);
					newDeviceModelInfo.appendVideoSourceDeviceModelList(model);
					
					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.info(format, productName, json);

				} catch (Exception e) {

					String format = "ProductName: {}" + System.lineSeparator() + "{}";
					String json = CommonUtil.loggingJsonConvert(device);
					log.error(format, productName, json);
					throw e;
				}
			}
		}

		{
			List<Future<?>> futures = new ArrayList<>();

			ExecutorService executorService = CommonUtil.newFixedThreadPool();

			for (Iterator<DeviceModel> ite = newDeviceModelInfo.iterator(); ite.hasNext();) {

				DeviceModel model = ite.next();

				Runnable task = () -> {
					
					model.bind(linkInformation);
					model.bind(newDeviceModelInfo);
				};

				Future<?> future = executorService.submit(task);
				futures.add(future);
			}
			
			executorService.shutdown();

			for (Future<?> future : futures) {

				try {

					future.get();

				} catch (InterruptedException | ExecutionException e) {

					log.error("error device execution", e);
				}
			}
		}

		this.deviceModelInfo = newDeviceModelInfo;

		log.info("★ initializeDevice end");
	}

	/**
	 * 周期処理
	 */
	public void manageExec() {

		log.info("ExternalManager start.");

		for (Iterator<DeviceModel> ite = deviceModelInfo.iterator(); ite.hasNext();) {

			DeviceModel model = ite.next();

			String productName = model.getProductName();
			Integer deviceNumber = model.getDeviceNumber();
			String traceAccessInfo = model.traceAccessInfo();

			try {
				model.checkDeviceHealth();

				log.info(MessageFormatUtil.format("healthCheck ProductName: {}, DeviceNumber: {}, Access: {}, Status: {}", productName, deviceNumber, traceAccessInfo, model.isConnect() ? "True" : "False"));

			} catch (Exception e) {
				log.warn(MessageFormatUtil.format("healthCheck ProductName: {}, DeviceNumber: {}, Access: {}, Status: {}", productName, deviceNumber, traceAccessInfo, model.isConnect() ? "True" : "False"), e);
			}
		}

		log.info("ExternalManager end.");
	}

	/**
	 * 接続状態取得
	 * @return 処理結果 true:正常, false:異常
	 */
	public boolean checkHealth() {
		boolean ret = true;

		//		if (externalDeviceList == null)
		//			return ret;
		//
		//		for (ExternalDevice device : externalDeviceList) {
		//			ret &= device.isConnect();
		//		}

		return ret;
	}

	private final Lock LOCK_CREATE_PLANS = new ReentrantLock();

	private final Lock LOCK_DEVICE_EXECUTION = new ReentrantLock();

	@Override
	/**
	 * [3.1表示制御情報更新]API用の関数。<br>
	 * APIの内容を丸ごとでパラメータとして処理
	 * @param controlList 制御パラメータリスト
	 * @return ErrorList 機器制御エラーの対象機器名の配列
	 */
	public List<String> machineControl(List<Control> controlList) {

		log.info("★ machineControl start");

		// 命令の優先順位
		// 1.マトリックススイッチャー
		// 2.モニター
		// 3.音量コントローラー

		boolean debug = false;

		if (debug) {

			return debug(controlList);

		} else {

			List<ExecutionPlan> allPlans = new ArrayList<ExecutionPlan>();

			synchronized (LOCK_CREATE_PLANS) {

				List<Future<List<ExecutionPlan>>> futures = new ArrayList<>();

				ExecutorService executorService = CommonUtil.newFixedThreadPool();

				for (Iterator<DeviceModel> ite = this.deviceModelInfo.iterator(); ite.hasNext();) {

					DeviceModel model = ite.next();

					java.util.concurrent.Callable<List<ExecutionPlan>> task = () -> {

						// 実行計画を生成する
						List<ExecutionPlan> plans = model.createExecutionPlan(controlList);

						return plans;
					};

					Future<List<ExecutionPlan>> future = executorService.submit(task);
					futures.add(future);
				}

				executorService.shutdown();

				for (Future<List<ExecutionPlan>> future : futures) {
					try {

						List<ExecutionPlan> result = future.get();
						allPlans.addAll(result);

					} catch (InterruptedException | ExecutionException e) {

						log.error("error device execution", e);
					}
				}
			}

			List<String> errorStringList = new ArrayList<>();

			{
				List<Future<List<String>>> futures = new ArrayList<>();

				ExecutorService executorService = CommonUtil.newFixedThreadPool();

				synchronized (LOCK_DEVICE_EXECUTION) {

					for (Iterator<ExecutionPlan> ite = allPlans.iterator(); ite.hasNext();) {

						ExecutionPlan plan = ite.next();

						java.util.concurrent.Callable<List<String>> task = () -> {

							List<String> errors = new ArrayList<String>();

							try {
								List<String> result = plan.transmit();
								errors.addAll(result);

							} catch (Exception e) {
								log.error("machine control errror. ", e);
							}

							return errors;
						};

						Future<List<String>> future = executorService.submit(task);
						futures.add(future);
					}
				}

				executorService.shutdown();

				for (Future<List<String>> future : futures) {

					try {

						List<String> result = future.get();
						errorStringList.addAll(result);

					} catch (InterruptedException | ExecutionException e) {

						log.error("error device execution", e);
					}
				}

			}

			log.info("★ machineControl end");

			return errorStringList;
		}
	}

	private List<String> debug(List<Control> controlList) {

		List<String> results = new ArrayList<>();

		for (Iterator<DeviceModel> ite = this.deviceModelInfo.iterator(); ite.hasNext();) {

			DeviceModel model = ite.next();

			log.info("machine control call. model={}", model.getProductName());

			// 実行計画を生成する
			List<ExecutionPlan> plans = model.createExecutionPlan(controlList);

			Iterator<ExecutionPlan> planIte = plans.iterator();
			if (!planIte.hasNext())
				continue;

			try {
				while (planIte.hasNext()) {

					ExecutionPlan plan = planIte.next();
					List<String> errors = plan.transmit();

					results.addAll(errors);
				}
			} catch (Exception e) {
				log.warn("machine control errror. model={}", model.getProductName());
			}
		}

		return results;
	}

	@Override
	public Integer getUsbSpeakerDisplaySplitNo(Integer displayType, Integer displayNo) {

		MediaReceiverModel mediaReceiverModel = deviceModelInfo.getMediaReceiverModelList().stream().filter((model) -> model.isUsbSpeakerConnect(displayType, displayNo)).findFirst().orElse(null);

		if (mediaReceiverModel != null) {
			return mediaReceiverModel.getDisplaySplitNumber();
		}

		return null;
	}

}
