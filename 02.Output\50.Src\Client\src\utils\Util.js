/**
 * 画面の表示Cellに、色/文字をObjectを返す。ClassNameがあれば、一緒につける
 *
 * @export
 * @param {*} obj
 * @param {*} className
 * @return {*} Face Object
 */
export function getCellFace(obj, className) {
  if (!obj) return;

  const result = getTextFace(obj);
  if (className) {
    result.className = className;
  }
  return result;
}

/**
 * 画面の表示Cellに、色/文字をObjectを返す。
 *
 * @export
 * @param {*} obj
 * @return {*} Face Object
 */
export function getTextFace(obj) {
  if (!obj) return;
  return {
    text_color: getHexColor(obj.text_color),
    background_color: getHexColor(obj.background_color),
    text: obj.display_text,
  };
}

/**
 * propsは有効なSourceなのか
 *
 * @export
 * @param {*} props
 * @return {*} true: 有効なSourceデータ。false: 無効なSourceデータ
 */
export function isValidSource(props) {
  if (!props) {
    return false;
  }

  const entries = Object.entries(props);
  if (entries.length <= 0) {
    return false;
  }

  let hasValidProp = false;
  for (const [key, value] of entries) {
    if (value) {
      hasValidProp = true;
    }
  }

  if (!hasValidProp) {
    return false;
  }

  return true;
}

/**
 * Stompを経由で、Browser側で、該当Msgを受信できたことサーバーに返す
 *
 * @export
 * @param {*} stompClient
 * @param {*} id
 * @param {*} result
 */
export function sendResultMsg(stompClient, id, result) {
  console.log('sendResultMsg start. id=' + id);
  if (stompClient) {
    var resultObj = { id: id, result: result };

    //Send Message
    stompClient.publish({
      destination: '/app/wsresult',
      body: JSON.stringify(resultObj),
    });
    console.log('sendResultMsg end. id=' + id);
  }
}

/**
 * WebSocketのEndPointを組み立てる
 *
 * @export
 * @param {*} displayNo
 * @param {*} splitNo
 * @param {*} detailSplitNo
 * @return {*} EndPointの文字列
 */
export function getWsEndpoint(displayNo, splitNo, detailSplitNo) {
  let endPoint = '/monitor/' + displayNo + '_' + splitNo;
  if (detailSplitNo != null) {
    endPoint = endPoint + '_' + detailSplitNo;
  }
  return endPoint;
}

/**
 * Colorを#付けて返す
 *
 * @export
 * @param {*} color
 * @return {*} #ありの文字列
 */
export function getHexColor(color) {
  if (color == null) {
    return color;
  }

  if (color.startsWith('#')) {
    return color;
  } else {
    return '#' + color;
  }
}

/**
 * display_colorとtext_color両方から、文字色を返す
 * text_colorが優先
 *
 * @export
 * @param {*} props
 * @return {*} 文字色
 */
export function getTextOrDisplayColor(props) {
  return getHexColor(props?.text_color || props?.display_color);
}

/**
 * 10月11日12時13分形式の文字列に単位の文字に特別のFontSizeを付ける
 *
 * @export
 * @param {*} text 10月11日12時13分形式の文字列
 * @param {*} unitFontSize 単位のFontsize
 * @return {*}
 */
export function formateDatetimeText(text, unitFontSize) {
  if (!text || !unitFontSize) return;

  const regex = /(\d+)(月|日|時|分)/g;
  let result = [...text.matchAll(regex)];

  let formattedText = '';
  result.forEach((element) => {
    const val = element[1];
    const unit = element[2];

    formattedText += `${val}<span style="font-size:${unitFontSize}">${unit}</span>`;
  });
  return formattedText;
}

/**
 * 10月11日12時13分形式の文字列に日付と時間を分割して返す
 *
 * @export
 * @param {*} text
 * @return {*} date/timeありのObject
 */
export function splitDateAndTime(text) {
  if (!text) return;

  const regex = /(\d+月\d+日)(\d+時\d+分)/g;
  let result = [...text.matchAll(regex)];

  if (Array.isArray(result) && result.length >= 1) {
    return { date: result[0][1], time: result[0][2] };
  }
  return undefined;
}

//Htmlを含むかを返す
function hasHtmlTag(text) {
  var reg = /<[^>]+>/gi;
  return reg.test(text);
}

/**
 * Textの通常SpaceをHtmlのSpaceを入れ替える
 *
 * @export
 * @param {*} props
 * @return {*}
 */
export function replaceWithHtmlSpace(text) {
  let refinedText = text;
  if (refinedText && !hasHtmlTag(refinedText)) {
    refinedText = refinedText.replace(/ /g, '&nbsp;');
    refinedText = refinedText.replace(/　/g, '&nbsp;&nbsp;'); //全角のSpace
  }
  return refinedText;
}

/**
 * 数字に,を入れて分割。12345678 -> '12,345,678'
 *
 * @export
 * @param {*} num
 * @return {*} 文字列
 */
export function toThousands(num) {
  return (num || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
}
