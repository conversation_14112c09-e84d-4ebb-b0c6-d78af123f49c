package com.contents.model;

import java.util.Objects;
import com.contents.model.BlinkSetting;
import com.contents.model.DigitalRadioContentIncomingCallMoveStationName;
import com.contents.model.DigitalRadioContentIncomingCallTs;
import com.contents.model.DigitalRadioContentOutgoingCallMoveStationName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DigitalRadioContentWirelessChannelName
 */
@Validated


public class DigitalRadioContentWirelessChannelName   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("outgoing_call_move_station_name")
  @Valid
  private List<DigitalRadioContentOutgoingCallMoveStationName> outgoingCallMoveStationName = null;

  @JsonProperty("incoming_call_move_station_name")
  @Valid
  private List<DigitalRadioContentIncomingCallMoveStationName> incomingCallMoveStationName = null;

  @JsonProperty("incoming_call_ts")
  @Valid
  private List<DigitalRadioContentIncomingCallTs> incomingCallTs = null;

  @JsonProperty("blink_setting")
  @Valid
  private List<BlinkSetting> blinkSetting = null;

  public DigitalRadioContentWirelessChannelName displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 無線チャネル名
   * @return displayText
   **/
  @Schema(description = "無線チャネル名")
  
  @Size(max=14)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public DigitalRadioContentWirelessChannelName textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public DigitalRadioContentWirelessChannelName backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public DigitalRadioContentWirelessChannelName outgoingCallMoveStationName(List<DigitalRadioContentOutgoingCallMoveStationName> outgoingCallMoveStationName) {
    this.outgoingCallMoveStationName = outgoingCallMoveStationName;
    return this;
  }

  public DigitalRadioContentWirelessChannelName addOutgoingCallMoveStationNameItem(DigitalRadioContentOutgoingCallMoveStationName outgoingCallMoveStationNameItem) {
    if (this.outgoingCallMoveStationName == null) {
      this.outgoingCallMoveStationName = new ArrayList<DigitalRadioContentOutgoingCallMoveStationName>();
    }
    this.outgoingCallMoveStationName.add(outgoingCallMoveStationNameItem);
    return this;
  }

  /**
   * Get outgoingCallMoveStationName
   * @return outgoingCallMoveStationName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=2)   public List<DigitalRadioContentOutgoingCallMoveStationName> getOutgoingCallMoveStationName() {
    return outgoingCallMoveStationName;
  }

  public void setOutgoingCallMoveStationName(List<DigitalRadioContentOutgoingCallMoveStationName> outgoingCallMoveStationName) {
    this.outgoingCallMoveStationName = outgoingCallMoveStationName;
  }

  public DigitalRadioContentWirelessChannelName incomingCallMoveStationName(List<DigitalRadioContentIncomingCallMoveStationName> incomingCallMoveStationName) {
    this.incomingCallMoveStationName = incomingCallMoveStationName;
    return this;
  }

  public DigitalRadioContentWirelessChannelName addIncomingCallMoveStationNameItem(DigitalRadioContentIncomingCallMoveStationName incomingCallMoveStationNameItem) {
    if (this.incomingCallMoveStationName == null) {
      this.incomingCallMoveStationName = new ArrayList<DigitalRadioContentIncomingCallMoveStationName>();
    }
    this.incomingCallMoveStationName.add(incomingCallMoveStationNameItem);
    return this;
  }

  /**
   * Get incomingCallMoveStationName
   * @return incomingCallMoveStationName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=2)   public List<DigitalRadioContentIncomingCallMoveStationName> getIncomingCallMoveStationName() {
    return incomingCallMoveStationName;
  }

  public void setIncomingCallMoveStationName(List<DigitalRadioContentIncomingCallMoveStationName> incomingCallMoveStationName) {
    this.incomingCallMoveStationName = incomingCallMoveStationName;
  }

  public DigitalRadioContentWirelessChannelName incomingCallTs(List<DigitalRadioContentIncomingCallTs> incomingCallTs) {
    this.incomingCallTs = incomingCallTs;
    return this;
  }

  public DigitalRadioContentWirelessChannelName addIncomingCallTsItem(DigitalRadioContentIncomingCallTs incomingCallTsItem) {
    if (this.incomingCallTs == null) {
      this.incomingCallTs = new ArrayList<DigitalRadioContentIncomingCallTs>();
    }
    this.incomingCallTs.add(incomingCallTsItem);
    return this;
  }

  /**
   * Get incomingCallTs
   * @return incomingCallTs
   **/
  @Schema(description = "")
      @Valid
  @Size(max=2)   public List<DigitalRadioContentIncomingCallTs> getIncomingCallTs() {
    return incomingCallTs;
  }

  public void setIncomingCallTs(List<DigitalRadioContentIncomingCallTs> incomingCallTs) {
    this.incomingCallTs = incomingCallTs;
  }

  public DigitalRadioContentWirelessChannelName blinkSetting(List<BlinkSetting> blinkSetting) {
    this.blinkSetting = blinkSetting;
    return this;
  }

  public DigitalRadioContentWirelessChannelName addBlinkSettingItem(BlinkSetting blinkSettingItem) {
    if (this.blinkSetting == null) {
      this.blinkSetting = new ArrayList<BlinkSetting>();
    }
    this.blinkSetting.add(blinkSettingItem);
    return this;
  }

  /**
   * Get blinkSetting
   * @return blinkSetting
   **/
  @Schema(description = "")
      @Valid
  @Size(max=2)   public List<BlinkSetting> getBlinkSetting() {
    return blinkSetting;
  }

  public void setBlinkSetting(List<BlinkSetting> blinkSetting) {
    this.blinkSetting = blinkSetting;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DigitalRadioContentWirelessChannelName digitalRadioContentWirelessChannelName = (DigitalRadioContentWirelessChannelName) o;
    return Objects.equals(this.displayText, digitalRadioContentWirelessChannelName.displayText) &&
        Objects.equals(this.textColor, digitalRadioContentWirelessChannelName.textColor) &&
        Objects.equals(this.backgroundColor, digitalRadioContentWirelessChannelName.backgroundColor) &&
        Objects.equals(this.outgoingCallMoveStationName, digitalRadioContentWirelessChannelName.outgoingCallMoveStationName) &&
        Objects.equals(this.incomingCallMoveStationName, digitalRadioContentWirelessChannelName.incomingCallMoveStationName) &&
        Objects.equals(this.incomingCallTs, digitalRadioContentWirelessChannelName.incomingCallTs) &&
        Objects.equals(this.blinkSetting, digitalRadioContentWirelessChannelName.blinkSetting);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor, backgroundColor, outgoingCallMoveStationName, incomingCallMoveStationName, incomingCallTs, blinkSetting);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DigitalRadioContentWirelessChannelName {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    outgoingCallMoveStationName: ").append(toIndentedString(outgoingCallMoveStationName)).append("\n");
    sb.append("    incomingCallMoveStationName: ").append(toIndentedString(incomingCallMoveStationName)).append("\n");
    sb.append("    incomingCallTs: ").append(toIndentedString(incomingCallTs)).append("\n");
    sb.append("    blinkSetting: ").append(toIndentedString(blinkSetting)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
