package com.contents.api;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import com.contents.common.CommonUtil;
import com.contents.common.bean.TemporaryContentData;
import com.contents.common.db.History;
import com.contents.manager.SystemSettingManager;
import com.contents.manager.TaskQueue;
import com.contents.model.ApiResult;
import com.contents.model.ContentBody;
import com.contents.model.ContentData;
import com.contents.model.ContentDataDisplayContentData;
import com.contents.service.SystemService;
import com.contents.service.UtilityService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class ContentApiController implements ContentApi {

	private static final Logger log = LoggerFactory.getLogger(ContentApiController.class);

	private final ObjectMapper objectMapper;

	private final HttpServletRequest request;

	private final SystemService systemService;

	private final UtilityService utilityService;

	private final TaskQueue taskQueue;

	@Autowired
	public ContentApiController(ObjectMapper objectMapper, HttpServletRequest request, SystemService systemService, UtilityService utilityService, TaskQueue taskQueue) {
		this.objectMapper = objectMapper;
		this.request = request;
		this.systemService = systemService;
		this.utilityService = utilityService;
		this.taskQueue = taskQueue;
	}

	public DeferredResult<ResponseEntity<ApiResult>> contentPost(@Parameter(in = ParameterIn.DEFAULT, description = "", required = true, schema = @Schema()) @Valid @RequestBody ContentBody body) {

		log.info("☆ contentPost start");

		String taskId;
		synchronized (taskQueue) {
			taskId = taskQueue.getTaskId("contentPost");
			taskQueue.print();
		}

		log.info("☆ contentPost taskId: {}", taskId);

		try {

			if (Objects.isNull(body))
				throw new Exception("RequestBody is null");

			History history = null;
			// 1, 履歴を保存
			try {
				history = systemService.saveApiBody2History(request, objectMapper.writeValueAsString(body));
			} catch (JsonProcessingException e) {
				log.error("contentPost: save history error", e);
			}

			DeferredResult<ResponseEntity<ApiResult>> deferredResult = utilityService.getResponseEntityDeferredResult(request.getRequestURI(), taskId, history, body);

			String accept = request.getHeader("Accept");
			if (accept == null || !accept.contains("application/json")) {
				deferredResult.setResult(utilityService.getApiResultNotJson(request.getRequestURI()));
				return deferredResult;
			}
			if (body.getData() == null || body.getData().getDisplayContentData() == null) {
				deferredResult.setResult(utilityService.getApiResultNoApiData(request.getRequestURI()));
				return deferredResult;
			}

			// APIを一つのTaskとして、IDを付けて管理する
			ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_SUCCESS);
			synchronized (taskQueue) {
				taskQueue.putDeferredResult(taskId, deferredResult);
				taskQueue.putApiResult(taskId, apiResult);
			}

			ContentData data = body.getData();

			if (Objects.isNull(data))
				throw new Exception("Content data is null");

			List<ContentDataDisplayContentData> displayContentData = data.getDisplayContentData();

			if (Objects.isNull(displayContentData))
				throw new Exception("Content display_content_data is null");

			List<TemporaryContentData> temporaries = new ArrayList<TemporaryContentData>();

			List<String> errors = new ArrayList<String>();

			log.info("● createTemporaryContentData start");

			{
				for (Iterator<ContentDataDisplayContentData> ite = displayContentData.iterator(); ite.hasNext();) {

					ContentDataDisplayContentData content = ite.next();

					try {

						List<TemporaryContentData> results = systemService.createTemporaryContentData(content);
						temporaries.addAll(results);

					} catch (Exception e) {

						Integer sourceNo = content.getSourceNo();
						Integer sourceSplitNo = content.getSourceSplitNo();
						log.error("sourceNo: {}, sourceSplitNo: {}", sourceNo, sourceSplitNo);
					}
				}
			}

			log.info("● createTemporaryContentData end");

			log.info("● convertVehicleSourceData start");

			{
				List<String> result = systemService.convertVehicleSourceData(temporaries);

				if (result != null)
					errors.addAll(result);
			}

			log.info("● convertVehicleSourceData end");

			log.info("● executeContent start");

			try {

				List<Future<List<String>>> futures = new ArrayList<>();

				ExecutorService executorService = CommonUtil.newFixedThreadPool();

				// 2, 最新コンテンツ保存
				for (Iterator<TemporaryContentData> ite = temporaries.iterator(); ite.hasNext();) {

					TemporaryContentData content = ite.next();
					java.util.concurrent.Callable<List<String>> task = () -> {
						List<String> results = systemService.executeContent(content, taskId);
						return results;
					};
					
					Future<List<String>> future = executorService.submit(task);
					futures.add(future);
				}
				
				executorService.shutdown();
				
				for (Future<List<String>> future : futures) {
					
					try {
						
						List<String> result = future.get();
						errors.addAll(result);

					} catch (InterruptedException | ExecutionException e) {

						log.error("error device execution", e);
					}
				}

			} catch (Exception e) {
				deferredResult.setResult(utilityService.getApiResultInternalError(request.getRequestURI(), e));
			}

			log.info("● executeContent end");

			if (errors.iterator().hasNext()) {
				apiResult.setResult(CommonUtil.API_RESULT_FAIL);
				apiResult.setMsg(StringUtils.join(errors, System.lineSeparator()));
			}

			return deferredResult;

		} catch (Exception e) {

			log.error("contentPost Error", e);

			Long apiTimeout = SystemSettingManager.getSystemSetting().getApiTimeout();
			ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_FAIL);
			apiResult.setMsg(e.getMessage());
			DeferredResult<ResponseEntity<ApiResult>> deferredResult = new DeferredResult<>(apiTimeout, new ResponseEntity<>(apiResult, HttpStatus.INTERNAL_SERVER_ERROR));
			return deferredResult;

		} finally {

			synchronized (taskQueue) {
				taskQueue.taskSendMsgDone(taskId);
			}

			log.info("☆ contentPost end");
		}
	}

}
