package com.contents.configuration;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import com.contents.common.external.initial.Display;
import com.contents.common.external.initial.DisplaySplit;
import com.contents.common.external.initial.External;
import com.contents.common.external.initial.VideoSource;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 外部設定情報
 */
@Component
@ConfigurationProperties(prefix = "app.configuration.external")
public class ExternalConfiguration {
    public final int DISPLAY_TYPE_MULTI_MONITOR = 0;
    public final int DISPLAY_TYPE_SINGLE_MONITOR = 1;

    /**
     * HDMI入力
     * (表示制御情報更新通知-input_switch と 映像系AP-外部設定情報 の紐付け)
     */
    public enum HdmiInput {
        MR_PC(0),
        MATRIX_SW(1);

        private final int id;

        HdmiInput(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public static HdmiInput valueOf(int id) {
            return Arrays.stream(HdmiInput.values())
                    .filter(data -> data.getId() == id)
                    .findFirst()
                    .orElse(null);
        }
    }

    private String filename;
    private Boolean machineControlEnable;
    private External external;

    public void setFilename(String filename) throws IOException {
        this.filename = filename;

        if (this.filename.contains("/")) {
        	ObjectMapper mapper = new ObjectMapper();
            File json = new File(filename);
            setExternal(mapper.readValue(json, External.class));	
        } else {
        	ObjectMapper mapper = new ObjectMapper();
            File json = new ClassPathResource(filename).getFile();
            setExternal(mapper.readValue(json, External.class));
        }
    }
    public String getFilename() {
        return filename;
    }

//    public void setMachineControlEnable(Boolean machineControlEnable) {
//        this.machineControlEnable = machineControlEnable;
//    }
//    public Boolean getMachineControlEnable() {
//        return machineControlEnable;
//    }

    public void setExternal(External external) {
        this.external = external;
    }
    public External getExternal() {
        return external;
    }


    /**
     * 全表示盤情報 取得
     * @param displayType 表示盤種別
     * @return 表示盤情報 (無ければ ListSize=0)
     */
    public List<Display> getAllDisplay(int displayType) {
        return external.getDisplayList().stream()
                .filter(d -> Objects.equals(d.getDisplayType(), displayType)).toList();
    }

    /**
     * 表示盤情報 取得
     * @param displayNo 表示盤番号
     * @return 表示盤情報 (無ければ null)
     */
    public Display getDisplay(int displayNo) {
        return external.getDisplayList().stream()
                .filter(d -> Objects.equals(d.getDisplayNo(), displayNo))
                .findFirst()
                .orElse(null);
    }

    /**
     * 表示盤情報 取得
     * @param displayNo 表示盤番号
     * @param displayType 表示盤種別
     * @return 表示盤情報 (無ければ null)
     */
    public Display getDisplay(int displayNo, int displayType) {
        return external.getDisplayList().stream()
                .filter(d ->
                        ((Objects.equals(d.getDisplayType(), displayType))
                                && (Objects.equals(d.getDisplayNo(), displayNo)))
                )
                .findFirst()
                .orElse(null);
    }

    /**
     * 画面分割(面)情報リスト 取得
     * @param display 表示盤情報
     * @return 画面分割(面)情報 (無ければ ListSize=0)
     */
    public List<DisplaySplit> getDisplaySplitList(Display display) {
        List<DisplaySplit> dispSplitList = new ArrayList<>();
        if (display != null) {
            dispSplitList = display.getDisplaySplitList().stream()
                    .filter(ds -> ((ds.getIpAddress() != null)
                            && (ds.getPort() != null)
                    )).toList();
        }
        return dispSplitList;
    }

    /**
     * 画面分割(面)情報 取得
     * @param display 表示盤情報
     * @param displaySplitNo 画面分割番号
     * @return 画面分割(面)情報 (無ければ null)
     */
    public DisplaySplit getDisplaySplit(Display display, int displaySplitNo) {
        DisplaySplit dispSplit = null;
        if (display != null) {
            dispSplit = display.getDisplaySplitList().stream()
                    .filter(ds -> ((Objects.equals(ds.getDisplaySplitNo(), displaySplitNo))
                            && (ds.getIpAddress() != null)
                            && (ds.getPort() != null)
                    ))
                    .findFirst()
                    .orElse(null);
        }
        return dispSplit;
    }

    /**
     * 映像入力情報 取得
     * @param sourceNo ソース番号
     * @return 映像入力情報 (無ければ null)
     */
    public VideoSource getVideoSource(int sourceNo) {
        return external.getMatrixSw().getVideoSourceList().stream()
                .filter(d -> Objects.equals(d.getVideoSourceNo(), sourceNo))
                .findFirst()
                .orElse(null);
    }

}