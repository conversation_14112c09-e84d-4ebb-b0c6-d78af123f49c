package com.contents.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.MessageSource;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.async.DeferredResult;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.bean.ContentInfo;
import com.contents.common.bean.Result;
import com.contents.common.bean.ScreenInfo;
import com.contents.common.bean.ScreenLayout;
import com.contents.common.bean.SoundInfo;
import com.contents.common.bean.SourceNo;
import com.contents.common.bean.TemporaryContentData;
import com.contents.common.db.Content;
import com.contents.common.db.ContentDao;
import com.contents.common.db.ContentPK;
import com.contents.common.db.Control;
import com.contents.common.db.ControlDao;
import com.contents.common.db.History;
import com.contents.common.db.HistoryDao;
import com.contents.common.db.Monitor;
import com.contents.common.db.MonitorDao;
import com.contents.common.db.MonitorPK;
import com.contents.common.db.RetryJpaOptimisticLock;
import com.contents.common.db.SoundDao;
import com.contents.composition.MachineComposition;
import com.contents.configuration.ExternalConfiguration;
import com.contents.manager.CompositionManager;
import com.contents.manager.KeyLockManager;
import com.contents.manager.SystemSettingManager;
import com.contents.manager.TaskQueue;
import com.contents.model.ContentDataDisplayContentData;
import com.contents.model.ControlMonitorDisplayContentData;
import com.contents.model.ControlMonitorDisplayControlData;
import com.contents.model.ControlMonitorDisplayDetailsSplitData;
import com.contents.model.ControlMonitorDisplaySplitData;
import com.contents.model.ControlSoundDisplaySoundRingData;
import com.contents.model.CustomVehicleContent;
import com.contents.model.SyncTime;
import com.contents.model.SyncTimeTime;
import com.contents.model.VehicleContent;
import com.contents.pojo.MatrixIndexConverter;
import com.contents.pojo.MatrixIndexInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 映像系AP全体に向けて、重要な処理をここで纏める<br>
 * Lomlokの機能を利用して、ConstructorでSpringBootから各private final のFieldを初期化する
 */
@Service
@Data
@AllArgsConstructor
@Slf4j
public class SystemService {
	// ConstructorでSpringBootから各private final のFieldを初期化する
	private final ContentDao contentDao;

	private final MonitorDao monitorDao;

	private final ControlDao controlDao;

	private final SoundDao soundDao;

	private final HistoryDao historyDao;

	private final SimpMessagingTemplate messagingTemplate;

	private final ScheduleService scheduleService;

	private final UtilityService utilityService;

	private final ObjectMapper objectMapper;

	private final TaskQueue taskQueue;

	private final MessageSource messageSource;

	private final MachineControlService controlService;

	private final ExternalConfiguration externalConfiguration;

	private final KeyLockManager keyLockManager;

	/**
	 * 3.1 表示盤制御APIを処理
	 *
	 * @param controlData API形式の制御データリスト
	 * @param taskId      Task Id
	 * @return エラー情報リストを含む処理結果
	 */
	@RetryJpaOptimisticLock(times = 201)
	public List<String> executeMonitorControl(List<ControlMonitorDisplayControlData> controlData, String taskId) {

		List<String> errors = new ArrayList<String>();

		List<TemporaryContentData> temporaries = createTemporaryContentData(controlData);

		// 1. 制御情報をDBに保存
		{
			List<String> results = saveControlInfo(temporaries);
			if (results != null)
				errors.addAll(results);
		}

		//　2．制御を実施
		{
			List<String> results = controlExecute(temporaries, taskId);
			if (results != null)
				errors.addAll(results);
		}

		return errors;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	protected List<String> saveControlInfo(List<TemporaryContentData> temporaries) {

		List<String> errors = new ArrayList<String>();

		Map<String, List<TemporaryContentData>> displayGroup = temporaries.stream().collect(Collectors.groupingBy(g -> g.getDisplayNo() + "-" + g.getDisplaySplitNo()));

		for (Iterator<Map.Entry<String, List<TemporaryContentData>>> displayGroupIterator = displayGroup.entrySet().iterator(); displayGroupIterator.hasNext();) {

			Map.Entry<String, List<TemporaryContentData>> splitGroupEntry = displayGroupIterator.next();

			String key = splitGroupEntry.getKey();

			List<TemporaryContentData> monitors = splitGroupEntry.getValue();
			TemporaryContentData entity = Collections.max(monitors, Comparator.comparingInt(TemporaryContentData::getSourceDisplayPattern));

			Integer displayNo = entity.getDisplayNo();
			Integer displaySplitNo = entity.getDisplaySplitNo();

			Integer newSourceDisplayPattern = entity.getSourceDisplayPattern();
			Integer currentSourceDisplayPattern = controlDao.selectMaxSourceDispPat(displayNo, displaySplitNo);

			if (!Objects.equals(currentSourceDisplayPattern, newSourceDisplayPattern)) {
				controlDao.deleteByDisplayNoAndDisplaySplitNo(displayNo, displaySplitNo);
			}

			monitors.forEach(obj -> obj.setSourceDisplayPattern(newSourceDisplayPattern));

			Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(displayNo, displaySplitNo));

			{
				String error = checkSetupMonitor(displayNo, displaySplitNo, monitorOpt);

				if (!StringUtils.isEmpty(error))
					errors.add(error);
			}

			if (!monitors.stream().anyMatch(d -> d.getSourceDisplayPattern() == 1))
				continue;

			List<Control> dbControls = controlDao.findByDisplayNoAndDisplaySplitNo(displayNo, displaySplitNo);

			for (TemporaryContentData monitor : monitors) {

				dbControls.removeIf(item -> item.getDisplay_details_split_no() == monitor.getDisplayDetailsSplitNo());
			}

			for (Control control : dbControls) {
				TemporaryContentData temp = createTemporaryContentData(control);
				monitors.add(temp);
			}
			
			List<String> results = validateVehicleContent(key, monitors);
			if (!results.isEmpty()) {
				temporaries.removeAll(monitors);
				errors.addAll(results);
			} else {
				temporaries.removeAll(monitors);
				temporaries.addAll(monitors);
			}
		}

		for (TemporaryContentData temporary : temporaries) {

			Control dbControl = temporary.getDbControl();
			dbControl.setStatus(CommonUtil.STATUS_RECEIVED);
			dbControl.setReceive_time(new Date());
			dbControl.setUpdate_time(new Date());
			controlDao.save(dbControl);
		}

		return errors;
	}

	private List<String> validateVehicleContent(String key, List<TemporaryContentData> monitors) {

		List<String> errors = new ArrayList<String>();

		// Filter for both SourceNo=1 (32-line vehicle content) and SourceNo=18 (50-line extended vehicle content)
		monitors = monitors.stream().filter(item -> item.getSourceNo() == 1 || item.getSourceNo() == 18).toList();

		if (monitors.isEmpty())
			return errors;

		// 左上、左下、右上、右下の順に0-3の番号を振った数値を取り出す
		Integer[] columnMajorIndexs = monitors.stream().map(monitor -> monitor.getMatrixIndexInfo().getColumnPriorityIndex()).sorted((a, b) -> a - b).toArray(Integer[]::new);

		Integer[] ddsNum = monitors.stream().map(monitor -> monitor.getDisplayDetailsSplitNo()).sorted((a, b) -> a - b).toArray(Integer[]::new);

		log.info("monitor split number [ {} ] [ {} ]",
				Arrays.stream(ddsNum).map(String::valueOf).collect(Collectors.joining(", ")),
				Arrays.stream(columnMajorIndexs).map(String::valueOf).collect(Collectors.joining(", ")));

		// 許容する車両コンテンツの配置パターン
		List<Integer[]> validateNumber = new ArrayList<Integer[]>() {
			{
				add(new Integer[] { 0, 1, 2, 3 });
				add(new Integer[] { 0, 1, 2 });
				add(new Integer[] { 0, 1 });
				add(new Integer[] { 2, 3 });
				add(new Integer[] { 0 });
				add(new Integer[] { 1 });
				add(new Integer[] { 2 });
				add(new Integer[] { 3 });
			}
		};

		boolean match = validateNumber.stream().anyMatch(idxs -> Arrays.equals(columnMajorIndexs, idxs));

		if (!match) {

			Object[] params = new Object[] {
					key,
					Arrays.stream(ddsNum).map(String::valueOf).collect(Collectors.joining(", "))
			};

			String message = MessageFormatUtil.format("サポートしていない車両コンテンツの配置 表示盤：{}, 詳細番号：{}", params);
			errors.add(message);

		}

		return errors;

	}

	protected String loadContent(TemporaryContentData temporary) {

		Integer displayNo = temporary.getDisplayNo();
		Integer displaySplitNo = temporary.getDisplaySplitNo();
		Integer displayDetailsSplitNo = temporary.getDisplayDetailsSplitNo();

		Integer sourceDisplayPattern = temporary.getSourceDisplayPattern();

		int splitSize = (sourceDisplayPattern == 1) ? 4 : 1;

		MatrixIndexInfo matrixIndexInfo = temporary.getMatrixIndexInfo();

		if (splitSize == 1 && matrixIndexInfo.getRowPriorityIndex() != 0) {

			// 非分割表示で表示位置データが左上では無い場合はエラー
			final Object[] params = { displayDetailsSplitNo, };
			return messageSource.getMessage("msg.api.error.wrong.detail.split.no", params, Locale.JAPANESE);
		}

		Integer displayType = temporary.getDisplayType();

		if (displayType == 1 && !ArrayUtils.contains(new Integer[] { 0, 1, 2, 3 }, displayDetailsSplitNo)) {

			// 単面モニターで「0, 1, 2, 3」以外の詳細番号の場合はエラー
			final Object[] params = { displayType, displayDetailsSplitNo };
			return messageSource.getMessage("msg.api.error.wrong.detail.split.no.single.monitor", params, Locale.JAPANESE);
		}

		Integer sourceNo = temporary.getSourceNo();
		Integer sourceSplitNo = temporary.getSourceSplitNo();

		log.info("displayNo: {}, displaySplitNo: {}, displayDetailsSplitNo: {}, sourceNo: {}, sourceSplitNo: {}",
				displayNo, displaySplitNo, displayDetailsSplitNo, sourceNo, sourceSplitNo);

		Optional<Content> contentOptional = contentDao.findById(new ContentPK(sourceNo, sourceSplitNo));

		if (contentOptional.isPresent()) {

			Content dbContent = contentOptional.get();
			temporary.setDbContent(dbContent);
		}

		return null;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	protected List<String> controlExecute(List<TemporaryContentData> temporaries, String taskId) {

		log.info("☆ controlExecute start");

		try {

			List<String> errors = new ArrayList<String>();

			Map<String, List<TemporaryContentData>> displayGroup = temporaries.stream().collect(Collectors.groupingBy(g -> g.getDisplayNo() + "-" + g.getDisplaySplitNo()));

			for (Iterator<Map.Entry<String, List<TemporaryContentData>>> ite = displayGroup.entrySet().iterator(); ite.hasNext();) {

				Map.Entry<String, List<TemporaryContentData>> entry = ite.next();
				List<TemporaryContentData> list = entry.getValue();

				DeferredResult<Boolean> deferredResult = sendMonitorControl(list, taskId);

				waitMonitorSetupDone(deferredResult);
			}

			for (TemporaryContentData temporary : temporaries) {

				String error = loadContent(temporary);
				if (!StringUtils.isEmpty(error))
					errors.add(error);
			}

			{
				List<String> results = convertVehicleSourceData(temporaries);

				if (results != null)
					errors.addAll(results);
			}

			List<Future<String>> futures = new ArrayList<>();
			ExecutorService executorService = CommonUtil.newFixedThreadPool();

			for (TemporaryContentData temporary : temporaries) {

				java.util.concurrent.Callable<String> task = () -> {
					String resultMsg = sendContent2Browser(temporary, taskId);
					return resultMsg;
				};
				
				Future<String> future = executorService.submit(task);
				futures.add(future);
			}
			
			executorService.shutdown();

			boolean machineControlEnable = SystemSettingManager.getSystemSetting().getMachineControlEnable();

			if (machineControlEnable) {
				
				List<Control> controlList = temporaries.stream().map(TemporaryContentData::getDbControl).toList();

				// 表示盤制御情報のAPIを丸ごとで、制御を実施
				List<String> errorStringList = controlService.machineControl(controlList);

				if (errorStringList.isEmpty())
					return errors;

				// 機器制御エラー
				String errmsg = messageSource.getMessage("msg.control.monitor.machine.control.error", null, Locale.JAPANESE);

				errors.add(errmsg);
				errors.addAll(errorStringList);
			}
			
			for (Future<String> future : futures) {
				
				try {

					String result = future.get();
					errors.add(result);

				} catch (InterruptedException | ExecutionException e) {

					log.error("error sendContent2Browser execution", e);
				}
			}
			

			return errors;

		} finally {
			log.info("☆ controlExecute end");
		}
	}

	//	/**
	//	 * 3.1 表示盤制御APIを処理
	//	 *
	//	 * @param controlData API形式の制御データリスト
	//	 * @param taskId      Task Id
	//	 * @return エラー情報リストを含む処理結果
	//	 */
	//	@RetryJpaOptimisticLock(times = 201)
	//	public Result executeMonitorControl(List<ControlMonitorDisplayControlData> controlData, String taskId) {
	//		// 1. 制御情報をDBに保存
	//		List<Control> resultList = saveControlInfo2Db(controlData);
	//		//　2．制御を実施
	//		return controlExecute(resultList, taskId);
	//	}

	/**
	 * 3.2表示盤喚起音吹鳴を処理
	 *
	 * @param controlData API形式の制御データリスト
	 * @param taskId      Task Id
	 * @return 処理結果 True：成功、False：失敗
	 */
	@RetryJpaOptimisticLock(times = 201)
	@Transactional
	public Result executeSoundControl(ControlSoundDisplaySoundRingData controlData, String taskId) {
		return soundExecute(controlData, taskId);
	}

	@RetryJpaOptimisticLock(times = 201)
	@Transactional
	public List<TemporaryContentData> createTemporaryContentData(ContentDataDisplayContentData content) {

		List<TemporaryContentData> results = new ArrayList<TemporaryContentData>();

		Integer sourceNo = content.getSourceNo();
		Integer sourceSplitNo = content.getSourceSplitNo();
		String sourceName = content.getSourceName();

		try {

			//Content dbContent = createSaveContent2Db(content);
			Content dbContent = saveContent2Db(content);

			List<Control> controlList = controlDao.findBySourceNoAndSourceSplitNo(content.getSourceNo(), content.getSourceSplitNo());

			for (Control control : controlList) {

				Integer displayType = control.getDisplay_type();
				Integer displayNo = control.getDisplay_no();
				Integer displaySplitNo = control.getDisplay_split_no();
				Integer displayDetailsSplitNo = control.getDisplay_details_split_no();
				Integer sourceDisplayPattern = control.getSource_disp_pat();

				MatrixIndexConverter converter = displayType == 1 ? new MatrixIndexConverter(1, 1, 2, 2) : new MatrixIndexConverter(2, 2, 2, 2);

				MatrixIndexInfo matrixIndexInfo = converter.convertMatrixIndex(displayDetailsSplitNo);

				Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(displayNo, displaySplitNo));

				TemporaryContentData temporary = new TemporaryContentData();

				temporary.setSourceNo(sourceNo);
				temporary.setSourceSplitNo(sourceSplitNo);
				temporary.setSourceName(sourceName);

				temporary.setDisplayType(displayType);
				temporary.setDisplayNo(displayNo);
				temporary.setDisplaySplitNo(displaySplitNo);
				temporary.setDisplayDetailsSplitNo(displayDetailsSplitNo);
				temporary.setSourceDisplayPattern(sourceDisplayPattern);

				temporary.setMatrixIndexInfo(matrixIndexInfo);

				temporary.setDbContent(dbContent);
				temporary.setDbControl(control);

				if (!monitorOpt.isEmpty()) {
					temporary.setMonitor(monitorOpt.get());
				}

				results.add(temporary);
			}

		} catch (Exception e) {
			log.error("saveContentsData error", e);
		}

		return results;
	}

	//	/**
	//	 * 3.3 以後のコンテンツ情報を処理
	//	 *
	//	 * @param content Content内容
	//	 * @param taskId  Task Id
	//	 * @return エラー情報リストを含む処理結果
	//	 * @throws JsonProcessingException
	//	 */
	//	@RetryJpaOptimisticLock(times = 201)
	//	@Transactional
	//	public List<String> executeContent(ContentDataDisplayContentData content, String taskId) throws JsonProcessingException {
	//
	//		log.info("☆ executeContent start");
	//
	//		List<String> errors = new ArrayList<String>();
	//
	//		try {
	//			Content dbContent = saveContent2Db(content);
	//
	//			List<Control> controlList = controlDao.findBySourceNoAndSourceSplitNo(content.getSourceNo(), content.getSourceSplitNo());
	//
	//			if ((CollectionUtils.isEmpty(controlList)))
	//				return errors;
	//
	//			for (Control control : controlList) {
	//
	//				Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(control.getDisplay_no(), control.getDisplay_split_no()));
	//
	//				if (monitorOpt.isEmpty() || monitorOpt.get().getStatus() != CommonUtil.MONITOR_STATUS_CONNECTED) {
	//					// 表示盤未セットアップの場合、コンテンツ表示をスキップ
	//					continue;
	//				}
	//
	//				sendContent(control, dbContent, taskId);
	//			}
	//
	//			ArrayUtils.isEmpty(new String[] {});
	//
	//			return errors;
	//
	//		} catch (Exception e) {
	//
	//			return errors;
	//
	//		} finally {
	//
	//			log.info("☆ executeContent end");
	//		}
	//	}

	/**
	 * 3.3 以後のコンテンツ情報を処理
	 *
	 * @param content Content内容
	 * @param taskId  Task Id
	 * @return エラー情報リストを含む処理結果
	 * @throws JsonProcessingException
	 */
	@RetryJpaOptimisticLock(times = 201)
	@Transactional
	public List<String> executeContent(TemporaryContentData temporary, String taskId) throws JsonProcessingException {

		log.info("☆ executeContent start");

		List<String> errors = new ArrayList<String>();

		if (!temporary.isMonitorReady())
			return errors;

		try {

			//			Content dbContent = temporary.getDbContent();
			//			saveContent2Db(dbContent);

			sendContent(temporary, taskId);

			return errors;

		} catch (Exception e) {

			log.error("", e);
			return errors;

		} finally {

			log.info("☆ executeContent end");
		}
	}

	/**
	 * 3.28時刻同期APIを処理
	 *
	 * @param syncTime SyncTimeのObject
	 * @return 処理結果 True：成功、False：失敗
	 */
	@RetryJpaOptimisticLock(times = 201)
	@Transactional
	public boolean executeTimeSync(SyncTime syncTime) throws JsonProcessingException {
		// 時刻コンテンツ内容として処理
		saveTimeContent2Db(syncTime.getTime());

		// currentTimeServiceにMonitorに通知するよう
		var controlList = controlDao.findBySourceNo(SourceNo.Now.getSourceNo());
		if (!CollectionUtils.isEmpty(controlList)) {
			controlList.forEach((control) -> {
				ScreenInfo screenInfo = new ScreenInfo();
				screenInfo.setDisplayNo(control.getDisplay_no());
				screenInfo.setSplitNo(control.getDisplay_split_no());
				Integer afterDetailSplitNo = control.getDisplay_details_split_no();
				// 単面表示盤の場合は画面分割番号を変換する (Frontendは4面マルチの画面分割番号指定で動作する)
				if (control.getDisplay_type() == 1) {
					afterDetailSplitNo = switch (afterDetailSplitNo) {
					case 2 -> 4;
					case 3 -> 5;
					default -> afterDetailSplitNo;
					};
				}
				screenInfo.setDetailSplitNo(afterDetailSplitNo);

				ContentInfo contentInfo = new ContentInfo(control.getSource_no(), control.getSource_split_no(), afterDetailSplitNo);
				//contentInfo.setSourceData(syncTime);
				
				scheduleService.registerTimeContentScreenInfo(screenInfo, contentInfo, syncTime.getTime());
			});
		}

		boolean result = true;

		boolean machineControlEnable = SystemSettingManager.getSystemSetting().getMachineControlEnable();

		if (machineControlEnable) {
			result = controlService.timeSync(syncTime);
		}

		//		if (externalConfiguration.getMachineControlEnable()) {
		//			result = controlService.timeSync(syncTime);
		//		}

		return result;
	}

	/**
	* Monitorのパソコンから、Urlでサーバーをアクセスすることは、モニターセットアップとみなす。
	*
	* @param displayNo 　表示盤番号
	* @param splitNo   　面番号。分割番号を要らない
	* @return 固定でTrueを返す
	*/
	@RetryJpaOptimisticLock(times = 201)
	@Transactional
	public boolean monitorSetup(Integer displayNo, Integer splitNo) {

		Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(displayNo, splitNo));
		if (monitorOpt.isPresent()) {
			if (monitorOpt.get().getStatus() != CommonUtil.MONITOR_STATUS_OFFLINE) {
				log.info("monitorSetup: monitor(displayNo={}, splitNo={}) has updated early.", displayNo, splitNo);
				return false;
			}
		}
		// 1. Monitory情報をDBに保存
		Monitor monitor = updateMonitorStatus(displayNo, splitNo, CommonUtil.MONITOR_STATUS_CONNECTED, monitorOpt.orElse(null));

		Integer displayNumber = monitor.getDisplay_no();
		Integer displaySplitNumber = monitor.getDisplay_split_no();

		// 2、表示盤制御
		List<Control> controlList = controlDao.findByDisplayNoAndDisplaySplitNo(displayNumber, displaySplitNumber);
		if (CollectionUtils.isEmpty(controlList)) {
			log.info("monitorSetup: controlList is empty");
			return true;
		}

		// ソース表示パターンを参照して表示制御を実行
		// ソース表示パターンは複数指定できるため、優先順位を以下の通りとする
		// 1面表示 (0) < 1/4面表示 (1) < 1面表示 (2) < 4面表示 (3)
		int dispPat = 0;
		for (Control ctrl : controlList) {
			dispPat = Math.max(dispPat, ctrl.getSource_disp_pat());
		}
		int splitSize = (dispPat == 1) ? 4 : 1;

		DeferredResult<Boolean> deferredResult = sendMonitorControl(splitSize, monitor.getDisplay_no(), monitor.getDisplay_split_no(), null, null);

		waitMonitorSetupDone(deferredResult);

		//　3．機器制御
		machineControl(monitor);

		List<TemporaryContentData> temporaries = new ArrayList<TemporaryContentData>();

		for (Control control : controlList) {

			TemporaryContentData temporary = createTemporaryContentData(control);
			temporaries.add(temporary);
			loadContent(temporary);
		}

		{
			List<String> results = convertVehicleSourceData(temporaries);

			if (results != null)
				StringUtils.join(results, System.lineSeparator());
		}

		//　4．コンテンツを送り出す
		List<Future<String>> futures = new ArrayList<>();
		ExecutorService executorService = CommonUtil.newFixedThreadPool();

		for (TemporaryContentData temporary : temporaries) {

			java.util.concurrent.Callable<String> task = () -> {
				String resultMsg = sendContent2Browser(temporary, null);
				return resultMsg;
			};

			Future<String> future = executorService.submit(task);
			futures.add(future);
		}
		
		executorService.shutdown();

		for (Future<String> future : futures) {
			
			try {

				String result = future.get();
				log.error(result);

			} catch (InterruptedException | ExecutionException e) {

				log.error("error sendContent2Browser execution", e);
			}
		}

		return true;
	}

	/**
	 * APIの履歴情報を保存<br>
	 * 他のDB処理にRuntimeExceptionがあるかどうかに関係なく、<br>
	 * 独立なTransactionで、DB保存を成功させる
	 *
	 * @param request API Request
	 * @param apiBody API Body
	 * @return DB保存したHistory
	 */
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public History saveApiBody2History(HttpServletRequest request, String apiBody) {
		History his = new History();
		his.setApi_path(String.format("%s:%s", request.getMethod(), request.getServletPath()));
		his.setReceive_time(new Date());
		his.setClient_info(CommonUtil.getClientInfo(request));
		if (StringUtils.isNotEmpty(apiBody)) {
			his.setApi_body(apiBody);
		}
		return historyDao.save(his);
	}

	/**
	 * 指定するMonitorをOfflineにする
	 *
	 * @param displayNo 　表示盤番号
	 * @param splitNo   　面番号
	 * @return 処理結果。True：成功、False：失敗
	 */
	public boolean monitorDown(Integer displayNo, Integer splitNo) {
		// 1. Monitory情報をDBに保存
		updateMonitorStatus(displayNo, splitNo, CommonUtil.MONITOR_STATUS_OFFLINE, null);

		return true;
	}

	// これからLocal用Private関数

	/**
	 * Content内容をDBに保存
	 *
	 * @param content Content内容
	 * @return Contentテーブルの一行Record
	 * @throws JsonProcessingException
	 */
	private Content saveContent2Db(Content dbContent) throws JsonProcessingException {
		// ContentをDB保存
		dbContent = contentDao.save(dbContent);
		return dbContent;
	}

	/**
	 * Content内容をDBに保存
	 *
	 * @param content Content内容
	 * @return Contentテーブルの一行Record
	 * @throws JsonProcessingException
	 */
	private Content saveContent2Db(ContentDataDisplayContentData content) throws JsonProcessingException {
		// ContentをDB保存
		Content dbContent = new Content();
		dbContent.setPk(new ContentPK(content.getSourceNo(), content.getSourceSplitNo()));
		dbContent.setSource_name(content.getSourceName());
		dbContent.setStatus(CommonUtil.STATUS_RECEIVED);
		dbContent.setReceive_time(new Date());
		dbContent.setUpdate_time(new Date());
		dbContent.setSource_data(objectMapper.writeValueAsString(content.getSourceData()));
		dbContent = contentDao.save(dbContent);
		return dbContent;
	}

	/**
	 * Content内容をDBに保存
	 *
	 * @param content Content内容
	 * @return Contentテーブルの一行Record
	 * @throws JsonProcessingException
	 */
	private Content createSaveContent2Db(ContentDataDisplayContentData content) throws JsonProcessingException {
		// ContentをDB保存
		Content dbContent = new Content();
		dbContent.setPk(new ContentPK(content.getSourceNo(), content.getSourceSplitNo()));
		dbContent.setSource_name(content.getSourceName());
		dbContent.setStatus(CommonUtil.STATUS_RECEIVED);
		dbContent.setReceive_time(new Date());
		dbContent.setUpdate_time(new Date());
		dbContent.setSource_data(objectMapper.writeValueAsString(content.getSourceData()));
		return dbContent;
	}

	/**
	 * MonitorのSetup完了を待ち
	 *
	 * @param deferredResult MonitorのSetup完了のDeferredResult
	 */
	private void waitMonitorSetupDone(DeferredResult<Boolean> deferredResult) {
		int timeout = 3000; // ブラウザ応答最大待ち時間(ms指定)
		int interval = 500; // 応答確認間隔(ms指定)
		while (!deferredResult.hasResult()) {
			try {
				if (timeout <= 0) {
					log.warn("No response from monitor.");
					break;
				}
				Thread.sleep(interval);
				timeout = timeout - interval;
			} catch (InterruptedException e) {
				throw new RuntimeException(e);
			}
		}
	}

	/**
	 * 指定のStatusにモニターステータス更新
	 *
	 * @param boardId 表示盤ID
	 * @param monitorId 面ID
	 * @param status MonitorのStatus
	 * @return DB保存のMonitor
	 */
	private Monitor updateMonitorStatus(Integer boardId, Integer monitorId, Integer status, Monitor monitor) {
		Date now = new Date();
		if (monitor == null) {
			monitor = new Monitor();
			monitor.setDisplay_no(boardId);
			monitor.setDisplay_split_no(monitorId);
		}
		monitor.setStatus(status);
		monitor.setReceive_time(now);
		monitor.setUpdate_time(now);
		return monitorDao.save(monitor);
	}

	//	/**
	//	 * 3.1の表示盤制御情報に従い、各制御を実施
	//	 *
	//	 * @param controlList DB保存済みの各表示盤制限情報
	//	 * @param taskId      　Task Id
	//	 */
	//	@Transactional(propagation = Propagation.REQUIRES_NEW)
	//	protected Result controlExecute(List<Control> controlList, String taskId) {
	//
	//		log.info("☆ controlExecute start");
	//
	//		Result result = new Result();
	//		Map<Integer, Map<Integer, Map<Integer, Control>>> sourceMgtMap = getSourceMgtMap(controlList);
	//
	//		sourceMgtMap.forEach((displayNo, value) -> {
	//			for (Map.Entry<Integer, Map<Integer, Control>> splitSet : value.entrySet()) {
	//
	//				Integer displaySplitNo = splitSet.getKey();
	//				log.info("displayNo: " + displayNo + ", displaySplitNo: " + displaySplitNo);
	//
	//				Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(displayNo, splitSet.getKey()));
	//
	//				//				if (monitorOpt.isEmpty() || monitorOpt.get().getStatus() != CommonUtil.MONITOR_STATUS_CONNECTED) {
	//				//					// 表示盤未セットアップの場合、処理Skipして、エラーメッセージを返す
	//				//					final Object[] params = { displayNo, displaySplitNo };
	//				//					String errmsg = messageSource.getMessage("msg.control.monitor.no.setup", params, Locale.JAPANESE);
	//				//					log.warn("displayNo:{}, msg:{}", displayNo, errmsg);
	//				//					result.getErrMsgList().add(errmsg);
	//				//					continue;
	//				//				}
	//
	//				String errmsg = checkSetupMonitor(displayNo, displaySplitNo, monitorOpt);
	//
	//				if (!StringUtils.isEmpty(errmsg)) {
	//					result.getErrMsgList().add(errmsg);
	//					continue;
	//				}
	//
	//				//表示盤制御
	//				Control control = getFirstSplitScreenControl(splitSet);
	//				if (control != null) {
	//					control.setStatus(CommonUtil.STATUS_SEND2BROWSER);
	//					control.setUpdate_time(new Date());
	//					controlDao.save(control);
	//				}
	//
	//				// ソース表示パターンを参照して表示制御を実行
	//				// ソース表示パターンは複数指定できるため、優先順位を以下の通りとする
	//				// 1面表示 (0) < 1/4面表示 (1) < 1面表示 (2) < 4面表示 (3)
	//				int dispPat = 0;
	//				for (Map.Entry<Integer, Control> split : splitSet.getValue().entrySet()) {
	//					dispPat = Math.max(dispPat, split.getValue().getSource_disp_pat());
	//				}
	//				int splitSize = (dispPat == 1) ? 4 : 1;
	//				DeferredResult<Boolean> deferredResult = sendMonitorControl(splitSize, displayNo, splitSet.getKey(), taskId, control);
	//
	//				waitMonitorSetupDone(deferredResult);
	//
	//				// コンテンツ表示
	//				splitSet.getValue().forEach((key, ctrl) -> {
	//
	//					String resultMsg = sendContent2Browser(splitSize, ctrl, taskId);
	//					if (StringUtils.isNotEmpty(resultMsg)) {
	//						result.getErrMsgList().add(resultMsg);
	//					}
	//				});
	//			}
	//		});
	//
	//		boolean machineControlEnable = SystemSettingManager.getSystemSetting().getMachineControlEnable();
	//
	//		if (machineControlEnable) {
	//			// 表示盤制御情報のAPIを丸ごとで、制御を実施
	//			List<String> errorStringList = controlService.machineControl(controlList);
	//			if (!errorStringList.isEmpty()) {
	//				// 機器制御エラー
	//				String errmsg = messageSource.getMessage("msg.control.monitor.machine.control.error", null, Locale.JAPANESE);
	//				result.getErrMsgList().add(errmsg);
	//				for (String error : errorStringList) {
	//					result.getErrMsgList().add(error);
	//				}
	//			}
	//		}
	//
	//		//		if (externalConfiguration.getMachineControlEnable()) {
	//		//			// 表示盤制御情報のAPIを丸ごとで、制御を実施
	//		//			List<String> errorStringList = controlService.machineControl(controlList);
	//		//			if (!errorStringList.isEmpty()) {
	//		//				// 機器制御エラー
	//		//				String errmsg = messageSource.getMessage("msg.control.monitor.machine.control.error", null, Locale.JAPANESE);
	//		//				result.getErrMsgList().add(errmsg);
	//		//				for (String error : errorStringList) {
	//		//					result.getErrMsgList().add(error);
	//		//				}
	//		//			}
	//		//		}
	//
	//		log.info("☆ controlExecute end");
	//
	//		return result;
	//	}

	protected TemporaryContentData createTemporaryContentData(Control control) {

		Integer displayType = control.getDisplay_type();

		MatrixIndexConverter converter = displayType == 1 ? new MatrixIndexConverter(1, 1, 2, 2) : new MatrixIndexConverter(2, 2, 2, 2);

		Integer displayDetailsSplitNo = control.getDisplay_details_split_no();

		TemporaryContentData temporary = new TemporaryContentData();
		temporary.bind(control);
		MatrixIndexInfo matrixIndexInfo = converter.convertMatrixIndex(displayDetailsSplitNo);
		temporary.setMatrixIndexInfo(matrixIndexInfo);

		return temporary;
	}

	protected List<TemporaryContentData> createTemporaryContentData(List<ControlMonitorDisplayControlData> controlData) {

		List<TemporaryContentData> temporaries = new ArrayList<TemporaryContentData>();

		for (ControlMonitorDisplayControlData display : controlData) {

			for (ControlMonitorDisplaySplitData splitData : display.getDisplaySplitData()) {

				Integer displayType = display.getDisplayType();
				Integer displayNo = display.getDisplayNo();
				Integer displaySplitNo = splitData.getDisplaySplitNo();
				String displayName = display.getDisplayName();

				for (ControlMonitorDisplayDetailsSplitData detailsSplitData : splitData.getDisplayDetailsSplitData()) {

					Integer displayDetailsSplitNo = detailsSplitData.getDisplayDetailsSplitNo();

					Control control = new Control();

					if (display.getDisplayType() != null) {
						control.setDisplay_type(displayType);
					}
					control.setDisplay_no(displayNo);
					control.setDisplay_name(displayName);

					control.setDisplay_split_no(displaySplitNo);
					control.setDisplay_details_split_no(displayDetailsSplitNo);

					ControlMonitorDisplayContentData displayContentData = detailsSplitData.getDisplayContentData();

					if (displayContentData != null) {

						Integer inputSwitch = displayContentData.getInputSwitch();
						Integer sourceNo = displayContentData.getSourceNo();
						String sourceName = displayContentData.getSourceName();
						Integer sourceSplitNo = displayContentData.getSourceSplitNo();
						Integer sourceDispPat = displayContentData.getSourceDispPat();
						Integer sourceVolControlType = displayContentData.getSourceVolControlType();
						Integer sourceVolMuteControl = displayContentData.getSourceVolMuteControl();
						Integer isVolControl = displayContentData.getIsVolControl();

						control.setInput_switch(inputSwitch);
						control.setSource_no(sourceNo);
						control.setSource_name(sourceName);
						control.setSource_split_no(sourceSplitNo);
						control.setSource_disp_pat(sourceDispPat);

						control.setSource_vol_control_type(sourceVolControlType);
						control.setSource_vol_mute_control(sourceVolMuteControl);
						control.setIs_vol_control(isVolControl);
					}

					TemporaryContentData temporary = createTemporaryContentData(control);

					temporaries.add(temporary);
				}
			}
		}

		return temporaries;
	}

	//	/**
	//	 * 3.1の表示盤制御情報に従い、各制御を実施
	//	 *
	//	 * @param controlList DB保存済みの各表示盤制限情報
	//	 * @param taskId      　Task Id
	//	 */
	//	@Transactional(propagation = Propagation.REQUIRES_NEW)
	//	protected Result controlExecute(List<Control> controlList, String taskId) {
	//
	//		log.info("☆ controlExecute start");
	//
	//		Result result = new Result();
	//		Map<Integer, Map<Integer, Map<Integer, Control>>> sourceMgtMap = getSourceMgtMap(controlList);
	//
	//		sourceMgtMap.forEach((displayNo, value) -> {
	//			for (Map.Entry<Integer, Map<Integer, Control>> splitSet : value.entrySet()) {
	//
	//				Integer displaySplitNo = splitSet.getKey();
	//				log.info("displayNo: " + displayNo + ", displaySplitNo: " + displaySplitNo);
	//
	//				Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(displayNo, splitSet.getKey()));
	//
	//				//				if (monitorOpt.isEmpty() || monitorOpt.get().getStatus() != CommonUtil.MONITOR_STATUS_CONNECTED) {
	//				//					// 表示盤未セットアップの場合、処理Skipして、エラーメッセージを返す
	//				//					final Object[] params = { displayNo, displaySplitNo };
	//				//					String errmsg = messageSource.getMessage("msg.control.monitor.no.setup", params, Locale.JAPANESE);
	//				//					log.warn("displayNo:{}, msg:{}", displayNo, errmsg);
	//				//					result.getErrMsgList().add(errmsg);
	//				//					continue;
	//				//				}
	//
	//				String errmsg = checkSetupMonitor(displayNo, displaySplitNo, monitorOpt);
	//
	//				if (!StringUtils.isEmpty(errmsg)) {
	//					result.getErrMsgList().add(errmsg);
	//					continue;
	//				}
	//
	//				//表示盤制御
	//				Control control = getFirstSplitScreenControl(splitSet);
	//				if (control != null) {
	//					control.setStatus(CommonUtil.STATUS_SEND2BROWSER);
	//					control.setUpdate_time(new Date());
	//					controlDao.save(control);
	//				}
	//
	//				// ソース表示パターンを参照して表示制御を実行
	//				// ソース表示パターンは複数指定できるため、優先順位を以下の通りとする
	//				// 1面表示 (0) < 1/4面表示 (1) < 1面表示 (2) < 4面表示 (3)
	//				int dispPat = 0;
	//				for (Map.Entry<Integer, Control> split : splitSet.getValue().entrySet()) {
	//					dispPat = Math.max(dispPat, split.getValue().getSource_disp_pat());
	//				}
	//				int splitSize = (dispPat == 1) ? 4 : 1;
	//				DeferredResult<Boolean> deferredResult = sendMonitorControl(splitSize, displayNo, splitSet.getKey(), taskId, control);
	//
	//				waitMonitorSetupDone(deferredResult);
	//
	//				// コンテンツ表示
	//				splitSet.getValue().forEach((key, ctrl) -> {
	//
	//					String resultMsg = sendContent2Browser(splitSize, ctrl, taskId);
	//					if (StringUtils.isNotEmpty(resultMsg)) {
	//						result.getErrMsgList().add(resultMsg);
	//					}
	//				});
	//			}
	//		});
	//
	//		boolean machineControlEnable = SystemSettingManager.getSystemSetting().getMachineControlEnable();
	//
	//		if (machineControlEnable) {
	//			// 表示盤制御情報のAPIを丸ごとで、制御を実施
	//			List<String> errorStringList = controlService.machineControl(controlList);
	//			if (!errorStringList.isEmpty()) {
	//				// 機器制御エラー
	//				String errmsg = messageSource.getMessage("msg.control.monitor.machine.control.error", null, Locale.JAPANESE);
	//				result.getErrMsgList().add(errmsg);
	//				for (String error : errorStringList) {
	//					result.getErrMsgList().add(error);
	//				}
	//			}
	//		}
	//
	//		//		if (externalConfiguration.getMachineControlEnable()) {
	//		//			// 表示盤制御情報のAPIを丸ごとで、制御を実施
	//		//			List<String> errorStringList = controlService.machineControl(controlList);
	//		//			if (!errorStringList.isEmpty()) {
	//		//				// 機器制御エラー
	//		//				String errmsg = messageSource.getMessage("msg.control.monitor.machine.control.error", null, Locale.JAPANESE);
	//		//				result.getErrMsgList().add(errmsg);
	//		//				for (String error : errorStringList) {
	//		//					result.getErrMsgList().add(error);
	//		//				}
	//		//			}
	//		//		}
	//
	//		log.info("☆ controlExecute end");
	//
	//		return result;
	//	}

	private String checkSetupMonitor(Integer displayNo, Integer displaySplitNo, Optional<Monitor> monitorOpt) {

		if (monitorOpt.isEmpty()) {
			// 表示盤未セットアップの場合、処理Skipして、エラーメッセージを返す
			final Object[] params = { displayNo, displaySplitNo };
			String errmsg = messageSource.getMessage("msg.control.monitor.no.setup", params, Locale.JAPANESE);
			log.warn("displayNo:{}, msg:{}", displayNo, errmsg);
			return errmsg;
		}

		Integer status = monitorOpt.get().getStatus();

		if (status != CommonUtil.MONITOR_STATUS_CONNECTED) {
			// 表示盤未セットアップの場合、処理Skipして、エラーメッセージを返す
			final Object[] params = { displayNo, displaySplitNo };
			String errmsg = messageSource.getMessage("msg.control.monitor.no.setup", params, Locale.JAPANESE);
			log.warn("displayNo:{}, msg:{}", displayNo, errmsg);
			return errmsg;
		}

		return null;
	}

	//	//　同じ表示盤で、1個目のコンテンツを表示する分割面の制御情報を返す
	//	private Control getFirstSplitScreenControl(Map.Entry<Integer, Map<Integer, Control>> splitSet) {
	//		for (Map.Entry<Integer, Control> map : splitSet.getValue().entrySet()) {
	//			if (map.getValue().getDisplay_details_split_no() == 0) {
	//				return map.getValue();
	//			}
	//		}
	//		return null;
	//	}

	/**
	 * コンテンツ内容をブラウザに送る
	 *
	 * @param splitSize 分割Size。1: 分割しない。4: 四個分割
	 * @param control   　Monitorの制御情報
	 * @param taskId    　Task Id
	 */
	private String sendContent2Browser(TemporaryContentData temporary, String taskId) {

		log.info("☆ sendContent2Browser start");

		Integer displayNo = temporary.getDisplayNo();
		Integer displaySplitNo = temporary.getDisplaySplitNo();
		Integer displayDetailsSplitNo = temporary.getDisplayDetailsSplitNo();

		Integer sourceNo = temporary.getSourceNo();
		Content dbContent = temporary.adoptContent();
		Control dbControl = temporary.getDbControl();

		if (sourceNo == SourceNo.Now.getSourceNo()) {

			//現在時刻コンテンツの場合、サーバーからClientに通知するようにCurrentTimeServiceに登録
			ScreenInfo screenInfo = temporary.createClockScreenInfo();
			ContentInfo contentInfo = temporary.createClockContentInfo();

			SyncTimeTime timeContentSetting = null;
			if (dbContent != null) {
				try {
					String sourceData = dbContent.getSource_data();
					timeContentSetting = objectMapper.readValue(sourceData, SyncTimeTime.class);
				} catch (JsonProcessingException e) {
					log.error("sendContent2Browser: To Json fail for time content", e);
				}
			}

			// 時刻コンテンツ更新用に設定を保持
			scheduleService.registerTimeContentScreenInfo(screenInfo, contentInfo, timeContentSetting);

		} else {

			if (dbContent != null) {

				// 時刻コンテンツ更新用の設定を削除
				scheduleService.clearTimeContentScreenInfo(displayNo, displaySplitNo, displayDetailsSplitNo);

				// 現在時刻コンテンツ以外の場合、コンテンツの種類をNotify2Browser
				sendContent(temporary, taskId);
			}
		}

		return null;
	}

	//	/**
	//	 * コンテンツ内容をブラウザに送る
	//	 *
	//	 * @param splitSize 分割Size。1: 分割しない。4: 四個分割
	//	 * @param control   　Monitorの制御情報
	//	 * @param taskId    　Task Id
	//	 */
	//	private String sendContent2Browser(int splitSize, Control control, String taskId) {
	//
	//		log.info("☆ sendContent2Browser start");
	//
	//		if (splitSize == 1 &&
	//				!(control.getDisplay_details_split_no() == 0 ||
	//						control.getDisplay_details_split_no() == 2 ||
	//						control.getDisplay_details_split_no() == 8 ||
	//						control.getDisplay_details_split_no() == 10)) {
	//			// 表示盤未セットアップの場合、処理終了
	//			final Object[] params = { control.getDisplay_details_split_no(), };
	//			return messageSource.getMessage("msg.api.error.wrong.detail.split.no", params, Locale.JAPANESE);
	//		}
	//		Integer displayType = control.getDisplay_type();
	//		Integer afterDetailSplitNo = control.getDisplay_details_split_no();
	//		if ((displayType == 1) && (afterDetailSplitNo < 0 || 3 < afterDetailSplitNo)) {
	//			final Object[] params = { displayType, afterDetailSplitNo };
	//			return messageSource.getMessage("msg.api.error.wrong.detail.split.no.single.monitor", params, Locale.JAPANESE);
	//		}
	//
	//		Optional<Content> contentOptional = contentDao.findById(new ContentPK(control.getSource_no(), control.getSource_split_no()));
	//
	//		//現在時刻コンテンツの場合、サーバーからClientに通知するようにCurrentTimeServiceに登録
	//		if (control.getSource_no() == SourceNo.Now.getSourceNo()) {
	//			ScreenInfo screenInfo = new ScreenInfo();
	//			screenInfo.setDisplayNo(control.getDisplay_no());
	//			screenInfo.setSplitNo(control.getDisplay_split_no());
	//			// 単面表示盤の場合は画面分割番号を変換する (Frontendは4面マルチの画面分割番号指定で動作する)
	//			if (displayType == 1) {
	//				afterDetailSplitNo = switch (afterDetailSplitNo) {
	//				case 2 -> 4;
	//				case 3 -> 5;
	//				default -> afterDetailSplitNo;
	//				};
	//			}
	//			screenInfo.setDetailSplitNo(afterDetailSplitNo);
	//
	//			ContentInfo contentInfo = new ContentInfo(control.getSource_no(), control.getSource_split_no(), afterDetailSplitNo);
	//			SyncTimeTime timeContentSetting = null;
	//			if (contentOptional.isPresent()) {
	//				try {
	//					timeContentSetting = objectMapper.readValue(contentOptional.get().getSource_data(), SyncTimeTime.class);
	//				} catch (JsonProcessingException e) {
	//					log.error("sendContent2Browser: To Json fail for time content", e);
	//				}
	//			}
	//			// 時刻コンテンツ更新用に設定を保持
	//			scheduleService.registerTimeContentScreenInfo(screenInfo, contentInfo, timeContentSetting);
	//		} else {
	//			if (contentOptional.isPresent()) {
	//				// 時刻コンテンツ更新用の設定を削除
	//				scheduleService.clearTimeContentScreenInfo(control.getDisplay_no(), control.getDisplay_split_no(), control.getDisplay_details_split_no());
	//				// 現在時刻コンテンツ以外の場合、コンテンツの種類をNotify2Browser
	//				sendContent(control, contentOptional.orElse(null), taskId);
	//			}
	//		}
	//
	//		log.info("☆ sendContent2Browser end");
	//
	//		return null;
	//	}

	//	/**
	//	 * <DisplayNo表示盤に表示するソース番号, Map<SplitNo, Map<DetailSplitNo, *>>形式で
	//	 * Mapを返す
	//	 *
	//	 * @param controlList 　制御情報リスト
	//	 * @return 纏まったMap
	//	 */
	//	private Map<Integer, Map<Integer, Map<Integer, Control>>> getSourceMgtMap(List<Control> controlList) {
	//
	//		//2. 4分割かどうかを判別する為、カテゴリ毎で、Mapを作成
	//		//<DisplayNo表示盤に表示するソース番号, Map<SplitNo, Map<DetailSplitNo, *>>
	//		Map<Integer, Map<Integer, Map<Integer, Control>>> sourceMgtMap = new HashMap<>();
	//
	//		for (Control control : controlList) {
	//
	//			Map<Integer, Map<Integer, Control>> splitNoMap;
	//
	//			if (sourceMgtMap.containsKey(control.getDisplay_no())) {
	//
	//				splitNoMap = sourceMgtMap.get(control.getDisplay_no());
	//			} else {
	//
	//				splitNoMap = new HashMap<>();
	//				sourceMgtMap.put(control.getDisplay_no(), splitNoMap);
	//			}
	//
	//			Map<Integer, Control> detailSplitNoMap;
	//			if (splitNoMap.containsKey(control.getDisplay_split_no())) {
	//
	//				detailSplitNoMap = splitNoMap.get(control.getDisplay_split_no());
	//			} else {
	//
	//				detailSplitNoMap = new HashMap<>();
	//				splitNoMap.put(control.getDisplay_split_no(), detailSplitNoMap);
	//			}
	//
	//			if (detailSplitNoMap.containsKey(control.getDisplay_details_split_no())) {
	//
	//				log.error("Same data exist. Display={}, SplitNo={}, DetailSplitNo={}",
	//						control.getDisplay_no(), control.getDisplay_split_no(), control.getDisplay_details_split_no());
	//				// 他の処理が必要かも、例えば既にこの分割番号で表示するコンテンツの表示停止。現時点、上書きだけ
	//				detailSplitNoMap.put(control.getDisplay_details_split_no(), control);
	//			} else {
	//
	//				detailSplitNoMap.put(control.getDisplay_details_split_no(), control);
	//			}
	//		}
	//		return sourceMgtMap;
	//	}

	//　制御情報をDBに保存
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	protected List<Control> saveControlInfo2Db(List<ControlMonitorDisplayControlData> controlData) {

		List<Control> resultList = new ArrayList<>();

		for (ControlMonitorDisplayControlData display : controlData) {

			// 表示盤番号単位

			for (ControlMonitorDisplaySplitData splitData : display.getDisplaySplitData()) {

				// 画面分割番号単位

				// 制御情報のソース表示パターンを取得
				// ソース表示パターンは複数指定できるため、優先順位を以下の通りとする
				// 1面表示 (0) < 1/4面表示 (1) < 1面表示 (2) < 4面表示 (3)
				int dispPat = 0;
				for (ControlMonitorDisplayDetailsSplitData split : splitData.getDisplayDetailsSplitData()) {
					dispPat = Math.max(dispPat, split.getDisplayContentData().getSourceDispPat());
				}

				// 現在のソース表示パターンを取得
				Integer currentDispPat = controlDao.selectMaxSourceDispPat(display.getDisplayNo(), splitData.getDisplaySplitNo());

				// ソース表示パターンが異なる場合は該当面の全制御情報を削除
				if (currentDispPat != null && dispPat != currentDispPat) {
					controlDao.deleteByDisplayNoAndDisplaySplitNo(display.getDisplayNo(), splitData.getDisplaySplitNo());
				}

				for (ControlMonitorDisplayDetailsSplitData detailsSplitData : splitData.getDisplayDetailsSplitData()) {

					Control control = new Control();
					if (display.getDisplayType() != null) {
						control.setDisplay_type(display.getDisplayType());
					}
					control.setDisplay_no(display.getDisplayNo());
					control.setDisplay_name(display.getDisplayName());

					control.setDisplay_split_no(splitData.getDisplaySplitNo());

					control.setDisplay_details_split_no(detailsSplitData.getDisplayDetailsSplitNo());

					if (detailsSplitData.getDisplayContentData() != null) {
						control.setInput_switch(detailsSplitData.getDisplayContentData().getInputSwitch());
						control.setSource_no(detailsSplitData.getDisplayContentData().getSourceNo());
						control.setSource_name(detailsSplitData.getDisplayContentData().getSourceName());
						control.setSource_split_no(detailsSplitData.getDisplayContentData().getSourceSplitNo());
						control.setSource_disp_pat(detailsSplitData.getDisplayContentData().getSourceDispPat());

						control.setSource_vol_control_type(detailsSplitData.getDisplayContentData().getSourceVolControlType());
						control.setSource_vol_mute_control(detailsSplitData.getDisplayContentData().getSourceVolMuteControl());
						control.setIs_vol_control(detailsSplitData.getDisplayContentData().getIsVolControl());
					}
					control.setStatus(CommonUtil.STATUS_RECEIVED);
					control.setReceive_time(new Date());
					control.setUpdate_time(new Date());
					resultList.add(controlDao.save(control));
				}
			}
		}
		return resultList;
	}

	//　コンテンツ内容をブラウザに送り出す
	private void sendContent(TemporaryContentData temporary, String taskId) {

		log.info("● sendContent start");

		final Control dbControl = temporary.getDbControl();
		final Content dbContent = temporary.getDbContent();

		final Content content = temporary.adoptContent();

		final Integer displayNo = dbControl.getDisplay_no();
		final Integer displaySplitNo = dbControl.getDisplay_split_no();

		final Integer sourceNo = dbControl.getSource_no();
		final Integer sourceSplitNo = dbControl.getSource_split_no();

		Integer afterDetailSplitNo = dbControl.getDisplay_details_split_no();

		ContentInfo contentInfo = new ContentInfo();
		contentInfo.setSourceNo(sourceNo);
		contentInfo.setSourceSplitNo(sourceSplitNo);

		// 単面表示盤の場合は画面分割番号を変換する (Frontendは4面マルチの画面分割番号指定で動作する)
		if (dbControl.getDisplay_type() == 1) {
			afterDetailSplitNo = switch (afterDetailSplitNo) {
			case 2 -> 4;
			case 3 -> 5;
			default -> afterDetailSplitNo;
			};
		}
		contentInfo.setDetailSplitNo(afterDetailSplitNo);

		// ソース名称 
		String sourceName = content.getSource_name();
		contentInfo.setSourceName(sourceName);
		// ソース表示パターン
		Integer sourceDispPattern = dbControl.getSource_disp_pat();
		contentInfo.setSourceDispPattern(sourceDispPattern);

		if (content != null) {
			String sourceData = content.getSource_data();
			contentInfo.setSourceData(sourceData);
		}

		log.info("★★★ displayNo: {}, displaySplitNo: {}, sourceNo: {}, sourceSplitNo: {}, sourceDetailSplitNo: {}", displayNo, displaySplitNo, sourceNo, sourceSplitNo, afterDetailSplitNo);

		//WebSocketの受け側のID
		String wsEndpoint = CommonUtil.getWebSocketId(displayNo, displaySplitNo, afterDetailSplitNo);

		final String basePrefix = MessageFormatUtil.format("sendContent2EndPoint({})", wsEndpoint);
		final String prefix = content != null ? MessageFormatUtil.format("{}_sourceNo_{}", basePrefix, content.getPk().getSource_no()) : basePrefix;

		log.info("☆ synchronized before sourceName: {}, ", sourceName, prefix);

		synchronized (taskQueue) {
			contentInfo.setId(taskQueue.getTaskId(prefix));
			taskQueue.putSubTask(taskId, contentInfo.getId());
			log.info("☆ synchronized in sourceName: {}", sourceName);
		}

		log.info("☆ synchronized after sourceName: {}", sourceName);

		log.info("★wsEndpoint: {}", wsEndpoint);

		messagingTemplate.convertAndSendToUser(wsEndpoint, CommonUtil.WS_CMD_SET_CONTENT, contentInfo);

		if (content != null) {
			// DBステータス更新
			content.setUpdate_time(new Date());
			content.setStatus(CommonUtil.STATUS_SEND2BROWSER);

			log.info("☆ database save before sourceName: {}", sourceName);
			synchronized (taskQueue) {
				contentDao.save(dbContent);
			}
			log.info("☆ database save after sourceName: {}", sourceName);
		}

		//		Thread thread = new Thread(new Runnable() {
		//
		//			@Override
		//			public void run() {
		//
		//				synchronized (taskQueue) {
		//					contentInfo.setId(taskQueue.getTaskId(prefix));
		//					taskQueue.putSubTask(taskId, contentInfo.getId());
		//					log.info("☆ synchronized in sourceName: {}", sourceName);
		//				}
		//
		//				log.info("☆ synchronized after sourceName: {}", sourceName);
		//
		//				log.info("★wsEndpoint: {}", wsEndpoint);
		//
		//				messagingTemplate.convertAndSendToUser(wsEndpoint, CommonUtil.WS_CMD_SET_CONTENT, contentInfo);
		//
		//				if (content != null) {
		//					// DBステータス更新
		//					content.setUpdate_time(new Date());
		//					content.setStatus(CommonUtil.STATUS_SEND2BROWSER);
		//
		//					log.info("☆ database save before sourceName: {}", sourceName);
		//					synchronized (taskQueue) {
		//						contentDao.save(dbContent);
		//					}
		//					log.info("☆ database save after sourceName: {}", sourceName);
		//				}
		//			}
		//		});
		//
		//		thread.start();

		log.info("● sendContent end");
	}

	/**
	 * モニターセットアップ時点で、表示盤の機械制御部分を実施
	 *
	 * @param monitor 　モニター情報
	 */
	private void machineControl(Monitor monitor) {
		// 機器制御
		List<Control> controlList = controlDao.findByDisplayNoAndDisplaySplitNo(monitor.getDisplay_no(), monitor.getDisplay_split_no());

		boolean machineControlEnable = SystemSettingManager.getSystemSetting().getMachineControlEnable();

		if (machineControlEnable) {
			controlService.machineControlWhenMonitorSetup(controlList);
		}
	}

	/**
	 * DBのControl情報に従い、Browserに表示盤制御実施
	 *
	 * @param splitSize 分割のSize
	 * @param displayNo 　DBのMonitor情報
	 * @param splitNo   　DBの分割情報
	 * @param control   　制御情報
	 * @return Deferred Result
	 */
	private DeferredResult<Boolean> sendMonitorControl(List<TemporaryContentData> temporaries, String taskId) {

		TemporaryContentData entity = Collections.max(temporaries, Comparator.comparingInt(TemporaryContentData::getSourceDisplayPattern));

		Integer sourceDisplayPattern = entity.getSourceDisplayPattern();

		int splitSize = (sourceDisplayPattern == 1) ? 4 : 1;

		// 1. Layouの制御
		//WebSocketの受け側のID
		String wsEndpoint = entity.createControlWebSocketEndpoint();

		ScreenLayout layout;
		if (splitSize == 1) {
			// 1分割
			layout = new ScreenLayout(1, 1);
		} else {
			// 4分割
			layout = new ScreenLayout(2, 2);
		}

		DeferredResult<Boolean> deferredResult;
		synchronized (taskQueue) {
			String prefix = MessageFormatUtil.format("sendMonitorControl2EndPoint({})", wsEndpoint);
			layout.setId(taskQueue.getTaskId(prefix));

			deferredResult = utilityService.getBooleanDeferredResult(wsEndpoint, layout.getId());

			taskQueue.putSubTask(taskId, layout.getId());

			// Layoutの処理を終わるまで待ち続ける為、DeferredResultを利用
			taskQueue.putDeferredResult(layout.getId(), deferredResult);
		}

		messagingTemplate.convertAndSendToUser(wsEndpoint, CommonUtil.WS_CMD_SET_CONTROL, layout);

		for (TemporaryContentData temporary : temporaries) {

			Control dbControl = temporary.getDbControl();
			dbControl.setStatus(CommonUtil.STATUS_SEND2BROWSER);
			dbControl.setUpdate_time(new Date());
			controlDao.save(dbControl);
		}

		return deferredResult;
	}

	/**
	 * DBのControl情報に従い、Browserに表示盤制御実施
	 *
	 * @param splitSize 分割のSize
	 * @param displayNo 　DBのMonitor情報
	 * @param splitNo   　DBの分割情報
	 * @param control   　制御情報
	 * @return Deferred Result
	 */
	private DeferredResult<Boolean> sendMonitorControl(int splitSize, Integer displayNo, Integer splitNo,
			String taskId, Control control) {
		// 1. Layouの制御
		//WebSocketの受け側のID
		String wsEndpoint = CommonUtil.getWebSocketId(displayNo, splitNo);

		ScreenLayout layout;
		if (splitSize == 1) {
			// 1分割
			layout = new ScreenLayout(1, 1);
		} else {
			// 4分割
			layout = new ScreenLayout(2, 2);
		}

		DeferredResult<Boolean> deferredResult;
		synchronized (taskQueue) {
			String prefix = MessageFormatUtil.format("sendMonitorControl2EndPoint({})", wsEndpoint);
			layout.setId(taskQueue.getTaskId(prefix));

			deferredResult = utilityService.getBooleanDeferredResult(wsEndpoint, layout.getId());

			taskQueue.putSubTask(taskId, layout.getId());

			// Layoutの処理を終わるまで待ち続ける為、DeferredResultを利用
			taskQueue.putDeferredResult(layout.getId(), deferredResult);
		}

		messagingTemplate.convertAndSendToUser(wsEndpoint, CommonUtil.WS_CMD_SET_CONTROL, layout);

		return deferredResult;
	}

	/**
	 * 時期同期APIの内容を時刻コンテンツとして、Contentテーブルに保存
	 *
	 * @param content 時期同期APIの内容
	 * @return Contentテーブルの一行Record
	 * @throws JsonProcessingException
	 */
	private Content saveTimeContent2Db(SyncTimeTime content) throws JsonProcessingException {
		// ContentをDB保存
		Content dbContent = new Content();
		dbContent.setPk(new ContentPK(SourceNo.Now.getSourceNo(), 0));
		dbContent.setSource_name("時刻コンテンツ");
		dbContent.setStatus(CommonUtil.STATUS_RECEIVED);
		dbContent.setReceive_time(new Date());
		dbContent.setUpdate_time(new Date());
		dbContent.setSource_data(objectMapper.writeValueAsString(content));
		dbContent = contentDao.save(dbContent);
		return dbContent;
	}

	/**
	 * 3.2の表示盤喚起音吹鳴情報に従い、制御を実施
	 *
	 * @param controlData 喚起音吹鳴情報
	 * @param taskId      　Task Id
	 */
	@Transactional(readOnly = true)
	protected Result soundExecute(ControlSoundDisplaySoundRingData controlData, String taskId) {

		Result result = new Result();

		Integer display_type = controlData.getDisplayType();
		Integer display_no = controlData.getDisplayNo();

		MachineComposition machineComposition = CompositionManager.loadComposition();

		Integer display_split_no = machineComposition.getUsbSpeakerDisplaySplitNo(display_type, display_no);

		log.info("pc.getDisplaySplitNo: " + display_split_no);

		if (display_split_no != null) {
			// DBの情報参照
			Optional<Monitor> monitorOpt = monitorDao.findById(new MonitorPK(display_no, display_split_no));

			if (monitorOpt.isEmpty() || monitorOpt.get().getStatus() != CommonUtil.MONITOR_STATUS_CONNECTED) {
				// 表示盤未セットアップの場合、処理Skipして、エラーメッセージを返す
				final Object[] params = { display_no, display_split_no };
				String msg = messageSource.getMessage("msg.control.monitor.no.setup", params, Locale.JAPANESE);
				log.warn(msg);
				result.getErrMsgList().add(msg);
			} else {
				DeferredResult<Boolean> deferredResult = sendSoundControl(display_no, display_split_no, controlData.getSoundNo(), controlData.getRepeatCount(), taskId);

				waitMonitorSetupDone(deferredResult);
			}
		} else {
			// 表示盤番号のスピーカー設定がない場合、処理Skipして、エラーメッセージを返す
			final Object[] params = { display_no };
			result.getErrMsgList().add(messageSource.getMessage("msg.control.sound.no.speaker", params, Locale.JAPANESE));
		}

		return result;
	}

	/**
	 * DBのMonitor情報に従い、Browserに表示盤喚起音制御実施
	 *
	 * @param displayNo 　DBのMonitor情報
	 * @param splitNo   　DBの分割情報
	 * @param soundNo   　再生ファイル番号
	 * @param repeatCount 繰り返し回数
	 * @param taskId タスクID
	 * @return Deferred Result
	 */
	private DeferredResult<Boolean> sendSoundControl(Integer displayNo, Integer splitNo, Integer soundNo, Integer repeatCount, String taskId) {

		SoundInfo soundInfo = new SoundInfo();
		soundInfo.setSoundNo(soundNo);
		soundInfo.setRepeatCount(repeatCount);

		//WebSocketの受け側のID
		String wsEndpoint = CommonUtil.getWebSocketId(displayNo, splitNo);

		DeferredResult<Boolean> deferredResult;
		synchronized (taskQueue) {
			String prefix = MessageFormatUtil.format("sendSoundControl2EndPoint({})", wsEndpoint);
			soundInfo.setId(taskQueue.getTaskId(prefix));

			deferredResult = utilityService.getBooleanDeferredResult(wsEndpoint, soundInfo.getId());

			taskQueue.putSubTask(taskId, soundInfo.getId());

			// soundInfoの処理を終わるまで待ち続ける為、DeferredResultを利用
			taskQueue.putDeferredResult(soundInfo.getId(), deferredResult);
		}

		log.info("★wsEndpoint: {}", wsEndpoint);
		messagingTemplate.convertAndSendToUser(wsEndpoint, CommonUtil.WS_CMD_SET_SOUND, soundInfo);

		return deferredResult;
	}

	public List<String> convertVehicleSourceData(List<TemporaryContentData> temporaries) {

		List<String> errors = new ArrayList<String>();

		// Check for both SourceNo=1 (32-line vehicle content) and SourceNo=18 (50-line extended vehicle content)
		if (temporaries.stream().anyMatch(d -> Objects.equals(d.getSourceNo(), 1) || Objects.equals(d.getSourceNo(), 18)) == false)
			return errors;

		log.info("★★★★★★★★★ start");

		try {

			// ------------------------------------------------------------------------
			// 車両コンテンツがある場合は必要に応じて車両コンテンツのデータを加工する
			// ------------------------------------------------------------------------

			// SourceNo=1（32行車両）とSourceNo=18（50行拡張車両）を分別処理
			Map<Integer, List<TemporaryContentData>> sourceGroup1 = temporaries.stream().filter(f -> f.getSourceNo() == 1).collect(Collectors.groupingBy(d -> d.getSourceSplitNo()));
			Map<Integer, List<TemporaryContentData>> sourceGroup18 = temporaries.stream().filter(f -> f.getSourceNo() == 18).collect(Collectors.groupingBy(d -> d.getSourceSplitNo()));

			// SourceNo=1の処理
			if (!sourceGroup1.isEmpty()) {
				processVehicleSourceGroup(sourceGroup1, temporaries, errors, 1);
			}

			// SourceNo=18の処理
			if (!sourceGroup18.isEmpty()) {
				processVehicleSourceGroup(sourceGroup18, temporaries, errors, 18);
			}



		} catch (Exception e) {
			log.error("error convertVehicleSourceData", e);
			errors.add(e.getMessage());
		} finally {

			log.info("★★★★★★★★★ end");
		}

		return errors;
	}

	/**
	 * 指定されたSourceNoの車両データを処理する
	 * @param sourceGroup ソース分割番号でグループ化されたデータ
	 * @param temporaries 全体のtemporaryデータリスト
	 * @param errors エラーリスト
	 * @param sourceNo ソース番号（1=32行車両、18=50行拡張車両）
	 */
	private void processVehicleSourceGroup(Map<Integer, List<TemporaryContentData>> sourceGroup,
			List<TemporaryContentData> temporaries, List<String> errors, int sourceNo) {

		// ソース分割番号毎に処理する
		for (Iterator<Map.Entry<Integer, List<TemporaryContentData>>> sourceGroupIterator = sourceGroup.entrySet().iterator(); sourceGroupIterator.hasNext();) {

			Map.Entry<Integer, List<TemporaryContentData>> sourceGroupEntry = sourceGroupIterator.next();

			List<TemporaryContentData> sourceSplits = sourceGroupEntry.getValue();

			if (!sourceSplits.stream().anyMatch(d -> d.getSourceDisplayPattern() == 1))
				continue;

			// 表示盤番号と面番号でグルーピング
			Map<String, List<TemporaryContentData>> splitGroup = sourceSplits.stream().collect(Collectors.groupingBy(d -> d.getDisplayNo() + "-" + d.getDisplaySplitNo()));

			// １モニター毎に処理する
			for (Iterator<Map.Entry<String, List<TemporaryContentData>>> splitGroupIterator = splitGroup.entrySet().iterator(); splitGroupIterator.hasNext();) {

				Map.Entry<String, List<TemporaryContentData>> splitGroupEntry = splitGroupIterator.next();

				List<TemporaryContentData> monitors = splitGroupEntry.getValue();

				if (!monitors.stream().anyMatch(d -> d.getSourceDisplayPattern() == 1))
					continue;

				String key = splitGroupEntry.getKey();

				List<String> results = validateVehicleContent(key, monitors);
				if (!results.isEmpty()) {
					errors.addAll(results);
					temporaries.removeAll(monitors);
					continue;
				}

				monitors = monitors.stream().sorted((a, b) -> a.getMatrixIndexInfo().getColumnPriorityIndex() - b.getMatrixIndexInfo().getColumnPriorityIndex()).toList();

				int groupId = 1;
				for (TemporaryContentData temporary : monitors) {

					Content dbContent = temporary.getDbContent();
					if (dbContent == null)
						continue;

					try {

						MatrixIndexInfo matrixIndexInfo = temporary.getMatrixIndexInfo();
						String rowAlign = matrixIndexInfo.getRowAlign();

						String sourceData = dbContent.getSource_data();

						VehicleContent vehicleContent = objectMapper.readValue(sourceData, VehicleContent.class);

						// SourceNoに応じて適切なContentクラスを使用
						int rowSize = 0;
						if (sourceNo == 1) {
							// 32行車両用（8行分組）
							rowSize = 8;
						} else if (sourceNo == 18) {
							// 50行拡張車両用（12行分組）
							rowSize = 12;
						} else {
							log.error("Unsupported SourceNo: {}", sourceNo);
							continue;
						}
						CustomVehicleContent newContent = new CustomVehicleContent(rowSize);
						newContent.setGroupId(groupId);
						newContent.setColumnPosition(rowAlign);
						newContent.bind(vehicleContent);

						String jsonString = objectMapper.writeValueAsString(newContent);

						com.contents.common.db.Content convertContent = CommonUtil.convertClassObject(dbContent, com.contents.common.db.Content.class);
						convertContent.setSource_data(jsonString);

						temporary.setConvertContent(convertContent);

					} catch (JsonProcessingException e) {

						log.error("json convert error", e);

					} catch (Exception e) {

						log.error("content convert error", e);

					} finally {

						groupId++;
					}
				}
			}
		}
	}

}
