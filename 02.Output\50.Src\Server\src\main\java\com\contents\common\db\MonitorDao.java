package com.contents.common.db;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 表示盤セットアップテーブルのPrimaryKey
 */
@Repository
@RepositoryRestResource(path = "monitor")
public interface MonitorDao extends JpaRepository<Monitor, MonitorPK>, Serializable {
        /**
         * MonitorをOfflineにする
         * @param updateTime 更新時間
         * @return 更新したRowの数
         */
        @Transactional
        @Modifying
        @Query(value = "UPDATE Monitor SET status = 0, update_time = :updateTime")
        Integer update2Offline(@Param("updateTime") Date updateTime);

        /**
         * 最新の受信時間で、指定したCount行を返す
         *
         * @param count　返す必要な行数
         * @return 行のリスト
         */
        @Query(value = "select * from Monitor t order by receive_time desc limit :count", nativeQuery = true)
        List<Monitor> findRowByLastReceiveTime(@Param("count") Integer count);
}
