package com.contents.common.db;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Random;

/**
 * JPAのObjectOptimisticLockingFailureExceptionが発生した時に、AOP方式で、リトライ処理を実施　<br>
 * RetryJpaOptimisticLockでAnnotation方式で、回数を指定する
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class RetryJpaOptimisticLockAspect {

    private int maxRetries;

    public void setMaxRetries(int maxRetries) {
        this.maxRetries = maxRetries;
    }

    @Pointcut("@annotation(RetryJpaOptimisticLock)")
    public void retryOnFailure() {
    }

    @Around("retryOnFailure()")
    public Object retry(ProceedingJoinPoint pjp) throws Throwable {
        // メソッド名
        MethodSignature msig = (MethodSignature) pjp.getSignature();
        // AOP処理のJava Object
        Object target = pjp.getTarget();
        // Annotation情報
        Method currentMethod = target.getClass().getMethod(msig.getName(), msig.getParameterTypes());
        // RetryJpaOptimisticLockを取る
        RetryJpaOptimisticLock annotation = currentMethod.getAnnotation(RetryJpaOptimisticLock.class);
        // リトライ処理回数を取る
        this.setMaxRetries(annotation.times());

        int count = 0;
        Random rand = new Random(25);
        OptimisticLockingFailureException lockFailureException;
        do {
            count++;
            try {
				//リトライ間隔は、0~199ミリ秒のランダム時間
                int delayMsTime = rand.nextInt(200);
                Thread.sleep(delayMsTime);
				
                // OptimisticLockingFailureExceptionを発生した処理を再実施
                return pjp.proceed();
            } catch (OptimisticLockingFailureException ex) {
                log.warn("JPAのObjectOptimisticLockingFailureExceptionを再実施失敗, count:{}", count);
                lockFailureException = ex;
            }
        } while (count < maxRetries);
        log.error("JPAのObjectOptimisticLockingFailureExceptionを".concat(String.valueOf(maxRetries)).concat("回をリトライしましたが、すべて失敗"));
        throw lockFailureException;
    }
}