package com.contents.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "fire-command-system")
public class FireCommandSystemProperties {
	
	/** 機器構成ファイル */
	private String deviceConfiguration;
	
	/** 映像系API */
	private VideoApi videoApi;


	/**
	 * 機器構成ファイルを取得します。
	 * @return 機器構成ファイル
	 */
	public String getDeviceConfiguration() {
	    return deviceConfiguration;
	}


	/**
	 * 機器構成ファイルを設定します。
	 * @param deviceConfiguration 機器構成ファイル
	 */
	public void setDeviceConfiguration(String deviceConfiguration) {
	    this.deviceConfiguration = deviceConfiguration;
	}


	/**
	 * 映像系APIを取得します。
	 * @return 映像系API
	 */
	public VideoApi getVideoApi() {
	    return videoApi;
	}


	/**
	 * 映像系APIを設定します。
	 * @param videoApi 映像系API
	 */
	public void setVideoApi(VideoApi videoApi) {
	    this.videoApi = videoApi;
	}
}
