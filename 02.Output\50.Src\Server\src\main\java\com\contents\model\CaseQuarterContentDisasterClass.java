package com.contents.model;

import java.util.Objects;
import com.contents.model.CaseQuarterContentDisasterClassCaseNo;
import com.contents.model.CaseQuarterContentDisasterClassDisasterType;
import com.contents.model.CaseQuarterContentDisasterClassFireStationName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseQuarterContentDisasterClass
 */
@Validated


public class CaseQuarterContentDisasterClass   {
  @JsonProperty("disaster_type")
  private CaseQuarterContentDisasterClassDisasterType disasterType = null;

  @JsonProperty("case_no")
  private CaseQuarterContentDisasterClassCaseNo caseNo = null;

  @JsonProperty("fire_station_name")
  private CaseQuarterContentDisasterClassFireStationName fireStationName = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  public CaseQuarterContentDisasterClass disasterType(CaseQuarterContentDisasterClassDisasterType disasterType) {
    this.disasterType = disasterType;
    return this;
  }

  /**
   * Get disasterType
   * @return disasterType
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentDisasterClassDisasterType getDisasterType() {
    return disasterType;
  }

  public void setDisasterType(CaseQuarterContentDisasterClassDisasterType disasterType) {
    this.disasterType = disasterType;
  }

  public CaseQuarterContentDisasterClass caseNo(CaseQuarterContentDisasterClassCaseNo caseNo) {
    this.caseNo = caseNo;
    return this;
  }

  /**
   * Get caseNo
   * @return caseNo
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentDisasterClassCaseNo getCaseNo() {
    return caseNo;
  }

  public void setCaseNo(CaseQuarterContentDisasterClassCaseNo caseNo) {
    this.caseNo = caseNo;
  }

  public CaseQuarterContentDisasterClass fireStationName(CaseQuarterContentDisasterClassFireStationName fireStationName) {
    this.fireStationName = fireStationName;
    return this;
  }

  /**
   * Get fireStationName
   * @return fireStationName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentDisasterClassFireStationName getFireStationName() {
    return fireStationName;
  }

  public void setFireStationName(CaseQuarterContentDisasterClassFireStationName fireStationName) {
    this.fireStationName = fireStationName;
  }

  public CaseQuarterContentDisasterClass backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseQuarterContentDisasterClass caseQuarterContentDisasterClass = (CaseQuarterContentDisasterClass) o;
    return Objects.equals(this.disasterType, caseQuarterContentDisasterClass.disasterType) &&
        Objects.equals(this.caseNo, caseQuarterContentDisasterClass.caseNo) &&
        Objects.equals(this.fireStationName, caseQuarterContentDisasterClass.fireStationName) &&
        Objects.equals(this.backgroundColor, caseQuarterContentDisasterClass.backgroundColor);
  }

  @Override
  public int hashCode() {
    return Objects.hash(disasterType, caseNo, fireStationName, backgroundColor);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseQuarterContentDisasterClass {\n");
    
    sb.append("    disasterType: ").append(toIndentedString(disasterType)).append("\n");
    sb.append("    caseNo: ").append(toIndentedString(caseNo)).append("\n");
    sb.append("    fireStationName: ").append(toIndentedString(fireStationName)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
