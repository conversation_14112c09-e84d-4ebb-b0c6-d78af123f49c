package com.contents.model;

import java.util.Objects;
import com.contents.model.ControlSoundDisplaySoundRingData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ControlSound
 */
@Validated


public class ControlSound   {
  @JsonProperty("display_sound_ring_data")
  private ControlSoundDisplaySoundRingData displaySoundRingData = null;

  public ControlSound displaySoundRingData(ControlSoundDisplaySoundRingData displaySoundRingData) {
    this.displaySoundRingData = displaySoundRingData;
    return this;
  }

  /**
   * Get displaySoundRingData
   * @return displaySoundRingData
   **/
  @Schema(description = "")
  
    @Valid
    public ControlSoundDisplaySoundRingData getDisplaySoundRingData() {
    return displaySoundRingData;
  }

  public void setDisplaySoundRingData(ControlSoundDisplaySoundRingData displaySoundRingData) {
    this.displaySoundRingData = displaySoundRingData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControlSound controlSound = (ControlSound) o;
    return Objects.equals(this.displaySoundRingData, controlSound.displaySoundRingData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displaySoundRingData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControlSound {\n");
    
    sb.append("    displaySoundRingData: ").append(toIndentedString(displaySoundRingData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
