package com.contents.common.external.initial;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報) 音量コントローラー
 */
public class SoundVolumeCtrl implements DeviceInterface {

    @JsonProperty("ip_address")
    private String ipAddress;
    
    @JsonProperty("port")
    private Integer port;
    
    @JsonProperty("product_name")
    private String productName;

    @JsonProperty("ip_address")
    public String getIpAddress() {
        return ipAddress;
    }

    @JsonProperty("ip_address")
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    @JsonProperty("port")
    public Integer getPort() {
        return port;
    }

    @JsonProperty("port")
    public void setPort(Integer port) {
        this.port = port;
    }

    @JsonProperty("product_name")
    public String getProductName() {
        return productName;
    }

    @JsonProperty("product_name")
    public void setProductName(String productName) {
        this.productName = productName;
    }

}