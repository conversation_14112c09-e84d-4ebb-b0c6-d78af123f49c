package com.contents.common.db;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * 表示盤喚起音吹鳴テーブル
 */
@Entity
@Data
public class Sound implements Serializable {
    private Integer display_type;

    /**
     * Primary Key
     */
    @Id
    @Column(nullable = false)
    private Integer display_no;

    private Integer sound_no;

    private Integer repeat_count;

    /**
     0：APIから受信, 1: Server処理失敗, 10：機器に通知中, 20:機器で制御成功, 30:機器で制御失敗
     */
    private Integer status;
    
    private Date receive_time;
    
    private Date update_time;
}
