package com.contents.model;

import java.util.Objects;
import com.contents.model.TotalFrequencyContentAggInfo;
import com.contents.model.TotalFrequencyContentAggUnit;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TotalFrequencyContent
 */
@Validated


public class TotalFrequencyContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("agg_unit")
  @Valid
  private List<TotalFrequencyContentAggUnit> aggUnit = null;

  @JsonProperty("agg_info")
  @Valid
  private List<TotalFrequencyContentAggInfo> aggInfo = null;

  public TotalFrequencyContent aggUnit(List<TotalFrequencyContentAggUnit> aggUnit) {
    this.aggUnit = aggUnit;
    return this;
  }

  public TotalFrequencyContent addAggUnitItem(TotalFrequencyContentAggUnit aggUnitItem) {
    if (this.aggUnit == null) {
      this.aggUnit = new ArrayList<TotalFrequencyContentAggUnit>();
    }
    this.aggUnit.add(aggUnitItem);
    return this;
  }

  /**
   * Get aggUnit
   * @return aggUnit
   **/
  @Schema(description = "")
      @Valid
  @Size(max=3)   public List<TotalFrequencyContentAggUnit> getAggUnit() {
    return aggUnit;
  }

  public void setAggUnit(List<TotalFrequencyContentAggUnit> aggUnit) {
    this.aggUnit = aggUnit;
  }

  public TotalFrequencyContent aggInfo(List<TotalFrequencyContentAggInfo> aggInfo) {
    this.aggInfo = aggInfo;
    return this;
  }

  public TotalFrequencyContent addAggInfoItem(TotalFrequencyContentAggInfo aggInfoItem) {
    if (this.aggInfo == null) {
      this.aggInfo = new ArrayList<TotalFrequencyContentAggInfo>();
    }
    this.aggInfo.add(aggInfoItem);
    return this;
  }

  /**
   * Get aggInfo
   * @return aggInfo
   **/
  @Schema(description = "")
      @Valid
  @Size(max=5)   public List<TotalFrequencyContentAggInfo> getAggInfo() {
    return aggInfo;
  }

  public void setAggInfo(List<TotalFrequencyContentAggInfo> aggInfo) {
    this.aggInfo = aggInfo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TotalFrequencyContent totalFrequencyContent = (TotalFrequencyContent) o;
    return Objects.equals(this.aggUnit, totalFrequencyContent.aggUnit) &&
        Objects.equals(this.aggInfo, totalFrequencyContent.aggInfo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(aggUnit, aggInfo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TotalFrequencyContent {\n");
    
    sb.append("    aggUnit: ").append(toIndentedString(aggUnit)).append("\n");
    sb.append("    aggInfo: ").append(toIndentedString(aggInfo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
