package com.contents.devicemodel;

public class ConnectorInfo {

	public ConnectorInfo(Integer connectorNumber, String connectorName) {
		this.connectorNumber = connectorNumber;
		this.connectorName = connectorName;
	}
	
	/** コネクター番号 */
	private Integer connectorNumber;
	
	/** コネクター名称 */
	private String connectorName;

	/**
	 * コネクター番号を取得します。
	 * @return コネクター番号
	 */
	public Integer getConnectorNumber() {
	    return connectorNumber;
	}

	/**
	 * コネクター番号を設定します。
	 * @param connectorNumber コネクター番号
	 */
	public void setConnectorNumber(Integer connectorNumber) {
	    this.connectorNumber = connectorNumber;
	}

	/**
	 * コネクター名称を取得します。
	 * @return コネクター名称
	 */
	public String getConnectorName() {
	    return connectorName;
	}

	/**
	 * コネクター名称を設定します。
	 * @param connectorName コネクター名称
	 */
	public void setConnectorName(String connectorName) {
	    this.connectorName = connectorName;
	}
	
	
}
