#############環境に合わせて設定を変更する必要(<PERSON><PERSON>に配置する前提)##############
#PostgreのDB設定。DBサーバの設定 必ず環境に合わせて設定する
#spring.datasource.url=*********************************************
spring.datasource.url=*******************************************

spring.datasource.username=dbadmin
spring.datasource.password=dbadmin

#spring.datasource.username=postgres
#spring.datasource.password=venetex

server.servlet.contextPath=/contentserver
server.port=8080

spring.devtools.remote.restart.enabled=true
spring.devtools.livereload.enabled=true

#spring.jpa.show-sql=true
#logging.level.org.hibernate.SQL=debug

#DBのAPI履歴の最大保存期間(月単位)。これ以上を超えると、自動的に削除される
fire-command-system.video-api.history-keep-month=120
api.history.max.keep.month=60

# APIのタイムアウト時間(ms単位)。10000の場合、10秒
#api.timeout=10000
fire-command-system.video-api.api-timeout=10000




#############あまり変更する必要がないデフォルト設定##############
# Log
#logging.file.name=ContentServer.log
#logging.file.name=/var/log/ContentServer.log
#logging.level.root=INFO



# 外部構成のJSONファイル名 (resourceフォルダ内のファイル)
app.configuration.external.filename=external_configuration.json
fire-command-system.device-configuration=device_configuration.json


# 機器制御有効化設定。 true: 機器制御有効, false: 機器制御無効(デバッグ用)
app.configuration.external.machine-control-enable=false
#fire-command-system.video-api.device-control=false
