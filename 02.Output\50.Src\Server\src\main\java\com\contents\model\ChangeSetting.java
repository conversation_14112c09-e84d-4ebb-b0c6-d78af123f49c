package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ChangeSetting
 */
@Validated


public class ChangeSetting   {
  @JsonProperty("change_time")
  private Integer changeTime = null;

  @JsonProperty("change_size")
  private Integer changeSize = null;

  public ChangeSetting changeTime(Integer changeTime) {
    this.changeTime = changeTime;
    return this;
  }

  /**
   * 切替時間.同階層の配列を切り替える間隔. 秒数を設定する. 0の場合は切り替えを行わない.0以上の整数を設定する
   * @return changeTime
   **/
  @Schema(description = "切替時間.同階層の配列を切り替える間隔. 秒数を設定する. 0の場合は切り替えを行わない.0以上の整数を設定する")
  
    public Integer getChangeTime() {
    return changeTime;
  }

  public void setChangeTime(Integer changeTime) {
    this.changeTime = changeTime;
  }

  public ChangeSetting changeSize(Integer changeSize) {
    this.changeSize = changeSize;
    return this;
  }

  /**
   * 切替サイズ
   * @return changeSize
   **/
  @Schema(description = "切替サイズ")
  
    public Integer getChangeSize() {
    return changeSize;
  }

  public void setChangeSize(Integer changeSize) {
    this.changeSize = changeSize;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChangeSetting changeSetting = (ChangeSetting) o;
    return Objects.equals(this.changeTime, changeSetting.changeTime) &&
        Objects.equals(this.changeSize, changeSetting.changeSize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(changeTime, changeSize);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChangeSetting {\n");
    
    sb.append("    changeTime: ").append(toIndentedString(changeTime)).append("\n");
    sb.append("    changeSize: ").append(toIndentedString(changeSize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
