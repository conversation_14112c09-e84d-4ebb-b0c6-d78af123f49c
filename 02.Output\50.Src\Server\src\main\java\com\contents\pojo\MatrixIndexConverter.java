package com.contents.pojo;

import java.util.HashMap;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * マトリックスインデックスコンバータークラス
 * <p>
 * // 沖電気の採番基準で全てのモニターを0番目の面番号に集約する
 */
@Slf4j
public class MatrixIndexConverter {

	/** モニター全体の横幅 */
	private int totalWidth;

	/** モニター全体の縦幅 */
	private int totalHeight;

	/** モニターの横の数（モニター1行に並ぶモニター数） */
	private int monitorWidthCount;

	/** モニターの縦の数（モニターの数の縦方向） */
	private int monitorHeightCount;

	/** 1モニターの横幅（コンテンツの数） */
	private int windowWidthCount;

	/** 1モニターの縦幅（コンテンツの数） */
	int windowHeightCount;
	
	private Map<Integer, String[]> rowAlignMap = new HashMap<Integer, String[]>() {
		{
			put(2, new String[] {"left", "right"});
		}
	};

	/**
	 * コンストラクタ
	 * @param monitorHeightCount モニターの縦の数
	 * @param monitorWidthCountモニターの横の数
	 * @param contentWidthCount 1モニターの横幅（コンテンツの数）
	 * @param contentHeightCount 1モニターの縦幅（コンテンツの数）
	 */
	public MatrixIndexConverter(int monitorHeightCount, int monitorWidthCount, int contentWidthCount, int contentHeightCount) {
		
		this.monitorHeightCount = monitorHeightCount;
		this.monitorWidthCount = monitorWidthCount;
		this.windowWidthCount = contentWidthCount;
		this.windowHeightCount = contentHeightCount;
		
		this.totalWidth = contentWidthCount * monitorWidthCount;
		this.totalHeight = contentHeightCount * monitorHeightCount;
	}

	public MatrixIndexInfo convertMatrixIndex(int index) {
		
		int row = index / this.totalWidth;
		int col = index % this.totalWidth;

		// モニターの位置（横方向と縦方向）を計算
		int monitorRow = row / this.windowHeightCount;
		int monitorCol = col / this.windowWidthCount;

		// モニター番号を計算（横方向のモニター位置と縦方向のモニター位置を合わせた計算）
		int monitor = monitorRow * this.monitorWidthCount + monitorCol;

		MatrixIndexInfo info = new MatrixIndexInfo();

		// basicIndex (モニター内でのインデックス)
		int localRow = row % this.windowHeightCount;
		int localCol = col % this.windowWidthCount;
		int indexInMonitor = localRow * this.windowWidthCount + localCol;
		info.setBasicIndex(indexInMonitor);

		// 行優先順インデックス
		int indexInRowMajor = localRow * this.windowWidthCount + localCol;
		info.setRowPriorityIndex(indexInRowMajor);

		// 列優先順インデックス
		int indexInColumnMajor = localCol * this.windowHeightCount + localRow;
		info.setColumnPriorityIndex(indexInColumnMajor);
		
		int align = indexInRowMajor % this.windowWidthCount;
		
		String[] params = rowAlignMap.get(this.windowWidthCount);
		String rowAlign = params[align];
		info.setRowAlign(rowAlign);

		// ログ出力
		log.info("monitor: {}, basicIndex: {}, rowPriorityIndex: {}, columnPriorityIndex: {}, columnPosition: {}",
				monitor, info.getBasicIndex(), info.getRowPriorityIndex(), info.getColumnPriorityIndex(), info.getRowAlign());

		return info;
	}

	
}
