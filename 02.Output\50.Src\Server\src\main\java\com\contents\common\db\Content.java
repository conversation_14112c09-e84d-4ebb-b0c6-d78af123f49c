package com.contents.common.db;

import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * DBのコンテンツテーブル
 */
@Entity
@Data
@TypeDefs({
    @TypeDef(name = "json", typeClass = JsonType.class)
})
public class Content implements Serializable {
    @EmbeddedId
    ContentPK pk;

    private String source_name;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    @Basic(fetch = FetchType.LAZY)
    private String source_data;

    /**
     * -1 廃棄
     * 0 指令系APから来たばかり
     * 1 Browserに通知済み
     * 2 Browserに制御済み
     */
    private Integer status;
    
    private Date receive_time;
    
    private Date update_time;
}
