package com.contents.manager;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.springframework.stereotype.Component;

@Component
public class KeyLockManager {

    // keyごとにReentrantLockを保持するConcurrentHashMap
    private ConcurrentHashMap<String, Lock> lockMap = new ConcurrentHashMap<>();

    // keyに対してロックをかける
    public void lock(String key) {
        // キーに基づいてロックを取得 (存在しない場合は新しく作成)
        Lock lock = lockMap.computeIfAbsent(key, k -> new ReentrantLock());

        // ロックを取得
        lock.lock();
    }

    // keyに対してロックを解除する
    public void unlock(String key) {
        Lock lock = lockMap.get(key);

        if (lock != null) {
            // ロックを解放
            lock.unlock();
        }
    }
}

