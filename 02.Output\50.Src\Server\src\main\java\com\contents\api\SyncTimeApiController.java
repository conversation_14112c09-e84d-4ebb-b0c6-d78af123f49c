package com.contents.api;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.contents.common.CommonUtil;
import com.contents.manager.TaskQueue;
import com.contents.model.ApiResult;
import com.contents.model.SyncTime;
import com.contents.service.SystemService;
import com.contents.service.UtilityService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;

/**
 * 3.28時刻同期API
 */
@Slf4j
@RestController
public class SyncTimeApiController implements SyncTimeApi {
    private final ObjectMapper objectMapper;

    private final HttpServletRequest request;

    private final SystemService systemService;

    private final UtilityService utilityService;

    @Autowired
    public SyncTimeApiController(ObjectMapper objectMapper, HttpServletRequest request, SystemService systemService, UtilityService utilityService, TaskQueue taskQueue) {
        this.objectMapper = objectMapper;
        this.request = request;
        this.systemService = systemService;
        this.utilityService = utilityService;
    }

    public ResponseEntity<ApiResult> syncTimePost(@Parameter(in = ParameterIn.DEFAULT, description = "", required=true, schema=@Schema()) @Valid @RequestBody SyncTime body) {
        String accept = request.getHeader("Accept");
        if (accept == null || !accept.contains("application/json")) {
            return utilityService.getApiResultNotJson(request.getRequestURI());
        }
        if (body.getTime() == null) {
            return utilityService.getApiResultNoApiData(request.getRequestURI());
        }
        
        try {
            // 0. 履歴保存
            var history = systemService.saveApiBody2History(request, objectMapper.writeValueAsString(body));

            // 1. 制御
            var runResult = systemService.executeTimeSync(body);
            var responseResult = new ResponseEntity<ApiResult>(
                        new ApiResult(runResult ? CommonUtil.API_RESULT_SUCCESS : CommonUtil.API_RESULT_FAIL), HttpStatus.OK);

            utilityService.saveApiResult2History(history, responseResult);
            return responseResult;
        } catch (JsonProcessingException e) {
            return utilityService.getApiResultInternalError(request.getRequestURI(), e);
        }
    }
}
