package com.contents.common.external.initial;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報) 表示盤
 */
public class Display implements DeviceInterface {

    @JsonProperty("display_no")
    private Integer displayNo;
    
    @JsonProperty("display_type")
    private Integer displayType;
    
    @JsonProperty("product_name")
    private String productName;
    
    @JsonProperty("sound_volume_ctrl_ch")
    private Integer soundVolumeCtrlCh;
    
    @JsonProperty("matrix_sw_sound_output_no")
    private Integer matrixSwSoundOutputNo;
    
    @JsonProperty("display_split_list")
    private List<DisplaySplit> displaySplitList = null;

    @JsonProperty("display_no")
    public Integer getDisplayNo() {
        return displayNo;
    }

    @JsonProperty("display_no")
    public void setDisplayNo(Integer displayNo) {
        this.displayNo = displayNo;
    }

    @JsonProperty("display_type")
    public Integer getDisplayType() {
        return displayType;
    }

    @JsonProperty("display_type")
    public void setDisplayType(Integer displayType) {
        this.displayType = displayType;
    }

    @JsonProperty("product_name")
    public String getProductName() {
        return productName;
    }

    @JsonProperty("product_name")
    public void setProductName(String productName) {
        this.productName = productName;
    }

    @JsonProperty("sound_volume_ctrl_ch")
    public Integer getSoundVolumeCtrlCh() {
        return soundVolumeCtrlCh;
    }

    @JsonProperty("sound_volume_ctrl_ch")
    public void setSoundVolumeCtrlCh(Integer soundVolumeCtrlCh) {
        this.soundVolumeCtrlCh = soundVolumeCtrlCh;
    }

    @JsonProperty("matrix_sw_sound_output_no")
    public Integer getMatrixSwSoundOutputNo() {
        return matrixSwSoundOutputNo;
    }

    @JsonProperty("matrix_sw_sound_output_no")
    public void setMatrixSwSoundOutputNo(Integer matrixSwSoundOutputNo) {
        this.matrixSwSoundOutputNo = matrixSwSoundOutputNo;
    }

    @JsonProperty("display_split_list")
    public List<DisplaySplit> getDisplaySplitList() {
        return displaySplitList;
    }

    @JsonProperty("display_split_list")
    public void setDisplaySplitList(List<DisplaySplit> displaySplitList) {
        this.displaySplitList = displaySplitList;
    }

}