package com.contents.common.external.initial;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報) マトリクススイッチャ映像ソース
 */
public class VideoSource implements DeviceInterface {

    @JsonProperty("video_source_no")
    private Integer videoSourceNo;
    
    @JsonProperty("video_source_name")
    private String videoSourceName;
    
    @JsonProperty("matrix_sw_input_no")
    private Integer matrixSwInputNo;

    @JsonProperty("video_source_no")
    public Integer getVideoSourceNo() {
        return videoSourceNo;
    }

    @JsonProperty("video_source_no")
    public void setVideoSourceNo(Integer videoSourceNo) {
        this.videoSourceNo = videoSourceNo;
    }

    @JsonProperty("video_source_name")
    public String getVideoSourceName() {
        return videoSourceName;
    }

    @JsonProperty("video_source_name")
    public void setVideoSourceName(String videoSourceName) {
        this.videoSourceName = videoSourceName;
    }

    @JsonProperty("matrix_sw_input_no")
    public Integer getMatrixSwInputNo() {
        return matrixSwInputNo;
    }

    @JsonProperty("matrix_sw_input_no")
    public void setMatrixSwInputNo(Integer matrixSwInputNo) {
        this.matrixSwInputNo = matrixSwInputNo;
    }

}