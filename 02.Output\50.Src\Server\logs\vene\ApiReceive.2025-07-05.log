2025-07-05 15:23:50 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /saveFrontError
2025-07-05 15:23:50 INFO  com.contents.configuration.RequestLoggingFilter - request. /saveFrontError
{
  "name" : "TypeError",
  "stack" : "TypeError: Cannot assign to read only property 'barTitle' of object '#<Object>'\n    at Weather (http://localhost:8088/contentapp/static/js/bundle.js:4749:18)\n    at renderWithHooks (http://localhost:8088/contentapp/static/js/bundle.js:39385:22)\n    at mountIndeterminateComponent (http://localhost:8088/contentapp/static/js/bundle.js:43050:17)\n    at beginWork (http://localhost:8088/contentapp/static/js/bundle.js:44518:20)\n    at beginWork$1 (http://localhost:8088/contentapp/static/js/bundle.js:50280:18)\n    at performUnitOfWork (http://localhost:8088/contentapp/static/js/bundle.js:49438:16)\n    at workLoopSync (http://localhost:8088/contentapp/static/js/bundle.js:49351:9)\n    at renderRootSync (http://localhost:8088/contentapp/static/js/bundle.js:49320:11)\n    at recoverFromConcurrentError (http://localhost:8088/contentapp/static/js/bundle.js:48728:24)\n    at performConcurrentWorkOnRoot (http://localhost:8088/contentapp/static/js/bundle.js:48629:26)",
  "componentStack" : "\n    at Weather (http://localhost:8088/contentapp/static/js/bundle.js:4749:26)\n    at SplitScreen (http://localhost:8088/contentapp/static/js/bundle.js:3748:88)\n    at div\n    at div\n    at div\n    at Home (http://localhost:8088/contentapp/static/js/bundle.js:6643:86)\n    at Routes (http://localhost:8088/contentapp/static/js/bundle.js:55331:5)\n    at header\n    at div\n    at App\n    at StompSessionProvider (http://localhost:8088/contentapp/static/js/bundle.js:55446:5)\n    at ErrorBoundary (http://localhost:8088/contentapp/static/js/bundle.js:52837:37)\n    at RootComponent (http://localhost:8088/contentapp/static/js/bundle.js:6414:81)\n    at Router (http://localhost:8088/contentapp/static/js/bundle.js:55264:15)\n    at BrowserRouter (http://localhost:8088/contentapp/static/js/bundle.js:54073:5)"
}
2025-07-05 17:37:57 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-05 17:37:57 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 18,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 1,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "代",
            "text_color" : "#ffff00",
            "background_color" : "#00ffff"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : "代"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大字数",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中２",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中3",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中4",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中３１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中４１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "機材車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "急",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#faff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急東1",
            "text_color" : "#00ff00",
            "background_color" : "#000000"
          }, {
            "display_text" : "東1",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東２",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東広報車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ffee",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 2,
            "lighting_text_color" : "#ff3300",
            "lighting_background_color" : "#00ffaa",
            "blink_speed" : 3000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ffff00",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-05 17:43:17 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /content
2025-07-05 17:43:17 INFO  com.contents.configuration.RequestLoggingFilter - request. /content
{
  "data" : {
    "display_content_data" : [ {
      "source_no" : 18,
      "source_name" : "車両コンテンツ",
      "source_split_no" : 0,
      "source_data" : {
        "is_deployment" : 1,
        "title_name" : [ {
          "display_text" : "あいうえおかきくけこさしすせ",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "代",
            "text_color" : "#ffff00",
            "background_color" : "#00ffff"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : "代"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大字数",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中２",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中3",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中4",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中３１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "中４１",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "機材車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "最大文字丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "急",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          }, {
            "display_text" : ""
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 5,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 500,
            "lighting_status" : "3"
          }, {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#faff00",
            "blink_speed" : 1000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "car_name" : [ {
            "display_text" : "救急東1",
            "text_color" : "#00ff00",
            "background_color" : "#000000"
          }, {
            "display_text" : "東1",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東２",
            "text_color" : "#ff0000",
            "background_color" : "#000000"
          }, {
            "display_text" : "東広報車",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ffee",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 0
          }, {
            "blink_time" : 2,
            "lighting_text_color" : "#ff3300",
            "lighting_background_color" : "#00ffaa",
            "blink_speed" : 3000,
            "lighting_status" : "3"
          } ]
        }, {
          "display_text" : "南",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ffff00",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "中消防署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffff00",
            "background_color" : "#00ff00"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "東分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        }, {
          "display_text" : "南分署",
          "text_color" : "#000000",
          "background_color" : "#ffffff",
          "deployment" : [ {
            "display_text" : "出",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "代",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "car_name" : [ {
            "display_text" : "救急中1",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "救急中2",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "town_name" : [ {
            "display_text" : "赤坂2丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "赤坂3丁目",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "disaster_type" : [ {
            "display_text" : "急病",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "avm_dynamic_state" : [ {
            "display_text" : "現着",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          }, {
            "display_text" : "待機",
            "text_color" : "#ffffff",
            "background_color" : "#000000"
          } ],
          "lighting_setting" : [ {
            "blink_time" : 10,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 1000,
            "lighting_status" : "1"
          }, {
            "blink_time" : 100,
            "lighting_text_color" : "#ff0000",
            "lighting_background_color" : "#00ff00",
            "blink_speed" : 10,
            "lighting_status" : "2"
          } ]
        } ]
      }
    } ]
  }
}
2025-07-05 17:43:49 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /saveFrontError
2025-07-05 17:43:49 INFO  com.contents.configuration.RequestLoggingFilter - request. /saveFrontError
{
  "name" : "TypeError",
  "stack" : "TypeError: Cannot assign to read only property 'barTitle' of object '#<Object>'\n    at Weather (http://localhost:8088/contentapp/static/js/bundle.js:4749:18)\n    at renderWithHooks (http://localhost:8088/contentapp/static/js/bundle.js:39385:22)\n    at mountIndeterminateComponent (http://localhost:8088/contentapp/static/js/bundle.js:43050:17)\n    at beginWork (http://localhost:8088/contentapp/static/js/bundle.js:44518:20)\n    at beginWork$1 (http://localhost:8088/contentapp/static/js/bundle.js:50280:18)\n    at performUnitOfWork (http://localhost:8088/contentapp/static/js/bundle.js:49438:16)\n    at workLoopSync (http://localhost:8088/contentapp/static/js/bundle.js:49351:9)\n    at renderRootSync (http://localhost:8088/contentapp/static/js/bundle.js:49320:11)\n    at recoverFromConcurrentError (http://localhost:8088/contentapp/static/js/bundle.js:48728:24)\n    at performConcurrentWorkOnRoot (http://localhost:8088/contentapp/static/js/bundle.js:48629:26)",
  "componentStack" : "\n    at Weather (http://localhost:8088/contentapp/static/js/bundle.js:4749:26)\n    at SplitScreen (http://localhost:8088/contentapp/static/js/bundle.js:3748:88)\n    at div\n    at div\n    at div\n    at Home (http://localhost:8088/contentapp/static/js/bundle.js:6643:86)\n    at Routes (http://localhost:8088/contentapp/static/js/bundle.js:55331:5)\n    at header\n    at div\n    at App\n    at StompSessionProvider (http://localhost:8088/contentapp/static/js/bundle.js:55446:5)\n    at ErrorBoundary (http://localhost:8088/contentapp/static/js/bundle.js:52837:37)\n    at RootComponent (http://localhost:8088/contentapp/static/js/bundle.js:6414:81)\n    at Router (http://localhost:8088/contentapp/static/js/bundle.js:55264:15)\n    at BrowserRouter (http://localhost:8088/contentapp/static/js/bundle.js:54073:5)"
}
2025-07-05 17:46:28 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-05 17:46:28 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-05 17:51:22 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-05 17:51:22 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-05 17:56:46 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-05 17:56:46 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 1,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-05 18:06:52 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-05 18:06:52 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 1,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
2025-07-05 18:14:57 INFO  com.contents.configuration.RequestLoggingFilter - Request path: /control_monitor
2025-07-05 18:14:57 INFO  com.contents.configuration.RequestLoggingFilter - request. /control_monitor
{
  "display_control_data" : [ {
    "display_type" : "0",
    "display_no" : 0,
    "display_name" : "指令センター表示盤",
    "display_split_data" : [ {
      "display_split_no" : 0,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 1,
          "source_name" : "車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    }, {
      "display_split_no" : 2,
      "display_details_split_data" : [ {
        "display_details_split_no" : 0,
        "display_content_data" : {
          "source_no" : 18,
          "source_name" : "拡張車両コンテンツ情報更新",
          "source_split_no" : 0,
          "source_disp_pat" : 0,
          "source_vol_control_type" : 2,
          "source_vol_mute_control" : 0,
          "input_switch" : 0,
          "is_vol_control" : 1
        }
      } ]
    } ]
  } ]
}
