package com.contents.devicemodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ExecutionPlanManager {

	private HashMap<Integer, List<ExecutionPlan>> executionPlan = new HashMap<Integer, List<ExecutionPlan>>();

	public synchronized void put(Integer deviceNumber, ExecutionPlan plan) {

		if (executionPlan.containsKey(deviceNumber)) {

			List<ExecutionPlan> list = executionPlan.get(deviceNumber);
			list.add(plan);
		} else {
			List<ExecutionPlan> list = new ArrayList<ExecutionPlan>();
			list.add(plan);
			executionPlan.put(deviceNumber, list);
		}
	}

	public synchronized void put(Integer deviceNumber, List<ExecutionPlan> plans) {
		
		if (executionPlan.containsKey(deviceNumber)) {

			List<ExecutionPlan> list = executionPlan.get(deviceNumber);
			list.addAll(plans);
		} else {
			List<ExecutionPlan> list = new ArrayList<ExecutionPlan>();
			list.addAll(plans);
			executionPlan.put(deviceNumber, list);
		}
	}
}
