package com.contents.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * AlarmContent
 */
@Validated

public class AlarmContent implements AnyOfcontentDataDisplayContentDataSourceData {
	@JsonProperty("issued_ts")
	@Valid
	private List<AlarmContentIssuedTs> issuedTs = null;

	@JsonProperty("combined_forecast_warning")
	@Valid
	private List<AlarmContentCombinedForecastWarning> combinedForecastWarning = null;

	public AlarmContent issuedTs(List<AlarmContentIssuedTs> issuedTs) {
		this.issuedTs = issuedTs;
		return this;
	}

	public AlarmContent addIssuedTsItem(AlarmContentIssuedTs issuedTsItem) {
		if (this.issuedTs == null) {
			this.issuedTs = new ArrayList<AlarmContentIssuedTs>();
		}
		this.issuedTs.add(issuedTsItem);
		return this;
	}

	/**
	 * Get issuedTs
	 * @return issuedTs
	 **/
	@Schema(description = "")
	@Valid
	@Size(max = 5)
	public List<AlarmContentIssuedTs> getIssuedTs() {
		return issuedTs;
	}

	public void setIssuedTs(List<AlarmContentIssuedTs> issuedTs) {
		this.issuedTs = issuedTs;
	}

	public AlarmContent combinedForecastWarning(List<AlarmContentCombinedForecastWarning> combinedForecastWarning) {
		this.combinedForecastWarning = combinedForecastWarning;
		return this;
	}

	public AlarmContent addCombinedForecastWarningItem(AlarmContentCombinedForecastWarning combinedForecastWarningItem) {
		if (this.combinedForecastWarning == null) {
			this.combinedForecastWarning = new ArrayList<AlarmContentCombinedForecastWarning>();
		}
		this.combinedForecastWarning.add(combinedForecastWarningItem);
		return this;
	}

	/**
	 * Get combinedForecastWarning
	 * @return combinedForecastWarning
	 **/
	@Schema(description = "")
	@Valid
	@Size(max = 5)
	public List<AlarmContentCombinedForecastWarning> getCombinedForecastWarning() {
		return combinedForecastWarning;
	}

	public void setCombinedForecastWarning(List<AlarmContentCombinedForecastWarning> combinedForecastWarning) {
		this.combinedForecastWarning = combinedForecastWarning;
	}

	@Override
	public boolean equals(java.lang.Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		AlarmContent alarmContent = (AlarmContent) o;
		return Objects.equals(this.issuedTs, alarmContent.issuedTs) &&
				Objects.equals(this.combinedForecastWarning, alarmContent.combinedForecastWarning);
	}

	@Override
	public int hashCode() {
		return Objects.hash(issuedTs, combinedForecastWarning);
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("class AlarmContent {\n");

		sb.append("    issuedTs: ").append(toIndentedString(issuedTs)).append("\n");
		sb.append("    combinedForecastWarning: ").append(toIndentedString(combinedForecastWarning)).append("\n");
		sb.append("}");
		return sb.toString();
	}

	/**
	 * Convert the given object to string with each line indented by 4 spaces
	 * (except the first line).
	 */
	private String toIndentedString(java.lang.Object o) {
		if (o == null) {
			return "null";
		}
		return o.toString().replace("\n", "\n    ");
	}
}
