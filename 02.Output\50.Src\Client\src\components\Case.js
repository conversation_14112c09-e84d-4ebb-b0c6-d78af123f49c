import React from 'react';
import Cell from './elements/Cell';
import ArrayScroll from './elements/ArrayScroll';
import { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';

/**
 * 事案コンテンツ<br>
 * propsは、「3.13事案コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module Case
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Case = (props) => {
  let row1Cell1Props, row1Cell2Props, row1Cell3Props, row1Props;
  let row2Cell1Props, row2Cell2Props;
  let row3Cell1Props, row3Cell2Props;
  let row5Cell1Props;
  let row6Cell1Props, row6Props;
  const Max_Cars = 12;
  const Max_Hospitals = 1;

  if (isValidSource(props)) {
    let obj = props.disaster_class;

    row1Props = { backgroundColor: getHexColor(obj.background_color) };
    row1Cell1Props = getCellFace(obj.disaster_type, 'col-span-4');
    row1Cell2Props = getCellFace(obj.case_no, 'col-span-7 col-start-6');
    row1Cell3Props = getCellFace(
      obj.fire_station_name,
      'col-span-4 col-start-14'
    );

    row2Cell1Props = getCellFace(props.town_name, 'col-span-6 w-fit');
    row2Cell2Props = getCellFace(props.target_name, 'col-span-9 col-start-8 w-fit');

    row3Cell1Props = getCellFace(
      props.awareness_time,
      'col-span-8 col-start-2 w-fit'
    );
    row3Cell2Props = getCellFace(props.command_time, 'col-span-8 col-start-12 w-fit');

    row5Cell1Props = getCellFace(
      props.latest_dynamic_state_time,
      'col-span-15 col-start-2 text-center'
    );

    row6Cell1Props = getCellFace(
      props.disaster_dynamic_state,
      'col-span-4 col-start-6 text-center'
    );
    row6Props = {
      backgroundColor: getHexColor(
        props.disaster_dynamic_state.background_color
      ),
    };

    // /**
    //  * 病院があるときに、
    //  * 表示位置：No.10の「直近動態時刻」
    //   最大文字数：15文字
    //   文字色と背景色：「直近動態時刻」と同じ
    //  */
    // props.transport_hospital?.display_data.forEach((element) => {
    //   element.text_color = props.latest_dynamic_state_time.text_color;
    //   element.background_color =
    //     props.latest_dynamic_state_time.background_color;
    // });
    // 病院があるときに、病院の色を表示するように仕様変更 20230118
  }

  return (
    <>
      {isValidSource(props) && (
        <div className="leading-[1] text-6xl">
          <div
            style={row1Props}
            className="border-transparent border-x-[1rem] grid grid-cols-[repeat(4,7rem)_minmax(0.25rem,1fr)_repeat(7,7rem)_minmax(0.25rem,1fr)_repeat(4,7rem)] text-7xl"
          >
            <Cell {...row1Cell1Props} />
            <Cell {...row1Cell2Props} />
            <Cell {...row1Cell3Props} />
          </div>
          <div className="border-transparent border-x-[1rem] grid grid-cols-16 text-7xl mt-[3.1rem]">
            <Cell {...row2Cell1Props} />
            <Cell {...row2Cell2Props} />
          </div>
          <div className="grid grid-cols-[minmax(3.5rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(3.5rem,2fr)] text-6xl auto-cols-fr my-[2.9rem]">
            <Cell {...row3Cell1Props} />
            <Cell {...row3Cell2Props} />
          </div>

          <ArrayScroll
            max={Max_Cars}
            gridLevelProps={
              'grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr text-6xl leading-[1] gap-y-[1.2rem] gap-x-[1.2rem]'
            }
            cellLevelProps={[
              'col-span-4 col-start-2',
              'col-span-4',
              'col-span-4',
              'col-span-4',
            ]}
            display_data={props.car_name.display_data}
            change_setting={props.car_name.change_setting}
          />
          {!props.transport_hospital && (
            <div className="grid grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] leading-[1] my-[1.9rem]">
              <Cell {...row5Cell1Props} />
            </div>
          )}
          {props.transport_hospital && (
            <div className="leading-[1.5]">
              <ArrayScroll
                max={Max_Hospitals}
                gridLevelProps={
                  'grid-cols-[minmax(0.25rem,1fr)_repeat(15,6rem)_minmax(0.25rem,1fr)] text-6xl leading-[1] my-[1.9rem]'
                }
                cellLevelProps={['col-span-15 col-start-2 w-fit']}
                display_data={props.transport_hospital.display_data}
                change_setting={props.transport_hospital.change_setting}
              />
            </div>
          )}

          <div
            style={row6Props}
            className="grid grid-cols-14 text-8xl auto-cols-fr leading-[1]"
          >
            <Cell {...row6Cell1Props} />
          </div>
        </div>
      )}
    </>
  );
};

export default Case;
