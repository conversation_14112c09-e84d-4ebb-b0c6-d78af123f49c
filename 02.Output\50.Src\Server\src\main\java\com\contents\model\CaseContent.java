package com.contents.model;

import java.util.Objects;
import com.contents.model.CaseContentAwarenessTime;
import com.contents.model.CaseContentCommandTime;
import com.contents.model.CaseContentDisasterDynamicState;
import com.contents.model.CaseContentLatestDynamicStateTime;
import com.contents.model.CaseContentTargetName;
import com.contents.model.CaseContentTransportHospital;
import com.contents.model.CaseHalfContentCarName;
import com.contents.model.CaseQuarterContentDisasterClass;
import com.contents.model.CaseQuarterContentTownName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseContent
 */
@Validated


public class CaseContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("disaster_class")
  private CaseQuarterContentDisasterClass disasterClass = null;

  @JsonProperty("town_name")
  private CaseQuarterContentTownName townName = null;

  @JsonProperty("target_name")
  private CaseContentTargetName targetName = null;

  @JsonProperty("awareness_time")
  private CaseContentAwarenessTime awarenessTime = null;

  @JsonProperty("command_time")
  private CaseContentCommandTime commandTime = null;

  @JsonProperty("car_name")
  private CaseHalfContentCarName carName = null;

  @JsonProperty("latest_dynamic_state_time")
  private CaseContentLatestDynamicStateTime latestDynamicStateTime = null;

  @JsonProperty("disaster_dynamic_state")
  private CaseContentDisasterDynamicState disasterDynamicState = null;

  @JsonProperty("transport_hospital")
  private CaseContentTransportHospital transportHospital = null;

  public CaseContent disasterClass(CaseQuarterContentDisasterClass disasterClass) {
    this.disasterClass = disasterClass;
    return this;
  }

  /**
   * Get disasterClass
   * @return disasterClass
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentDisasterClass getDisasterClass() {
    return disasterClass;
  }

  public void setDisasterClass(CaseQuarterContentDisasterClass disasterClass) {
    this.disasterClass = disasterClass;
  }

  public CaseContent townName(CaseQuarterContentTownName townName) {
    this.townName = townName;
    return this;
  }

  /**
   * Get townName
   * @return townName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentTownName getTownName() {
    return townName;
  }

  public void setTownName(CaseQuarterContentTownName townName) {
    this.townName = townName;
  }

  public CaseContent targetName(CaseContentTargetName targetName) {
    this.targetName = targetName;
    return this;
  }

  /**
   * Get targetName
   * @return targetName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseContentTargetName getTargetName() {
    return targetName;
  }

  public void setTargetName(CaseContentTargetName targetName) {
    this.targetName = targetName;
  }

  public CaseContent awarenessTime(CaseContentAwarenessTime awarenessTime) {
    this.awarenessTime = awarenessTime;
    return this;
  }

  /**
   * Get awarenessTime
   * @return awarenessTime
   **/
  @Schema(description = "")
  
    @Valid
    public CaseContentAwarenessTime getAwarenessTime() {
    return awarenessTime;
  }

  public void setAwarenessTime(CaseContentAwarenessTime awarenessTime) {
    this.awarenessTime = awarenessTime;
  }

  public CaseContent commandTime(CaseContentCommandTime commandTime) {
    this.commandTime = commandTime;
    return this;
  }

  /**
   * Get commandTime
   * @return commandTime
   **/
  @Schema(description = "")
  
    @Valid
    public CaseContentCommandTime getCommandTime() {
    return commandTime;
  }

  public void setCommandTime(CaseContentCommandTime commandTime) {
    this.commandTime = commandTime;
  }

  public CaseContent carName(CaseHalfContentCarName carName) {
    this.carName = carName;
    return this;
  }

  /**
   * Get carName
   * @return carName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseHalfContentCarName getCarName() {
    return carName;
  }

  public void setCarName(CaseHalfContentCarName carName) {
    this.carName = carName;
  }

  public CaseContent latestDynamicStateTime(CaseContentLatestDynamicStateTime latestDynamicStateTime) {
    this.latestDynamicStateTime = latestDynamicStateTime;
    return this;
  }

  /**
   * Get latestDynamicStateTime
   * @return latestDynamicStateTime
   **/
  @Schema(description = "")
  
    @Valid
    public CaseContentLatestDynamicStateTime getLatestDynamicStateTime() {
    return latestDynamicStateTime;
  }

  public void setLatestDynamicStateTime(CaseContentLatestDynamicStateTime latestDynamicStateTime) {
    this.latestDynamicStateTime = latestDynamicStateTime;
  }

  public CaseContent disasterDynamicState(CaseContentDisasterDynamicState disasterDynamicState) {
    this.disasterDynamicState = disasterDynamicState;
    return this;
  }

  /**
   * Get disasterDynamicState
   * @return disasterDynamicState
   **/
  @Schema(description = "")
  
    @Valid
    public CaseContentDisasterDynamicState getDisasterDynamicState() {
    return disasterDynamicState;
  }

  public void setDisasterDynamicState(CaseContentDisasterDynamicState disasterDynamicState) {
    this.disasterDynamicState = disasterDynamicState;
  }

  public CaseContent transportHospital(CaseContentTransportHospital transportHospital) {
    this.transportHospital = transportHospital;
    return this;
  }

  /**
   * Get transportHospital
   * @return transportHospital
   **/
  @Schema(description = "")
  
    @Valid
    public CaseContentTransportHospital getTransportHospital() {
    return transportHospital;
  }

  public void setTransportHospital(CaseContentTransportHospital transportHospital) {
    this.transportHospital = transportHospital;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseContent caseContent = (CaseContent) o;
    return Objects.equals(this.disasterClass, caseContent.disasterClass) &&
        Objects.equals(this.townName, caseContent.townName) &&
        Objects.equals(this.targetName, caseContent.targetName) &&
        Objects.equals(this.awarenessTime, caseContent.awarenessTime) &&
        Objects.equals(this.commandTime, caseContent.commandTime) &&
        Objects.equals(this.carName, caseContent.carName) &&
        Objects.equals(this.latestDynamicStateTime, caseContent.latestDynamicStateTime) &&
        Objects.equals(this.disasterDynamicState, caseContent.disasterDynamicState) &&
        Objects.equals(this.transportHospital, caseContent.transportHospital);
  }

  @Override
  public int hashCode() {
    return Objects.hash(disasterClass, townName, targetName, awarenessTime, commandTime, carName, latestDynamicStateTime, disasterDynamicState, transportHospital);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseContent {\n");
    
    sb.append("    disasterClass: ").append(toIndentedString(disasterClass)).append("\n");
    sb.append("    townName: ").append(toIndentedString(townName)).append("\n");
    sb.append("    targetName: ").append(toIndentedString(targetName)).append("\n");
    sb.append("    awarenessTime: ").append(toIndentedString(awarenessTime)).append("\n");
    sb.append("    commandTime: ").append(toIndentedString(commandTime)).append("\n");
    sb.append("    carName: ").append(toIndentedString(carName)).append("\n");
    sb.append("    latestDynamicStateTime: ").append(toIndentedString(latestDynamicStateTime)).append("\n");
    sb.append("    disasterDynamicState: ").append(toIndentedString(disasterDynamicState)).append("\n");
    sb.append("    transportHospital: ").append(toIndentedString(transportHospital)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
