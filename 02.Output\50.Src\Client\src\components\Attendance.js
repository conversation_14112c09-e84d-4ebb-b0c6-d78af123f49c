import React from 'react';
import Title from './elements/Title';
import Cell from './elements/Cell';
import { getCellFace, isValidSource } from '../utils/Util.js';

/**
 * 出退コンテンツ<br>
 * propsは、「3.13出退コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module Attendance
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Attendance = (props) => {
  const MAX_ROW = 14;
  return (
    <div className="text-6xl leading-tight">
      <Title title={'出退状況'} />
      {isValidSource(props) && (
        <div className="border-transparent border-x-[1rem] grid grid-cols-2 grid-rows-7 grid-flow-col gap-4 auto-cols-fr leading-[1] gap-y-[2.7rem] gap-x-[7rem] mt-[3rem]">
          {props.official_position_name?.map((item, index) => {
            if (index >= MAX_ROW) return undefined;

            return <AttendanceRow key={index} {...props} index={index} />;
          })}
        </div>
      )}
    </div>
  );
};

const AttendanceRow = (props) => {
  if (
    !props.official_position_name[props.index] ||
    !props.attendance_dynamic_state_name || 
    !props.attendance_dynamic_state_name[props.index]
  ) {
    return;
  }

  let cell1 = getCellFace(
    props.official_position_name[props.index],
    'col-span-6 w-fit'
  );
  let cell2 = getCellFace(
    props.attendance_dynamic_state_name[props.index],
    'col-span-2 col-start-8 w-fit'
  );

  return (
    <div className="grid grid-cols-9 grid-rows-1 auto-cols-fr">
      <Cell {...cell1} />
      <Cell {...cell2} />
    </div>
  );
};

export default Attendance;
