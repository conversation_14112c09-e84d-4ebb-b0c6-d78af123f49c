package com.contents.service;

import java.util.Locale;

import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.db.History;
import com.contents.common.db.HistoryDao;
import com.contents.manager.KeyLockManager;
import com.contents.manager.SystemSettingManager;
import com.contents.manager.TaskQueue;
import com.contents.model.ApiResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 他の処理のUtility処理<br>
 * 主に、各種Response結果の作成
 */
@Service
@Data
@Slf4j
public class UtilityService {

	private final TaskQueue taskQueue;

	private final HistoryDao historyDao;

	private final ObjectMapper objectMapper;

	private final MessageSource messageSource;

	private final KeyLockManager keyLockManager;

	//private Long apiTimeout;

	public UtilityService(TaskQueue taskQueue, HistoryDao historyDao, ObjectMapper objectMapper, MessageSource messageSource, KeyLockManager keyLockManager) {
		this.taskQueue = taskQueue;
		this.historyDao = historyDao;
		this.objectMapper = objectMapper;
		this.messageSource = messageSource;
		this.keyLockManager = keyLockManager;
	}

	/**
	 * BooleanのDeferredResultを作成して返す
	 * @param wsEndpoint 該当WebSocketのEndPoint
	 * @param taskId　Task Id
	 * @return BooleanのDeferredResult
	 */
	public DeferredResult<Boolean> getBooleanDeferredResult(String wsEndpoint, String taskId) {
		Long apiTimeout = SystemSettingManager.getSystemSetting().getApiTimeout();
		DeferredResult<Boolean> deferredResult = new DeferredResult<>(apiTimeout);
		deferredResult.onTimeout(() -> log.warn("getBooleanDeferredResult. {}({}): Timeout", wsEndpoint, taskId));
		deferredResult.onCompletion(() -> log.info("getBooleanDeferredResult. {}({}): Complete", wsEndpoint, taskId));
		deferredResult.onError(throwable -> log.error("getBooleanDeferredResult. {}({}): Error with({})", wsEndpoint, taskId, throwable.toString()));
		return deferredResult;
	}

	/**
	 * 共通のDeferredResultを作成して返す
	 *
	 * @param api API名
	 * @param taskId　Task Id
	 * @return 共通形式のDeferredResult
	 */
	public DeferredResult<ResponseEntity<ApiResult>> getResponseEntityDeferredResult(String api, String taskId, History history, Object body) {
		final ResponseEntity<ApiResult> TIME_OUT_RESULT = getApiResultTimeout();

		//DeferredResultをセットアップ、Timeout時間とTimeout時のAPI結果を設定
		Long apiTimeout = SystemSettingManager.getSystemSetting().getApiTimeout();
		log.info("★☆★ ApiTimeout: {}({}): Start ", api, taskId);
		DeferredResult<ResponseEntity<ApiResult>> result = new DeferredResult<>(apiTimeout, TIME_OUT_RESULT);

		result.onTimeout(() -> {
			try {
				//String jsonString = this.objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(history);
				String jsonString = CommonUtil.loggingJsonConvert(body);
				log.warn("★☆★ {}({}): onTimeout{}{}", api, taskId, System.getProperty("line.separator"), jsonString);
			} catch (Exception e) {
				log.warn(MessageFormatUtil.format("★☆★ {}({}): onTimeout", api, taskId), e);
			}
			synchronized (taskQueue) {
				taskQueue.metricsOutput();
			}
			clearTaskInfo(taskId);
		});
		result.onCompletion(() -> {
			log.info("★☆★ {}({}): onComplete", api, taskId);
			if (history != null) {
				try {
					saveApiResult2History(history, (ResponseEntity<ApiResult>) result.getResult());
				} catch (JsonProcessingException e) {
					log.error(MessageFormatUtil.format("★☆★ {}({}): Write history error", api, taskId), e);
				}
			}
			// Timeoutを含め、ここで関連情報をすべてClear
			clearTaskInfo(taskId);
		});
		result.onError(throwable -> {
			log.error(MessageFormatUtil.format("★☆★ {}({}): onError with({})", api, taskId), throwable);
			clearTaskInfo(taskId);
		});
		return result;
	}

	private void clearTaskInfo(String taskId) {
		synchronized (taskQueue) {
			taskQueue.clearTaskInfo(taskId);
			taskQueue.metricsOutput();
		}
	}

	/**
	 * APIの処理結果を履歴テーブルに保存
	 * @param history 履歴DBEntity
	 * @param result　API処理結果
	 * @throws JsonProcessingException Exception
	 */
	public void saveApiResult2History(History history, ResponseEntity<ApiResult> result)
			throws JsonProcessingException {
		if (result == null) {
			return;
		}

		history.setApi_status(result.getStatusCodeValue());
		history.setApi_result(objectMapper.writeValueAsString(result.getBody()));
		historyDao.save(history);
	}

	/**
	 * タイムアウトのAPIResultを作成して返す
	 * @return タイムアウトのAPIResult
	 */
	public ResponseEntity<ApiResult> getApiResultTimeout() {
		// Timeout結果
		//        String msg = messageSource.getMessage("msg.api.error.timeout", null, Locale.JAPANESE);

		ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_FAIL);
		apiResult.setMsg("Processing was not completed.");
		return new ResponseEntity<>(apiResult, HttpStatus.REQUEST_TIMEOUT);
	}

	/**
	 * Json形式ではないのAPIResultを作成して返す
	 *
	 * @param api　API名
	 * @return Json形式ではないのAPIResult
	 */
	public ResponseEntity<ApiResult> getApiResultNotJson(String api) {
		// Jsonではない
		String msg = messageSource.getMessage("msg.api.error.not.json", null, Locale.JAPANESE);

		ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_FAIL);

		final ResponseEntity<ApiResult> result = new ResponseEntity<>(apiResult, HttpStatus.BAD_REQUEST);
		log.error("{}: {}", api, msg);

		return result;
	}

	/**
	 * ContentなしのAPIResultを作成して返す
	 *
	 * @param api　API名
	 * @return APIResult
	 */
	public ResponseEntity<ApiResult> getApiResultNoApiData(String api) {
		// Contentないメッセージ
		String msg = messageSource.getMessage("msg.api.error.no.api.data", null, Locale.JAPANESE);

		ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_FAIL);
		final ResponseEntity<ApiResult> result = new ResponseEntity<>(apiResult, HttpStatus.NOT_MODIFIED);
		log.error("{}: {}", api, msg);
		return result;
	}

	/**
	 * HttpStatus=500の内部エラーのAPIResultを作成して返す
	 *
	 * @param api　API名
	 * @param e　発生したException
	 * @return APIResult
	 */
	public ResponseEntity<ApiResult> getApiResultInternalError(String api, Exception e) {
		// 内部処理エラーメッセージ
		String msg = messageSource.getMessage("msg.api.error.internal", null, Locale.JAPANESE);

		var errorMsg = MessageFormatUtil.format("{}: {}, {}", api, msg, e.toString());
		log.error(errorMsg);

		ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_FAIL, errorMsg);
		final ResponseEntity<ApiResult> result = new ResponseEntity<>(apiResult, HttpStatus.INTERNAL_SERVER_ERROR);

		return result;
	}
}
