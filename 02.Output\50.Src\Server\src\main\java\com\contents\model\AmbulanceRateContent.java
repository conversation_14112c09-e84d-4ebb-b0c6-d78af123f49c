package com.contents.model;

import java.util.Objects;
import com.contents.model.AmbulanceRateContentDispatchNum;
import com.contents.model.AmbulanceRateContentTitle;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * AmbulanceRateContent
 */
@Validated


public class AmbulanceRateContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("title")
  private AmbulanceRateContentTitle title = null;

  @JsonProperty("dispatch_num")
  private AmbulanceRateContentDispatchNum dispatchNum = null;

  public AmbulanceRateContent title(AmbulanceRateContentTitle title) {
    this.title = title;
    return this;
  }

  /**
   * Get title
   * @return title
   **/
  @Schema(description = "")
  
    @Valid
    public AmbulanceRateContentTitle getTitle() {
    return title;
  }

  public void setTitle(AmbulanceRateContentTitle title) {
    this.title = title;
  }

  public AmbulanceRateContent dispatchNum(AmbulanceRateContentDispatchNum dispatchNum) {
    this.dispatchNum = dispatchNum;
    return this;
  }

  /**
   * Get dispatchNum
   * @return dispatchNum
   **/
  @Schema(description = "")
  
    @Valid
    public AmbulanceRateContentDispatchNum getDispatchNum() {
    return dispatchNum;
  }

  public void setDispatchNum(AmbulanceRateContentDispatchNum dispatchNum) {
    this.dispatchNum = dispatchNum;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AmbulanceRateContent ambulanceRateContent = (AmbulanceRateContent) o;
    return Objects.equals(this.title, ambulanceRateContent.title) &&
        Objects.equals(this.dispatchNum, ambulanceRateContent.dispatchNum);
  }

  @Override
  public int hashCode() {
    return Objects.hash(title, dispatchNum);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AmbulanceRateContent {\n");
    
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    dispatchNum: ").append(toIndentedString(dispatchNum)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
