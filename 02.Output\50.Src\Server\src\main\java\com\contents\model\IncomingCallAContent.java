package com.contents.model;

import java.util.Objects;
import com.contents.model.IncomingCallAContentLineName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * IncomingCallAContent
 */
@Validated


public class IncomingCallAContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("line_name")
  @Valid
  private List<IncomingCallAContentLineName> lineName = null;

  public IncomingCallAContent lineName(List<IncomingCallAContentLineName> lineName) {
    this.lineName = lineName;
    return this;
  }

  public IncomingCallAContent addLineNameItem(IncomingCallAContentLineName lineNameItem) {
    if (this.lineName == null) {
      this.lineName = new ArrayList<IncomingCallAContentLineName>();
    }
    this.lineName.add(lineNameItem);
    return this;
  }

  /**
   * Get lineName
   * @return lineName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=28)   public List<IncomingCallAContentLineName> getLineName() {
    return lineName;
  }

  public void setLineName(List<IncomingCallAContentLineName> lineName) {
    this.lineName = lineName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IncomingCallAContent incomingCallAContent = (IncomingCallAContent) o;
    return Objects.equals(this.lineName, incomingCallAContent.lineName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(lineName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IncomingCallAContent {\n");
    
    sb.append("    lineName: ").append(toIndentedString(lineName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
