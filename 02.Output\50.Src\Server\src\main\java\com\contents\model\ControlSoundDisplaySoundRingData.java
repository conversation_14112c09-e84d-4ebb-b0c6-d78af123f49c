package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ControlSoundDisplaySoundRingData
 */
@Validated


public class ControlSoundDisplaySoundRingData   {
  @JsonProperty("display_type")
  private Integer displayType = null;

  @JsonProperty("display_no")
  private Integer displayNo = null;

  @JsonProperty("sound_no")
  private Integer soundNo = null;

  @JsonProperty("repeat_count")
  private Integer repeatCount = null;

  public ControlSoundDisplaySoundRingData displayType(Integer displayType) {
    this.displayType = displayType;
    return this;
  }

  /**
   * 表示盤の種類毎に付与される識別番号 0：指令センター表示盤(4面マルチ)、1：署所表示盤(単面)、 2：指令台ディスプレイ
   * minimum: 0
   * maximum: 2
   * @return displayType
   **/
  @Schema(description = "表示盤の種類毎に付与される識別番号 0：指令センター表示盤(4面マルチ)、1：署所表示盤(単面)、 2：指令台ディスプレイ")
  
  @Min(0) @Max(2)   public Integer getDisplayType() {
    return displayType;
  }

  public void setDisplayType(Integer displayType) {
    this.displayType = displayType;
  }

  public ControlSoundDisplaySoundRingData displayNo(Integer displayNo) {
    this.displayNo = displayNo;
    return this;
  }

  /**
   * 表示盤種別毎に付与される表示盤の識別番号
   * @return displayNo
   **/
  @Schema(description = "表示盤種別毎に付与される表示盤の識別番号")
  
    public Integer getDisplayNo() {
    return displayNo;
  }

  public void setDisplayNo(Integer displayNo) {
    this.displayNo = displayNo;
  }

  public ControlSoundDisplaySoundRingData soundNo(Integer soundNo) {
    this.soundNo = soundNo;
    return this;
  }

  /**
   * 吹鳴したい喚起音のファイルの番号
   * @return soundNo
   **/
  @Schema(description = "吹鳴したい喚起音のファイルの番号")
  
    public Integer getSoundNo() {
    return soundNo;
  }

  public void setSoundNo(Integer soundNo) {
    this.soundNo = soundNo;
  }

  public ControlSoundDisplaySoundRingData repeatCount(Integer repeatCount) {
    this.repeatCount = repeatCount;
    return this;
  }

  /**
   * ファイルの再生を繰り返す回数
   * @return repeatCount
   **/
  @Schema(description = "ファイルの再生を繰り返す回数")
  
    public Integer getRepeatCount() {
    return repeatCount;
  }

  public void setRepeatCount(Integer repeatCount) {
    this.repeatCount = repeatCount;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControlSoundDisplaySoundRingData controlSoundDisplaySoundRingData = (ControlSoundDisplaySoundRingData) o;
    return Objects.equals(this.displayType, controlSoundDisplaySoundRingData.displayType) &&
        Objects.equals(this.displayNo, controlSoundDisplaySoundRingData.displayNo) &&
        Objects.equals(this.soundNo, controlSoundDisplaySoundRingData.soundNo) &&
        Objects.equals(this.repeatCount, controlSoundDisplaySoundRingData.repeatCount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayType, displayNo, soundNo, repeatCount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControlSoundDisplaySoundRingData {\n");
    
    sb.append("    displayType: ").append(toIndentedString(displayType)).append("\n");
    sb.append("    displayNo: ").append(toIndentedString(displayNo)).append("\n");
    sb.append("    soundNo: ").append(toIndentedString(soundNo)).append("\n");
    sb.append("    repeatCount: ").append(toIndentedString(repeatCount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
