import React from 'react';
import Cell from './elements/Cell';
import Title from './elements/Title';
import { getCellFace, isValidSource } from '../utils/Util.js';
import BlinkBlock from './elements/BlinkBlock';

/**
 * デジタル無線コンテンツ<br>
 * propsは、「3.17デジタル無線コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module DigitalRadio
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const DigitalRadio = (props) => {
  const MAX_ROW = 4;
  return (
    <>
      <Title title={'デジタル無線通信状況'} />
      {isValidSource(props) && (
        <div className="grid min-h-full grid-rows-2 grid-cols-2 place-content-stretch text-5xl leading-[1]">
          {props.wireless_channel_name.map((item, index) => {
            if (index >= MAX_ROW) return undefined;

            return <DigitalRadioSubPart key={index} {...item} index={index} />;
          })}
        </div>
      )}
    </>
  );
};

const DigitalRadioSubPart = (props) => {
  const MAX_ROW = 2;

  let borderStyle = '';
  if (props.index % 2 === 1) {
    borderStyle = 'border-l-2';
  }
  
  let appendGridClassName;

  if (props.index % 2 == 0) {
    appendGridClassName = 'grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)]'
  } else {
    appendGridClassName = 'grid-cols-[1rem_repeat(4,5rem)_minmax(0.25rem,1fr)_repeat(3,5rem)_minmax(0.25rem,1fr)_repeat(5,1.2fr)_1rem]'
  }
  let row1Cell1Props = getCellFace(
      props,
      `col-span-full justify-self-center text-[4.2rem] w-fit`
  );
  return (
    <div
      className={`grid content-start ${appendGridClassName} ${borderStyle} gap-y-10 pt-2`}
    >
      <Cell {...row1Cell1Props} />

      {props.outgoing_call_move_station_name &&
        props.outgoing_call_move_station_name.map((item, index) => {
          if (index >= MAX_ROW) return undefined;

          return <RadioDetailRow key={index} {...props} index={index} rightPadding={props.index % 2 == 1}/>;
        })}
    </div>
  );
};

// デジタル無線コンテンツ
const RadioDetailRow = (props) => {
  let showInfo1 = props.outgoing_call_move_station_name[props.index];
  let showInfoSeperator1 = { ...showInfo1 };
  showInfoSeperator1.display_text = ' ';
  let showInfoSeperator0 = { ...showInfoSeperator1 };

  let showInfo2 = {};
  let showInfoSeperator2 = {};
  if (
    props.incoming_call_move_station_name &&
    props.incoming_call_move_station_name[props.index]
  ) {
    showInfo2 = props.incoming_call_move_station_name[props.index];
    showInfoSeperator2 = { ...showInfo2 };
    showInfoSeperator2.display_text = ' ';
  }

  let showInfo3 = {};
  if (props.incoming_call_ts && props.incoming_call_ts[props.index]) {
    showInfo3 = props.incoming_call_ts[props.index];
  }
  let showInfoSeperator3 = { ...showInfo3 };
  showInfoSeperator3.display_text = ' ';

  let showBlock = [];
  /* 1行で点滅する為、仕様より、Span+1(隙間列)にする */
  showBlock.push({ showInfo: showInfoSeperator0, className: 'col-span-1' });
  showBlock.push({ showInfo: showInfo1, className: 'col-span-4 col-start-2' });
  showBlock.push({ showInfo: showInfoSeperator1, className: 'col-span-1' });
  showBlock.push({ showInfo: showInfo2, className: 'col-span-3 col-start-7' });
  showBlock.push({ showInfo: showInfoSeperator2, className: 'col-span-1' });
  showBlock.push({ showInfo: showInfo3, className: 'col-span-5 col-start-11' });
  if (props.rightPadding) {
    showBlock.push({ showInfo: showInfoSeperator3, className: 'col-span-1' });
  }

  return (
    <BlinkBlock
      block={showBlock}
      blink_setting={
        props.blink_setting ? props.blink_setting[props.index] : {}
      }
    />
  );
};

export default DigitalRadio;
