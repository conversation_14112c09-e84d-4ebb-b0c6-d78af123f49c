package com.contents.common.external.cross;

/**
 * デバイス構成
 * */
public class DeviceConfiguration {

	/** 端末情報 */
	private DeviceInfomation deviceInfomation;

	/** リンク情報 */
	private RoutingInformation routingInformation;
	
	/** 外部機器情報 */
	private ExternalDeviceInformation externalDeviceInformation;

	/**
	 * 端末情報を取得します。
	 * @return 端末情報
	 */
	public DeviceInfomation getDeviceInfomation() {
	    return deviceInfomation;
	}

	/**
	 * 端末情報を設定します。
	 * @param deviceInfomation 端末情報
	 */
	public void setDeviceInfomation(DeviceInfomation deviceInfomation) {
	    this.deviceInfomation = deviceInfomation;
	}

	/**
	 * linkInformationを取得します。
	 * @return linkInformation
	 */
	public RoutingInformation getRoutingInformation() {
	    return routingInformation;
	}

	/**
	 * linkInformationを設定します。
	 * @param linkInformation linkInformation
	 */
	public void setRoutingInformation(RoutingInformation routingInformation) {
	    this.routingInformation = routingInformation;
	}

	/**
	 * 外部機器情報を取得します。
	 * @return 外部機器情報
	 */
	public ExternalDeviceInformation getExternalDeviceInformation() {
	    return externalDeviceInformation;
	}

	/**
	 * 外部機器情報を設定します。
	 * @param externalDeviceInformation 外部機器情報
	 */
	public void setExternalDeviceInformation(ExternalDeviceInformation externalDeviceInformation) {
	    this.externalDeviceInformation = externalDeviceInformation;
	}
	
	
	
}
