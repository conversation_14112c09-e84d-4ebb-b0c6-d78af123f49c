package com.contents.common.db;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

/**
 *　API履歴テーブルのDAO
 */
@Repository
@RepositoryRestResource(path = "history")
public interface HistoryDao extends JpaRepository<History, Long>, Serializable {
    @Transactional
    @Modifying
    @Query(value = "DELETE FROM  history a WHERE a.receive_time < (now() - interval '1 month' * :beforeMonths)", nativeQuery = true)
    Integer deleteBeforeMonths(@Param("beforeMonths") Integer beforeMonths);

    /**
     * 最新の受信時間で、指定したCount行を返す
     *
     * 本来、receive_timeでOrderして、最後のCount行を返すが、receive_timeでDBIndexをない為、処理が遅い。
     * API履歴テーブルのIdをシーケンスで自動作成なので、結果的にreceive_timeと同じ順番です。
     * なので、IDでOrderして、指定したCountでデータを取り出す
     * @param count　返す必要な行数
     * @return 行のリスト
     */
    @Query(value = "select * from History t order by id desc limit :count", nativeQuery = true)
    List<History> findRowByLastReceiveTime(@Param("count") Integer count);
}
