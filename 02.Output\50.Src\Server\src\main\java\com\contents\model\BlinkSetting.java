package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * BlinkSetting
 */
@Validated


public class BlinkSetting   {
  @JsonProperty("blink_time")
  private Integer blinkTime = null;

  @JsonProperty("lighting_text_color")
  private String lightingTextColor = null;

  @JsonProperty("lighting_background_color")
  private String lightingBackgroundColor = null;

  @JsonProperty("lighting_status")
  private String lightingStatus = null;

  @JsonProperty("blink_speed")
  private Integer blinkSpeed = null;

  public BlinkSetting blinkTime(Integer blinkTime) {
    this.blinkTime = blinkTime;
    return this;
  }

  /**
   * 点滅表示を行う時間 秒数を設定する 0の場合は点滅しない 0以上の整数を設定する
   * minimum: 0
   * @return blinkTime
   **/
  @Schema(description = "点滅表示を行う時間 秒数を設定する 0の場合は点滅しない 0以上の整数を設定する")
  
  @Min(0)  public Integer getBlinkTime() {
    return blinkTime;
  }

  public void setBlinkTime(Integer blinkTime) {
    this.blinkTime = blinkTime;
  }

  public BlinkSetting lightingTextColor(String lightingTextColor) {
    this.lightingTextColor = lightingTextColor;
    return this;
  }

  /**
   * 画面に表示するテキストの文字色 16進数のRGB値を設定する  '#00ff00'
   * @return lightingTextColor
   **/
  @Schema(description = "画面に表示するテキストの文字色 16進数のRGB値を設定する  '#00ff00'")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getLightingTextColor() {
    return lightingTextColor;
  }

  public void setLightingTextColor(String lightingTextColor) {
    this.lightingTextColor = lightingTextColor;
  }

  public BlinkSetting lightingBackgroundColor(String lightingBackgroundColor) {
    this.lightingBackgroundColor = lightingBackgroundColor;
    return this;
  }

  /**
   * 画面に表示するテキストの背景色 16進数のRGB値を設定する '#0F0F0F'
   * @return lightingBackgroundColor
   **/
  @Schema(description = "画面に表示するテキストの背景色 16進数のRGB値を設定する '#0F0F0F'")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getLightingBackgroundColor() {
    return lightingBackgroundColor;
  }

  public void setLightingBackgroundColor(String lightingBackgroundColor) {
    this.lightingBackgroundColor = lightingBackgroundColor;
  }

  public BlinkSetting lightingStatus(String lightingStatus) {
    this.lightingStatus = lightingStatus;
    return this;
  }

  /**
   * 該当箇所が点灯などをするかの設定 0~3を設定する
   * @return lightingStatus
   **/
  @Schema(description = "該当箇所が点灯などをするかの設定 0~3を設定する")
  
  @Pattern(regexp="^[0-3]{1}$")   public String getLightingStatus() {
    return lightingStatus;
  }

  public void setLightingStatus(String lightingStatus) {
    this.lightingStatus = lightingStatus;
  }

  public BlinkSetting blinkSpeed(Integer blinkSpeed) {
    this.blinkSpeed = blinkSpeed;
    return this;
  }

  /**
   * 点滅の切り替えを行う速度 ミリ秒を設定する 0の場合は点滅しない 0以上の整数を設定する
   * minimum: 0
   * @return blinkSpeed
   **/
  @Schema(description = "点滅の切り替えを行う速度 ミリ秒を設定する 0の場合は点滅しない 0以上の整数を設定する")
  
  @Min(0)  public Integer getBlinkSpeed() {
    return blinkSpeed;
  }

  public void setBlinkSpeed(Integer blinkSpeed) {
    this.blinkSpeed = blinkSpeed;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BlinkSetting blinkSetting = (BlinkSetting) o;
    return Objects.equals(this.blinkTime, blinkSetting.blinkTime) &&
        Objects.equals(this.lightingTextColor, blinkSetting.lightingTextColor) &&
        Objects.equals(this.lightingBackgroundColor, blinkSetting.lightingBackgroundColor) &&
        Objects.equals(this.lightingStatus, blinkSetting.lightingStatus) &&
        Objects.equals(this.blinkSpeed, blinkSetting.blinkSpeed);
  }

  @Override
  public int hashCode() {
    return Objects.hash(blinkTime, lightingTextColor, lightingBackgroundColor, lightingStatus, blinkSpeed);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BlinkSetting {\n");
    
    sb.append("    blinkTime: ").append(toIndentedString(blinkTime)).append("\n");
    sb.append("    lightingTextColor: ").append(toIndentedString(lightingTextColor)).append("\n");
    sb.append("    lightingBackgroundColor: ").append(toIndentedString(lightingBackgroundColor)).append("\n");
    sb.append("    lightingStatus: ").append(toIndentedString(lightingStatus)).append("\n");
    sb.append("    blinkSpeed: ").append(toIndentedString(blinkSpeed)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
