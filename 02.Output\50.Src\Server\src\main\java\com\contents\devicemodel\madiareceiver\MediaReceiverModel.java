package com.contents.devicemodel.madiareceiver;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

import com.contents.common.CommonUtil;
import com.contents.common.db.Control;
import com.contents.common.external.cross.DeviceLinkItem;
import com.contents.common.external.cross.DisplayGroupInfo;
import com.contents.common.external.cross.DisplayRoutingInfo;
import com.contents.common.external.cross.DisplaySplitInfo;
import com.contents.common.external.cross.MediaReceiverDevice;
import com.contents.common.external.cross.NetworkInfo;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.devicemodel.DeviceModel;
import com.contents.devicemodel.ExecutionPlan;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class MediaReceiverModel extends DeviceModel {

	private final int PING_TIMEOUT = 1; // 1秒
	
	/** メディアレシーバー */
	protected MediaReceiverDevice mediaReceiverDevice;

	/** 端末リンク情報 */
	protected DeviceLinkItem deviceLinkItem;

	/** 表示盤情報 */
	protected DisplayGroupInfo displayGroupInfo;

	/** 表示盤分割面情報 */
	protected DisplaySplitInfo displaySplitInfo;

	/*** 表示盤リンク情報  */
	protected DisplayRoutingInfo displayRoutingInfo;

	/** コネクター番号 */
	protected Integer connectorNumber;

	/** 表示盤種別 */
	protected Integer displayType;

	/** 表示盤番号 */
	protected Integer displayNumber;

	/** 表示盤分割面番号 */
	protected Integer displaySplitNumber;
	
	/** USBスピーカー接続フラグ */
	protected boolean usbSpeakerConnect = false;

	/**
	 * メディアレシーバーデバイスをバインドします。
	 * @param mediaReceiverDevice メディアレシーバーデバイス
	 */
	public void bind(MediaReceiverDevice mediaReceiverDevice) {
		this.mediaReceiverDevice = mediaReceiverDevice;
		this.deviceNumber = mediaReceiverDevice.getDeviceNumber();
		this.productName = mediaReceiverDevice.getProductName();
		this.networkInfo = mediaReceiverDevice.getNetworkInfo();
	}

	public void expand(RoutingInformation linkInformation) {

		for (Iterator<DisplayRoutingInfo> ite = linkInformation.getDisplayRoutingList().iterator(); ite.hasNext();) {

			DisplayRoutingInfo link = ite.next();

			java.util.Optional<DeviceLinkItem> optional = link.getInputList().stream().filter((input) -> Objects.equals(input.getDeviceNumber(), this.deviceNumber)).findFirst();

			if (optional.isEmpty())
				continue;

			this.displayRoutingInfo = link;
			this.deviceLinkItem = optional.get();
			this.connectorNumber = this.deviceLinkItem.getConnectorNumber();

			break;
		}
		
		if (displayRoutingInfo == null)
			return;

		for (Iterator<DisplayGroupInfo> ite = linkInformation.getDisplayGroupManageList().iterator(); ite.hasNext();) {

			DisplayGroupInfo group = ite.next();

			java.util.Optional<DisplaySplitInfo> optional = group.getSplitList().stream().filter((split) -> Objects.equals(split.getDeviceNumber(), displayRoutingInfo.getDeviceNumber())).findFirst();

			if (optional.isEmpty())
				continue;

			this.displayGroupInfo = group;

			this.displayNumber = group.getDisplayNumber();
			this.displayType = group.getDisplayType();

			this.displaySplitInfo = optional.get();
			this.displaySplitNumber = this.displaySplitInfo.getDisplaySplitNumber();

			this.usbSpeakerConnect = Objects.equals(group.getOnUsbSpeakerDeviceNumber(), this.deviceNumber);
			
			break;
		}
	}
	
	@Override
	public List<ExecutionPlan> createExecutionPlan(Control control) {
		// 何もしない
		return null;
	}
	
	public boolean isUsbSpeakerConnect(Integer displayType, Integer displayNumber) {
		if (!Objects.equals(this.displayType, displayType))
			return false;

		if (!Objects.equals(this.displayNumber, displayNumber))
			return false;
		
		return usbSpeakerConnect;
	}

	public boolean match(Integer displayType, Integer displayNumber, Integer displaySplitNumber) {

		if (!Objects.equals(this.displayType, displayType))
			return false;

		if (!Objects.equals(this.displayNumber, displayNumber))
			return false;

		if (!Objects.equals(this.displaySplitNumber, displaySplitNumber))
			return false;

		return true;
	}

	@Override
	protected void healthCheck() {

		try {
			NetworkInfo networkInfo = mediaReceiverDevice.getNetworkInfo();
			
			String ipAddress = networkInfo.getIpAddress();
			Integer port = networkInfo.getPort();

			if (!CommonUtil.checkIpAddressFormat(ipAddress)) {
				updateDeviceConnectStatus(false);
				log.info("Health check Invalid IP address.");
				return;
			}

			Ping ping = new Ping(ipAddress, PING_TIMEOUT);
			if (ping.ping()) {
				
				updateDeviceConnectStatus(true);
			} else {
				log.warn("Health check ping error. ip={}", ipAddress);
				updateDeviceConnectStatus(false);
			}
		} catch (Exception e) {
			log.error("Health checkInstant error.", e);
			updateDeviceConnectStatus(false);
		}

	}

	/**
	 * メディアレシーバーを取得します。
	 * @return メディアレシーバー
	 */
	public MediaReceiverDevice getMediaReceiverDevice() {
		return mediaReceiverDevice;
	}

	/**
	 * 端末リンク情報を取得します。
	 * @return 端末リンク情報
	 */
	public DeviceLinkItem getDeviceLinkItem() {
		return deviceLinkItem;
	}

	/**
	 * 表示盤情報を取得します。
	 * @return 表示盤情報
	 */
	public DisplayGroupInfo getDisplayGroupInfo() {
		return displayGroupInfo;
	}

	/**
	 * 表示盤分割面情報を取得します。
	 * @return 表示盤分割面情報
	 */
	public DisplaySplitInfo getDisplaySplitInfo() {
		return displaySplitInfo;
	}

	/**
	 * * 表示盤リンク情報を取得します。
	 * @return * 表示盤リンク情報
	 */
	public DisplayRoutingInfo getDisplayRoutingInfo() {
		return displayRoutingInfo;
	}

	/**
	 * コネクター番号を取得します。
	 * @return コネクター番号
	 */
	public Integer getConnectorNumber() {
		return connectorNumber;
	}

	/**
	 * 表示盤種別を取得します。
	 * @return 表示盤種別
	 */
	public Integer getDisplayType() {
		return displayType;
	}

	/**
	 * 表示盤番号を取得します。
	 * @return 表示盤番号
	 */
	public Integer getDisplayNumber() {
		return displayNumber;
	}

	/**
	 * 表示盤分割面番号を取得します。
	 * @return 表示盤分割面番号
	 */
	public Integer getDisplaySplitNumber() {
		return displaySplitNumber;
	}


}
