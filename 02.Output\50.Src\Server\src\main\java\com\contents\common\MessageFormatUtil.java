package com.contents.common;

import org.slf4j.helpers.MessageFormatter;

public class MessageFormatUtil {

	
	public static String format(String format, Object arg) {
		return MessageFormatter.format(format, arg).getMessage();
	}
	
	public static String format(String format, Object arg1, Object arg2) {
		return MessageFormatter.format(format, arg1, arg2).getMessage();
	}
	
	public static String format(String format, Object... arguments) {
		return MessageFormatter.arrayFormat(format, arguments).getMessage();
	}
}
