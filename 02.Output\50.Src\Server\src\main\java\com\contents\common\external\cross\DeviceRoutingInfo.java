package com.contents.common.external.cross;

import java.util.List;

public abstract class DeviceRoutingInfo {

	/** デバイス番号 */
	protected Integer deviceNumber;
	
	/** 入力情報 */
	protected List<DeviceLinkItem> inputList;
	
	/** 出力情報 */
	protected List<DeviceLinkItem> outputList;

	/**
	 * デバイス番号を取得します。
	 * @return デバイス番号
	 */
	public Integer getDeviceNumber() {
	    return deviceNumber;
	}

	/**
	 * デバイス番号を設定します。
	 * @param deviceNumber デバイス番号
	 */
	public void setDeviceNumber(Integer deviceNumber) {
	    this.deviceNumber = deviceNumber;
	}

	/**
	 * 入力情報を取得します。
	 * @return 入力情報
	 */
	public List<DeviceLinkItem> getInputList() {
	    return inputList;
	}

	/**
	 * 入力情報を設定します。
	 * @param inputList 入力情報
	 */
	public void setInputList(List<DeviceLinkItem> inputList) {
	    this.inputList = inputList;
	}

	/**
	 * 出力情報を取得します。
	 * @return 出力情報
	 */
	public List<DeviceLinkItem> getOutputList() {
	    return outputList;
	}

	/**
	 * 出力情報を設定します。
	 * @param outputList 出力情報
	 */
	public void setOutputList(List<DeviceLinkItem> outputList) {
	    this.outputList = outputList;
	}
	
	
}
