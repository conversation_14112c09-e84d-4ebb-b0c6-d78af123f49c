package com.contents.common.external.cross;

public class NetworkInfo {

	/** IPアドレス */
	private String ipAddress;
	
	/** ポート番号 */
	private Integer port;

	/**
	 * IPアドレスを取得します。
	 * @return IPアドレス
	 */
	public String getIpAddress() {
	    return ipAddress;
	}

	/**
	 * IPアドレスを設定します。
	 * @param ipAddress IPアドレス
	 */
	public void setIpAddress(String ipAddress) {
	    this.ipAddress = ipAddress;
	}

	/**
	 * ポート番号を取得します。
	 * @return ポート番号
	 */
	public Integer getPort() {
	    return port;
	}

	/**
	 * ポート番号を設定します。
	 * @param port ポート番号
	 */
	public void setPort(Integer port) {
	    this.port = port;
	}
	
}
