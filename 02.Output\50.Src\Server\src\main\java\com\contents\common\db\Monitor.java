package com.contents.common.db;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 表示盤セットアップテーブル
 */
@Entity
@Data
@IdClass(MonitorPK.class)
@NoArgsConstructor
public class Monitor implements Serializable {
    /**
     * 表示盤番号
     */
    @Id
    @Column(nullable = false)
    private Integer display_no;

    /**
     * 面番号
     */
    @Id
    @Column(nullable = false)
    private Integer display_split_no;

    /**
     * 0: disconnected 未接続(default) 1：Connected 接続済み 2：接続Timeout
     */
    private Integer status;
    
    private Date receive_time;
    
    private Date update_time;
}
