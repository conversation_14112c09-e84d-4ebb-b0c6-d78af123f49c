import React from 'react';
import Cell from './elements/Cell';
import ArrayScroll from './elements/ArrayScroll';
import { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';

/**
 * 簡易事案コンテンツ(1-2サイズ)<br>
 * propsは、「3.6簡易事案コンテンツ(1-2サイズ)情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module CaseHalf
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const CaseHalf = (props) => {
  const MAX_ROW = 2;
  return (
    <>
      {isValidSource(props) &&
        props.case.map((item, index) => {
          //・1コンテンツあたり最大2件の情報を表示可能。
          if (index >= MAX_ROW) return undefined;

          return <CaseHalfRow key={index} item={item} index={index} />;
        })}
    </>
  );
};

/**
 * 簡易事案コンテンツ(1-2サイズ)の一事案
 * auto-cols-fr で列を同幅にする
 */
const CaseHalfRow = (props) => {
  let row1Cell1Props, row1Cell2Props, row1Cell3Props, row1Props;
  let row2Cell1Props, row2Cell2Props;
  const Max_Cars = 8;

  const data = props?.item;

  let obj = data?.disaster_class;
  row1Props = { backgroundColor: getHexColor(obj?.background_color) };
  row1Cell1Props = getCellFace(obj?.disaster_type, 'col-span-4');
  row1Cell2Props = getCellFace(obj?.case_no, 'col-span-7 col-start-6');
  row1Cell3Props = getCellFace(
    obj?.fire_station_name,
    'col-span-4 col-start-14'
  );

  row2Cell1Props = getCellFace(data?.awareness_time, 'col-span-8 col-start-2 w-fit');
  row2Cell2Props = getCellFace(
    data?.town_name,
    'col-span-10 col-start-10 w-fit'
  );

  return (
    <div className="leading-[1]">
      <div
        style={row1Props}
        className="border-transparent border-x-[1rem] grid grid-cols-19 auto-cols-fr text-6xl"
      >
        <Cell {...row1Cell1Props} />
        <Cell {...row1Cell2Props} />
        <Cell {...row1Cell3Props} />
      </div>
      <div className="grid grid-cols-[minmax(6rem,2fr)_repeat(10,5.85rem)_repeat(10,5.85rem)_minmax(6rem,2fr)] auto-cols-fr my-[3.1rem] text-5xl">
        <Cell {...row2Cell1Props} />
        <Cell {...row2Cell2Props} />
      </div>

      <ArrayScroll
        max={Max_Cars}
        gridLevelProps={
          'grid-cols-[minmax(0,1fr)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_repeat(4,5.85rem)_minmax(0,1fr)] auto-cols-fr mb-[3rem] gap-y-[1.2rem] gap-x-[1.2rem] text-6xl'
        }
        cellLevelProps={[
          'col-span-4 col-start-2',
          'col-span-4',
          'col-span-4',
          'col-span-4',
        ]}
        display_data={props?.item?.car_name?.display_data}
        change_setting={props?.item?.car_name?.change_setting}
      />
    </div>
  );
};

export default CaseHalf;
