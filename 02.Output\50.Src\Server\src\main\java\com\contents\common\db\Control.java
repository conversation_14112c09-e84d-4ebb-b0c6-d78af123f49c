package com.contents.common.db;

import lombok.Data;
import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 表示盤制御テーブル
 */
@Entity
@Data
@IdClass(ControlPK.class)
public class Control implements Serializable {
    private Integer display_type;

    /**
     * Primary Key 1
     */
    @Id
    @Column(nullable = false)
    private Integer display_no;

    private String display_name;

    /**
     * Primary Key 2
     */
    @Id
    @Column(nullable = false)
    private Integer display_split_no;

    /**
     * Primary Key 3
     */
    @Id
    @Column(nullable = false)
    private Integer display_details_split_no;

    private Integer input_switch;

    @Column(nullable = false)
    private Integer source_no;

    private String source_name;

    @Column(nullable = false)
    private Integer source_split_no;

    private Integer source_disp_pat;

    private Integer source_vol_control_type;

    private Integer source_vol_mute_control;

    // 音量制御有無
    private Integer is_vol_control;

    /**
     * -1 廃棄
     * 0 指令系APから来たばかり
     * 1 Browserに通知済み
     * 2 Browserに制御済み
     */
    private Integer status;
    
    private Date receive_time;
    
    private Date update_time;
}
