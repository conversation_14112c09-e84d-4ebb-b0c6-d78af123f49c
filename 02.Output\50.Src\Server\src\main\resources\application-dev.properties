#############環境に合わせて設定を変更する必要##############
#PostgreのDB設定。DBサーバの設定 必ず環境に合わせて設定する
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=admin

#DBのAPI履歴の最大保存期間(月単位)。これ以上を超えると、自動的に削除される
api.history.max.keep.month=2

# APIのタイムアウト時間(ms単位)。10000の場合、10秒
api.timeout=10000

#############あまり変更する必要がないデフォルト設定##############

#############開発環境専用設定、変更する必要がない##############
# SpringBootの内蔵Tomcatを使う時のみ効く。外部Tomcatを利用する時に、外部TomcatのContentPathとPortを従う
server.servlet.contextPath=/contentserver
server.port=8080

# 全体のLog
#logging.file.name=/var/log/ContentServer.log
#logging.level.root=INFO
#logging.level.web=DEBUG
#logging.level.org.springframework.web=DEBUG
#spring.mvc.log-request-details=true
#server.tomcat.accesslog.enabled=true

# SQLのLog設定
#spring.jpa.show-sql=true
#logging.level.org.hibernate.SQL=debug

#SpringBootの内蔵Tomcat利用で、自作のSSLのKey情報。外部Tomcatを利用する時、外部Tomcatの設定に従う
#server.ssl.key-store=classpath:keystore.p12
#server.ssl.key-store-password=welcome01!
#server.ssl.keyAlias=contentsap
#server.ssl.keyStoreType=PKCS12

# 外部構成のJSONファイル名 (resourceフォルダ内のファイル)
app.configuration.external.filename=external_configuration.json


# 機器制御有効化設定。 true: 機器制御有効, false: 機器制御無効(デバッグ用)
app.configuration.external.machine-control-enable=false