package com.contents.devicemodel.monitor;

import com.contents.common.annotation.ExternalDeviceProduct;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@ExternalDeviceProduct(names = { "DUMMY-MONITOR" })
public class DummyMonitor extends MonitorDeviceModel {
	
	@Override
	public boolean selectInput(Integer connectorNumber, Integer inputSwitch) {
		
		return true;
	}

	@Override
	public boolean setMultiMonitor(Integer sourceDispPattern) {
		
		return true;
	}

	@Override
	protected void healthCheck() {
		
		log.info("Health check: true");
		updateDeviceConnectStatus(true);
	}
	

}
