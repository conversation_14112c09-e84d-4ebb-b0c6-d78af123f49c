package com.contents.manager;

import java.io.File;
import java.io.IOException;

import org.springframework.core.io.ClassPathResource;

import com.contents.configuration.SystemSetting;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SystemSettingManager {

	private static SystemSetting systemSetting = null;
	
	public static SystemSetting getSystemSetting() {
		
		if (systemSetting == null) {
			
			String tomcatBaseDir = System.getProperty("catalina.base");

			String tomcatConfDir = tomcatBaseDir + "/conf";

			try {
				final String targetFile = "system_setting.json";
				String deviceConfiguration = tomcatConfDir + "/" + targetFile;
				File file = new File(deviceConfiguration);
				
				ObjectMapper mapper = new ObjectMapper();
				SystemSetting obj = null;
				
				if (file.exists()) {
					obj = mapper.readValue(file, SystemSetting.class);
				} else {
					file = new ClassPathResource(targetFile).getFile();
					obj = mapper.readValue(file, SystemSetting.class);
				}
				
				if (obj != null) {
					systemSetting = obj;
					log.info("load object complate system_setting.json");
				} else {
					log.warn("load object error system_setting.json");
				}
				
				
				
			} catch (IOException e) {
				log.warn("read file error system_setting.json", e);
			}
		}
		
		return systemSetting;
	}
}
