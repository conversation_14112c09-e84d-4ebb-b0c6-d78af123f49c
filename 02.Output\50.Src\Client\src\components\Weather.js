import React, { Component } from 'react';
import Cell from './elements/Cell.js';
import { getCellFace, isValidSource, toThousands } from '../utils/Util.js';
import Title from './elements/Title.js';
import CellBox from './elements/CellBox.js';
import './Weather.css';

/**
 * 気象状況コンテンツ<br>
 * propsは、「3.10気象コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module Weather
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Weather = (props) => {
  const barTitle = props.barTitle ?? '気象状況';
  console.log(`props.barTitle: ${barTitle}`);
  return (
    <>
      <Title title={barTitle}/>
      {isValidSource(props) && (
        <div className="flex flex-row">
          <div className="basis-[54.25%] grid grid-cols-1">
			<div className="p-[3.6rem] mt-[.7rem]">
			  <WindDirection {...props.wind_direction} />
			</div>
		  </div>
          <div className="basis-[45.75%] ml-[2rem]">
            <div className="grid grid-cols-[repeat(4,5.1rem)_3.1rem_repeat(3,5.5rem)_3.1rem_repeat(2,5rem)] grid-rows-8 gap-y-[2.3rem] mt-[3.8rem] mb-[3rem] text-5xl leading-[1] mr-[3rem]">
              <WeatherRow weather="最大風速" detail={props.wind_speed_max} />
              <WeatherRow weather="平均風速" detail={props.wind_speed} />
              <WeatherRow weather="気温" detail={props.temperature} />
              <WeatherRow weather="雨量" detail={props.rainfall} />
              <WeatherRow
                weather="実効湿度"
                detail={props.effective_humidity}
              />
              <WeatherRow weather="相対湿度" detail={props.relative_humidity} />
              <WeatherRow weather="気圧" detail={props.atmospheric_pressure} />
              {/* <WeatherRow weather='観測時刻' detail={props.observation_time} /> */}
              <WeatherRow weather="天候" detail={props.weather} />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

// 気象状況コンテンツの風向
const WindDirection = (props) => {
  const imageMap = {
    北: 'North.png',
    北_北東: 'North_Northeast.png',
    北東: 'Northeast.png',
    北東_東: 'Northeast_East.png',
    東: 'East.png',
    東_南東: 'East_Southeast.png',
    南東: 'Southeast.png',
    南東_南: 'Southeast_South.png',
    南: 'South.png',
    南_南西: 'South_Southwest.png',
    南西: 'Southwest.png',
    南西_西: 'Southwest_West.png',
    西: 'West.png',
    西_北西: 'West_Northwest.png',
    北西: 'Northwest.png',
    北西_北: 'Northwest_North.png',
  };

  if (!props || !props.display_text) {
    return null;
  }

  // 風向の表示色なし(画像のまま)。背景色は黒のまま
  let imgPath = `/images/${imageMap[props.display_text.trim()]}`;

  return (
    <img className="min-w-full"
      src={process.env.PUBLIC_URL + imgPath}
      alt={props.display_text}
    />
  );
};

// 気象状況コンテンツの一行天気
const WeatherRow = (props) => {
  const unitMap = {
    平均風速: 'm/s',
    風向: '',
    最大風速: 'm/s',
    気温: '&#8451;',
    雨量: 'mm',
    実効湿度: '%',
    相対湿度: '%',
    気圧: 'hPa',
    観測時刻: '',
    天候: '',
  };
  const cell1Props = {
    text_color: '#080808',
    background_color: '#fff',
    text: props.weather,
    className: 'col-start-1 col-span-4 justify',
  };
  const cell2Props = getCellFace(
    props.detail,
    'col-span-3 col-start-6 place-self-end w-fit'
  );

  const cell3Props = {
    text_color: '#fff',
    background_color: '#000',
    className: 'col-start-10 col-span-2',
  };

  return (
    <>
      <Cell {...cell1Props} />
      <Cell {...cell2Props} />
      {props.detail?.display_text && (
        <CellBox {...cell3Props}>
          <span dangerouslySetInnerHTML={{ __html: unitMap[props.weather] }} />
        </CellBox>
      )}
    </>
  );
};

export default Weather;
