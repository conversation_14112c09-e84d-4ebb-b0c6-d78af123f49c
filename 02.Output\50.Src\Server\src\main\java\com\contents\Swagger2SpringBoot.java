package com.contents;

import java.io.Serial;

import org.springframework.boot.ExitCodeGenerator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.contents.configuration.LocalDateConverter;
import com.contents.configuration.LocalDateTimeConverter;

import lombok.extern.slf4j.Slf4j;
import springfox.documentation.oas.annotations.EnableOpenApi;

@Slf4j
@SpringBootApplication
@EnableOpenApi
@EnableAsync
@EnableTransactionManagement
@ComponentScan(basePackages = { "com.contents", "com.contents.api", "com.contents.configuration" })
public class Swagger2SpringBoot extends SpringBootServletInitializer {

	public static void main(String[] args) {

		//    	SLF4JBridgeHandler.removeHandlersForRootLogger();
		//    	SLF4JBridgeHandler.install();

		new SpringApplication(Swagger2SpringBoot.class).run(args);
		
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(Swagger2SpringBoot.class);
	}

	@Configuration
	static class CustomDateConfig implements WebMvcConfigurer {
		@Override
		public void addFormatters(FormatterRegistry registry) {
			registry.addConverter(new LocalDateConverter("yyyy-MM-dd"));
			registry.addConverter(new LocalDateTimeConverter("yyyy-MM-dd'T'HH:mm:ss.SSS"));
		}
	}

	static class ExitException extends RuntimeException implements ExitCodeGenerator {
		@Serial
		private static final long serialVersionUID = 1L;

		@Override
		public int getExitCode() {
			return 10;
		}

	}
}
