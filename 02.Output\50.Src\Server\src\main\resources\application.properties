# 開発環境の場合、この行のコメント外す
spring.profiles.active=dev
#生産環境の場合、この行のコメントを外す
#spring.profiles.active=prod

info.version=@project.version@

#############あまり変更する必要がないデフォルト設定##############
#DB Pool設定
spring.datasource.hikari.minimum-idle=10
# PostgreSQLのpostgresql.confで設定するmax_connectionsと合わせる。
# デフォルトでは、max_connections=200
spring.datasource.hikari.maximum-pool-size=200

#spring.main.web-application-type=none

#TOMCAT設定
server.tomcat.min-spare-threads=50


#SwaggerAPI関連
springfox.documentation.open-api.v3.path=/api-docs
spring.jackson.date-format=com.contents.api.RFC3339DateFormat
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.main.allow-bean-definition-overriding=true

#PostgreのDB設定
spring.datasource.driver-class-name=org.postgresql.Driver
#SpringBootのJPA設定
spring.jpa.database=postgresql
spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jpa.hibernate.naming.implicit-strategy=org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
spring.jpa.open-in-view=false



# DBのSchema自動制御。最初のDBSchemaは、JPAによる自動的に作成。
# 将来、DB変更された時に、このFlagをTrueにして、自動的にSchemaのバージョン管理する予定
spring.flyway.enabled=false
spring.flyway.baseline-on-migrate=true
spring.flyway.clean-disabled=true
spring.flyway.baseline-version=1

# JpaRepositoryをRestで公開する関連設定
spring.data.rest.basePath=/inner
spring.data.rest.defaultPageSize=100
spring.data.rest.pageParamName=page
spring.data.rest.sortParamName=sort
spring.data.rest.limitParamName=size

# Config for Actuator
management.endpoints.web.exposure.include=*
management.endpoints.web.discovery.enabled=true
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always

spring.jmx.enabled=false


spring.messages.basename=messages.properties






