package com.contents.devicemodel.monitor;

import java.util.ArrayList;
import java.util.List;

import com.contents.common.annotation.ExternalDeviceProduct;

import lombok.extern.slf4j.Slf4j;

/**
 * モニターデバイスモデルクラス
 * <p>
 * 対応機種: PN-V553
 * <p>
 * ※「PN-V553」は「LCD-UN462VA」とコマンドインターフェースが一致していため、継承して入力インターフェースの違いだけを差分実装する。
 */
@Slf4j
@ExternalDeviceProduct(names = { "PN-V553" })
public class PnV553 extends LcdUn462Va {

	protected List<ConnectorInfo> createConnectorList() {

		List<ConnectorInfo> list = new ArrayList<ConnectorInfo>();
		list.add(new ConnectorInfo(1, "VGA", 1, true));
		list.add(new ConnectorInfo(2, "DVI", 3, true));
		list.add(new ConnectorInfo(3, "VIDEO", 5, false));
		list.add(new ConnectorInfo(4, "YGA", 12, false));
		list.add(new ConnectorInfo(5, "OPTION", 13, false));
		list.add(new ConnectorInfo(6, "DISPLAY_PORT1", 15, true));
		list.add(new ConnectorInfo(7, "DISPLAY_PORT2", 16, false));
		list.add(new ConnectorInfo(8, "HDMI1", 17, true));
		list.add(new ConnectorInfo(9, "HDMI2", 18, true));
		list.add(new ConnectorInfo(10, "HDMI3", 130, false));
		list.add(new ConnectorInfo(11, "MP", 135, false));
		return list;
	}

	@Override
	public synchronized boolean setMultiMonitor(Integer sourceDispPattern) {
		
		boolean ret = super.setMultiMonitor(sourceDispPattern);
		return ret;
	}

	@Override
	public synchronized boolean selectInput(Integer connectorNumber, Integer inputSwitch) {
		
		boolean ret = super.selectInput(connectorNumber, inputSwitch);
		return ret;
	}

}
