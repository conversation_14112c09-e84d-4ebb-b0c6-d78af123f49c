package com.contents.common.db;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;

/**
 * 表示盤制御テーブルのDAO
 */
@Repository
@RepositoryRestResource(path = "control")
public interface ControlDao extends JpaRepository<Control, ControlPK>, Serializable {
    @Query(value = "SELECT t FROM Control t where t.source_no = :sourceNo and t.source_split_no = :sourceSplitNo")
    List<Control> findBySourceNoAndSourceSplitNo(@Param("sourceNo") Integer sourceNo, @Param("sourceSplitNo") Integer sourceSplitNo);

    @Query(value = "SELECT t FROM Control t where t.source_no = :sourceNo")
    List<Control> findBySourceNo(@Param("sourceNo") Integer sourceNo);

    @Query(value = "SELECT t FROM Control t where t.display_no = :displayNo and t.display_split_no = :splitNo")
    List<Control> findByDisplayNoAndDisplaySplitNo(@Param("displayNo") Integer displayNo, @Param("splitNo") Integer splitNo);

    @Transactional
    @Modifying
    @Query(value = "delete FROM Control t where t.display_no = :displayNo and t.display_split_no = :splitNo")
    Integer deleteByDisplayNoAndDisplaySplitNo(@Param("displayNo") Integer displayNo, @Param("splitNo") Integer splitNo);

    @Query(value = "SELECT max(t.source_disp_pat) FROM Control t WHERE t.display_no = :displayNo and t.display_split_no = :splitNo")
    Integer selectMaxSourceDispPat(@Param("displayNo") Integer displayNo, @Param("splitNo") Integer splitNo);

    /**
     * 最新の受信時間で、指定したCount行を返す
     *
     * @param count　返す必要な行数
     * @return 行のリスト
     */
    @Query(value = "select * from Control t order by receive_time desc limit :count", nativeQuery = true)
    List<Control> findRowByLastReceiveTime(@Param("count") Integer count);
}
