package com.contents.pojo;

import java.io.IOException;
import java.net.InetAddress;

import lombok.extern.slf4j.Slf4j;

/**
 * ping制御
 */
@Slf4j
public class Ping {
	private final String ip;
	private final Integer toutSec;
	private static final boolean IS_WINDOWS = System.getProperty("os.name").toLowerCase().startsWith("win");

	/**
	 * Ping実行
	 * @param ip ターゲット IPアドレス
	 * @param toutSec 応答タイムアウト(秒)
	 */
	public Ping(String ip, Integer toutSec) {
		this.ip = ip;
		this.toutSec = toutSec;
	}

	/**
	 * Ping を実行し、ホストとの疎通を確認します。
	 * @return Ping送信結果 true:疎通確認OK, false:疎通確認NG
	 */
	public boolean ping() {
		
		try {
			
			InetAddress inetAddress = InetAddress.getByName(ip);
			String[] command;

			if (IS_WINDOWS) {
				// Windows の場合
				int timeoutMSec = (toutSec * 1000);
				String strTimeoutMSec = Integer.toString(timeoutMSec);
				command = new String[]{"ping", "-n", "1", "-w", strTimeoutMSec, inetAddress.getHostAddress()};
			} else {
				// Linux の場合
				String strTimeout = toutSec.toString();
				command = new String[]{"ping", "-c", "1", "-W", strTimeout, inetAddress.getHostAddress()};
			}

			ProcessBuilder pingProcess = new ProcessBuilder(command);
			Process process =  pingProcess.start();
			int ret = process.waitFor();
			log.info("ping({}) ret = {}", ip, ret);
			return ret == 0;
		} catch (InterruptedException ire) {
			log.error("InterruptedException: " + ire.getMessage());
			return false;
		} catch (IOException ioe) {
			log.error("IOException: " + ioe.getMessage());
			return false;
		} catch (Exception err) {
			log.error("Exception: " + err.getMessage());
			return false;
		}
	}

}

