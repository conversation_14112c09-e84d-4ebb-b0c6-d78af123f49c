package com.contents.model;

import java.util.Objects;
import com.contents.model.TotalFrequencyContentNumberList;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * TotalFrequencyContentAggInfo
 */
@Validated


public class TotalFrequencyContentAggInfo   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("number_list")
  @Valid
  private List<TotalFrequencyContentNumberList> numberList = null;

  public TotalFrequencyContentAggInfo displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 集計対象
   * @return displayText
   **/
  @Schema(description = "集計対象")
  
  @Size(max=5)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public TotalFrequencyContentAggInfo textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public TotalFrequencyContentAggInfo backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public TotalFrequencyContentAggInfo numberList(List<TotalFrequencyContentNumberList> numberList) {
    this.numberList = numberList;
    return this;
  }

  public TotalFrequencyContentAggInfo addNumberListItem(TotalFrequencyContentNumberList numberListItem) {
    if (this.numberList == null) {
      this.numberList = new ArrayList<TotalFrequencyContentNumberList>();
    }
    this.numberList.add(numberListItem);
    return this;
  }

  /**
   * Get numberList
   * @return numberList
   **/
  @Schema(description = "")
      @Valid
  @Size(max=3)   public List<TotalFrequencyContentNumberList> getNumberList() {
    return numberList;
  }

  public void setNumberList(List<TotalFrequencyContentNumberList> numberList) {
    this.numberList = numberList;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TotalFrequencyContentAggInfo totalFrequencyContentAggInfo = (TotalFrequencyContentAggInfo) o;
    return Objects.equals(this.displayText, totalFrequencyContentAggInfo.displayText) &&
        Objects.equals(this.textColor, totalFrequencyContentAggInfo.textColor) &&
        Objects.equals(this.backgroundColor, totalFrequencyContentAggInfo.backgroundColor) &&
        Objects.equals(this.numberList, totalFrequencyContentAggInfo.numberList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor, backgroundColor, numberList);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TotalFrequencyContentAggInfo {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    numberList: ").append(toIndentedString(numberList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
