package com.contents.common;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.InetAddressValidator;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * System全体の定数、及ぶ他のUtility関数
 */
@Slf4j
public final class CommonUtil {
	private CommonUtil() {
	}

	public static final String WS_CMD_SET_CONTENT = "/setContent";
	public static final String WS_CMD_SET_CONTROL = "/setControl";
	public static final String WS_CMD_SET_SOUND = "/setSound";
	public static final int MONITOR_STATUS_OFFLINE = 0;
	public static final int MONITOR_STATUS_CONNECTED = 1;

	/**
	 * -1 廃棄
	 * 0 指令系APから来たばかり
	 * 1 Browserに通知済み
	 * 2 Browserに制御/表示済み
	 */
	public static final int STATUS_DROP = -1;
	public static final int STATUS_RECEIVED = 0;
	public static final int STATUS_SEND2BROWSER = 1;
	public static final int STATUS_BROWSER_DONE = 2;

	public static final int API_RESULT_SUCCESS = 0;
	public static final int API_RESULT_FAIL = -1;

	/**
	 * WebSocketのEndPointを作成
	 *
	 * @param displayNo 表示盤ID
	 * @param splitNo   画面ID
	 * @return EndPoint名
	 */
	public static String getWebSocketId(Integer displayNo, Integer splitNo) {
		//WebSocketの受け側のID
		return MessageFormatUtil.format("{}_{}", displayNo, splitNo);
	}

	/**
	 * WebSocketのEndPointを作成
	 *
	 * @param displayNo 表示盤ID
	 * @param splitNo   画面ID
	 * @param detailSplitNo 分割面ID
	 * @return EndPoint名
	 */
	public static String getWebSocketId(Integer displayNo, Integer splitNo, Integer detailSplitNo) {
		//WebSocketの受け側のID
		return MessageFormatUtil.format("{}_{}_{}", displayNo, splitNo, mapDetailSplitNo(detailSplitNo));
	}

	/**
	 * 各機能で、一面の表示盤番号前提なので、4面表示盤での分割番号と一面の表示盤番号のマッピング
	 * @param detailSplitNo APIでの表示盤番号
	 * @return 0/1/2/3限定の表示盤番号
	 */
	public static Integer mapDetailSplitNo(Integer detailSplitNo) {
		// <4面表示盤での分割番号, 一面の表示盤番号>
		final Map<Integer, Integer> multiScreenDetailSplitNoMap = new HashMap<Integer, Integer>(16) {
			{
				put(0, 0);
				put(2, 0);
				put(8, 0);
				put(10, 0);

				put(1, 1);
				put(3, 1);
				put(9, 1);
				put(11, 1);

				put(4, 2);
				put(6, 2);
				put(12, 2);
				put(14, 2);

				put(5, 3);
				put(7, 3);
				put(13, 3);
				put(15, 3);
			}
		};

		return multiScreenDetailSplitNoMap.get(detailSplitNo);
	}

	/**
	 * WebSocketのEndPointを作成
	 *
	 * @param monitorId モニターID
	 * @return EndPoint
	 */
	public static String getWebSocketId(Integer monitorId) {
		//WebSocketの受け側のID
		return MessageFormatUtil.format("{}", monitorId);
	}

	/**
	 * APIを呼び出すClient側の情報
	 *
	 * @param req Http Request
	 * @return Client情報
	 */
	public static String getClientInfo(HttpServletRequest req) {
		StringBuilder sb = new StringBuilder();
		String remoteAddr = req.getRemoteHost();

		/* This means you just access the web application in localhost. */
		if ("0:0:0:0:0:0:0:1".equalsIgnoreCase(remoteAddr)) {
			/* Get localhost inetAddress object */
			InetAddress inetAddressObj;
			try {
				inetAddressObj = InetAddress.getLocalHost();
				/* Get localhost real ip*/
				remoteAddr = inetAddressObj.getHostAddress();
			} catch (UnknownHostException ignored) {
			}
		} else {
			/* This means the client request to you through a proxy server. */
			if (StringUtils.isEmpty(remoteAddr)) {
				remoteAddr = req.getHeader("X-Forwarded-For");
			}
		}
		if (StringUtils.isEmpty(remoteAddr)) {
			remoteAddr = req.getRemoteAddr();
		}

		sb.append(remoteAddr);
		if (req.getRemotePort() >= 0) {
			sb.append(MessageFormatUtil.format(":{}", req.getRemotePort()));
		}
		return sb.toString();
	}

	/**
	 * APIを呼び出すServer側の情報
	 *
	 * @param request Http Request
	 * @return Server情報
	 */
	public static String getServerInfo(HttpServletRequest request) {
		StringBuilder sb = new StringBuilder();
		sb.append(MessageFormatUtil.format("Host: {}. ", request.getRequestURL()));

		return sb.toString();
	}


	public static void sleep(long millis) {
		try {
			Thread.sleep(millis);
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
	}

	private static final InetAddressValidator tnetAddressValidator = InetAddressValidator.getInstance();;

	public static boolean checkIpAddressFormat(String ipAddress) {
		
		boolean ret = false;
		try {
			ret |= tnetAddressValidator.isValidInet4Address(ipAddress);
		} catch (Exception e) {
			ret |= false;
		}
		try {
			ret |= tnetAddressValidator.isValidInet6Address(ipAddress);
		} catch (Exception e) {
			ret |= false;
		}
		return ret;
	}

	private static final ObjectMapper objectMapper = new ObjectMapper();
	

	
	public static String loggingJsonConvert(final Object obj) {
		try {
            String formattedJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
			return formattedJson;
		} catch (Exception e) {
			log.error("json formatting error.", e);
		}
		return "";
	}

	public static String loggingJsonConvert(final String body) {

		try {
			Object json = objectMapper.readValue(body, Object.class);
            String formattedJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(json);
			return formattedJson;
		} catch (Exception e) {
			log.error("json formatting error.", e);
		}
		return "";
	}

	public static <TResult, TEntity> TResult convertClassObject(TEntity entity, Class<TResult> cls) {
		
		try {
			
			String jsonString = objectMapper.writeValueAsString(entity);
			
			TResult result = objectMapper.readValue(jsonString, cls);
			
			return result;
			
		} catch (Exception e) {
			
			log.error("json formatting error.", e);
		}
		
		return null;
	}
	
	public static ExecutorService newFixedThreadPool() {
		
		int coreCount = Runtime.getRuntime().availableProcessors();
		int nThreads = coreCount * 2;
		
		log.info("ThreadPool nThreads: {}", nThreads);
		
		return Executors.newFixedThreadPool(nThreads);
	}
	}
