package com.contents.external.monitor.pn_hs;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import javax.xml.bind.DatatypeConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.contents.common.external.initial.Display;
import com.contents.common.external.initial.DisplaySplit;
import com.contents.configuration.ExternalConfiguration;
import com.contents.configuration.ExternalConfiguration.HdmiInput;
import com.contents.manager.SocketClient;
import com.contents.manager.SocketManager;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;

/**
 * 単面マルチモニタ制御<br>
 * PN-HSシリーズ
 */
@Slf4j
@Component
public class SingleMonitorControl {
	private static final int PING_TIMEOUT = 1;
	private static final int CONNECT_TIMEOUT = 20000;
	private static final int RECEIVE_TIMEOUT = 10000; // 10秒

	private static final String MSG_LOGIN = "Login:";
	private static final String MSG_PASSWORD = "Password:";
	private static final String MSG_NEW_PASSWORD = "New password:";
	private static final String MSG_RETYPE_NEW_PASSWORD = "Retype new password:";
	private static final String MSG_OK = "OK";
	private static final String MSG_GOODBYE = "goodbye";
	private static final String MSG_NEWLINE = "\r\n";
	private static final int RES_INPUT_MAX_LEN = (2 + MSG_NEWLINE.length());
	private static final int RES_TEMP_MAX_LEN = (3 + MSG_NEWLINE.length());

	private static final String CMD_USER = "\r\n";
	private static final String CMD_PASS = "\r\n";
	private static final String CMD_BYE = "BYE\r\n";
	private static final String CMD_NEWLINE = "\r\n";
	private static final String CMD_INPUT = "INPS";
	private static final String INPUT_PARM_TOGGLE = "   0";
	private static final String INPUT_PARM_DSUB = "   2";
	private static final String INPUT_PARM_HDMI1 = "  10";
	private static final String INPUT_PARM_USB = "  11";
	private static final String INPUT_PARM_HDMI2 = "  13";
	private static final String INPUT_PARM_HDMI3 = "  18";
	private static final String CMD_ENLG = "ENLG";
	private static final String ENLG_OFF = "   0";
	private static final String ENLG_ON = "   1";
	private static final String CMD_TEMP = "ERRT";
	private static final String PARM_GET = "   ?";
	private static final String GET_TEMP = CMD_TEMP + PARM_GET + CMD_NEWLINE;
	private static final String GET_INPUT = CMD_INPUT + PARM_GET + CMD_NEWLINE;

	private enum Input {
		TOGGLE(0), DSUB(2), HDMI1(10), USB(11), HDMI2(13), HDMI3(18);

		private final int id;

		Input(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	private final ExternalConfiguration extConf;
	//private final SocketManager socketMgr;

	/** 接続状態MAP true:接続, false:未接続　(key:displayNo,displaySplitNo Value:isConnect) */
	private final Map<Integer, Map<Integer, Boolean>> connectStatusMap = new HashMap<>();

	@Autowired
	public SingleMonitorControl(
			ExternalConfiguration extConf) {
		this.extConf = extConf;
		//healthCheck();
	}

	//    @Autowired
	//    public SingleMonitorControl(
	//            ExternalConfiguration extConf,
	//            SocketManager socketMgr) {
	//        this.extConf = extConf;
	//        this.socketMgr = socketMgr;
	//        healthCheck();
	//    }

	/**
	 * 接続状態
	 * @return 接続状態 true:全接続OK, false:いずれかの接続がNG or 接続先が無い
	 */
	public boolean isConnect() {
		boolean retIsConnect = true;
		for (Map.Entry<Integer, Map<Integer, Boolean>> entry : this.connectStatusMap.entrySet()) {
			if (retIsConnect) {
				for (Map.Entry<Integer, Boolean> connect : entry.getValue().entrySet()) {
					// 未接続状態があればfalseにしてループを抜ける
					if (!connect.getValue()) {
						retIsConnect = false;
						break;
					}
				}
			} else {
				// ひとつでも未接続状態があればループを抜ける
				break;
			}
		}

		// 接続先がない場合もfalseを返す
		return (!this.connectStatusMap.isEmpty()) && retIsConnect;
	}

	/**
	 * 定期監視
	 */
	public void healthCheck() {
		log.info("Health check start.");

		// 表示盤情報
		List<Display> displayList = extConf.getAllDisplay(extConf.DISPLAY_TYPE_SINGLE_MONITOR);
		for (Display display : displayList) {
			// 画面分割(面)情報
			List<DisplaySplit> displaySplitList = extConf.getDisplaySplitList(display);
			boolean isConnect;
			for (DisplaySplit displaySplit : displaySplitList) {
				// ping送信
				Ping ping = new Ping(displaySplit.getIpAddress(), PING_TIMEOUT);
				if (ping.ping()) {
					// ヘルスチェック用コマンド送信
					isConnect = healthExec(displaySplit.getIpAddress(), displaySplit.getPort());

				} else {
					log.warn("Health check ping error. ip={}", displaySplit.getIpAddress());
					isConnect = false;
				}

				// 接続状態MAP 更新
				Map<Integer, Boolean> connect = this.connectStatusMap.get(display.getDisplayNo());
				if (connect != null) {
					// displaySplitNoのMAPが既にあれば上書き (or 追加)
					connect.put(displaySplit.getDisplaySplitNo(), isConnect);
				} else {
					// displaySplitNoのMAPが無ければMAPを新規作成
					Map<Integer, Boolean> newConnect = new HashMap<>();
					newConnect.put(displaySplit.getDisplaySplitNo(), isConnect);
					this.connectStatusMap.put(display.getDisplayNo(), newConnect);
				}
			}
		}
		// 結果ログ出力
		this.connectStatusMap.forEach((dispNo, connect) -> connect.forEach((dispSplitNo, isConnect) -> log.info("displayNo={}, displaySplitNo={}, isConnect={}", dispNo, dispSplitNo, isConnect)));

		log.info("Health check end.");
	}

	/**
	 * ヘルスチェック用コマンド送信
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean healthExec(String ip, int port) {
		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		// ソケット接続
		SocketClient socket = SocketManager.getSocketClient(ip, port);
		if (socket.openAndReplyWait(CONNECT_TIMEOUT, RECEIVE_TIMEOUT, callback)) {
			// 応答チェック
			String recv = rcvCmd.get();
			//String login = (MSG_LOGIN + MSG_NEWLINE);
			String login = MSG_LOGIN;
			if (!Objects.equals(login, recv)) {
				log.error("Connect response error.");
				return false;
			}
			// ログイン
			if (sendLogin(socket)) {
				int error = 0;
				// 温度取得コマンド
				if (!sendGetTemp(socket)) {
					log.error("Get temperature error.");
					error++;
				}

				// 切断
				if (!sendLogout(socket)) {
					log.error("logout error.");
					error++;
				}
				socket.close();
				return error == 0;
			} else {
				log.error("login error.");
				socket.close();
				return false;
			}
		} else {
			log.error("Socket connection error.");
			return false;
		}
	}

	/**
	 * 入力切替設定要求
	 * @param inputSw 入力切替
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean selectInput(int displayNo, int displaySplitNo, int inputSw) {
		log.info("selectInput(): displayNo={}, displaySplitNo={}, inputSw={}", displayNo, displaySplitNo, inputSw);

		// 画面分割(面)情報 取得
		DisplaySplit displaySplit = extConf.getDisplaySplit(
				extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_SINGLE_MONITOR), displaySplitNo);

		if (displaySplit != null) {
			Input input = null;
			HdmiInput target = HdmiInput.valueOf(inputSw);
			String targetName = (target != null) ? target.toString() : null;
			// 入力切替先をモニタ情報から取得
			if (Objects.equals(displaySplit.getHdmiInput1(), targetName)) {
				input = Input.HDMI1;
			} else if (Objects.equals(displaySplit.getHdmiInput2(), targetName)) {
				input = Input.HDMI2;
			}

			// 入力切替先が見つかればモニタに入力切替コマンドを送信
			if (input != null) {
				boolean result = inputSetExec(displaySplit.getIpAddress(), displaySplit.getPort(), input);
				if (!result) {
					log.error("SingleMonitor input port setting error.");
					return false;
				}
			} else {
				// 入力切替先が見つからない
				log.warn("Input switch destination not found. displayNo={}, displaySplitNo={}, inputSw={}",
						displayNo, displaySplitNo, inputSw);
			}
		} else {
			// 対象のモニタが見つからない場合はログ出力して正常終了
			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
		}
		return true;
	}

	/**
	 * 入力切替設定要求
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @param inputPort 設定入力ポート
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean inputSetExec(String ip, int port, Input inputPort) {
		SocketClient socket;

		if (inputPort != null) {
			// 受信コールバック
			AtomicReference<String> rcvCmd = new AtomicReference<>();
			Consumer<byte[]> callback = (msg) -> {
				log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
				String str = new String(msg);
				rcvCmd.set(str);
				log.info("response: " + str.replace(MSG_NEWLINE, ""));
			};

			// ソケット接続
			socket = SocketManager.getSocketClient(ip, port);
			if (socket.openAndReplyWait(CONNECT_TIMEOUT, RECEIVE_TIMEOUT, callback)) {
				// 応答チェック
				String recv = rcvCmd.get();
				//String login = (MSG_LOGIN + MSG_NEWLINE);
				String login = MSG_LOGIN;
				if (!Objects.equals(login, recv)) {
					log.error("Connect response error.");
					return false;
				}
				// ログイン
				if (sendLogin(socket)) {
					int error = 0;
					// 入力切替コマンド
					if (!sendSetInput(socket, inputPort)) {
						log.error("Set input error.");
						error++;
					}

					setTileMatrix(socket, 0, 0, false);

					// 切断
					if (!sendLogout(socket)) {
						log.error("logout error.");
						error++;
					}
					socket.close();
					return error == 0;
				} else {
					log.error("login error.");
					socket.close();
					return false;
				}
			} else {
				log.error("Socket connection error.");
				return false;
			}
		} else {
			log.error("unknown parameter: inputPort");
			return false;
		}
	}

	/**
	 * ログインコマンド送信
	 * @param socket Open済みのSocket
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendLogin(SocketClient socket) {

		log.info("★★ログイン");

		byte[] sendData;

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		com.contents.common.CommonUtil.sleep(150);

		// ユーザー名送信
		sendData = CMD_USER.getBytes();
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答チェック
		{
			String recv = rcvCmd.get();
			String password = (MSG_NEWLINE + MSG_PASSWORD);
			if (!Objects.equals(password, recv)) {
				log.error("User response error.");
				return false;
			}
		}

		com.contents.common.CommonUtil.sleep(150);

		// パスワード送信
		sendData = CMD_PASS.getBytes();
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答チェック
		{
			String recv = rcvCmd.get();

			String ok = (MSG_OK + MSG_NEWLINE);

			if (Objects.equals(ok, recv)) {
				return true;
			}

			String newPassword = (MSG_NEWLINE + MSG_NEW_PASSWORD);
			if (Objects.equals(newPassword, recv)) {

				com.contents.common.CommonUtil.sleep(150);

				// パスワード送信
				sendData = CMD_PASS.getBytes();
				if (!socket.send(sendData, callback)) {
					return false;
				}

				recv = rcvCmd.get();

				String retypeNewPassword = (MSG_NEWLINE + MSG_RETYPE_NEW_PASSWORD);
				if (Objects.equals(retypeNewPassword, recv)) {

					com.contents.common.CommonUtil.sleep(150);

					// パスワード送信
					sendData = CMD_PASS.getBytes();
					if (!socket.send(sendData, callback)) {
						return false;
					}

					recv = rcvCmd.get();

					if (Objects.equals(ok, recv)) {
						return true;
					}

					return false;

				} else {
					log.error("non return {}", MSG_RETYPE_NEW_PASSWORD);
					return false;
				}
			}

			String incorrect = ("Login incorrect" + MSG_NEWLINE);
			if (Objects.equals(incorrect, recv)) {
				log.error("Password response error.");
				return false;
			}

		}

		return true;
	}

	/**
	 * ログアウトコマンド送信
	 * @param socket Open済みのSocket
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendLogout(SocketClient socket) {

		log.info("★★ログアウト");

		byte[] sendData;

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		com.contents.common.CommonUtil.sleep(150);

		// 終了コマンド送信
		sendData = CMD_BYE.getBytes();
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答チェック
		if (!Objects.equals((MSG_GOODBYE + MSG_NEWLINE), rcvCmd.get())) {
			log.error("Logout response error.");
			return false;
		}

		return true;
	}

	/**
	 * 入力切替コマンド送信
	 * @param socket Open済みのSocket
	 * @param input 入力設定
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendSetInput(SocketClient socket, Input input) {
		byte[] sendData;

		log.info("★★入力切替");

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		// 入力切替コマンド送信
		String strSetInput = createCommandSetInput(input);
		if (strSetInput == null) {
			log.warn("Set input command null.");
			return false;
		}

		com.contents.common.CommonUtil.sleep(150);

		sendData = strSetInput.getBytes();
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答チェック
		String recv = rcvCmd.get();
		String ok = (MSG_OK + MSG_NEWLINE);
		if (!Objects.equals(ok, recv)) {
			log.error("command error input change");
			return false;
		}

		return true;
	}

	/**
	 * コマンドレスポンス確認
	 * @param str レスポンス文字列
	 * @param lenMax レスポンス文字列最大長
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean responseCheckValue(String str, int lenMax) {
		// コマンド長
		if (str == null || str.length() > lenMax) {
			log.error("Response length error.");
			return false;
		}

		// 改行コード除去
		String chkStr = str.replace(MSG_NEWLINE, "");

		// 数値確認
		if (chkStr.matches("[0-9]+")) {
			int getVal = Integer.parseInt(chkStr);
			log.info("Get val = " + getVal);
		} else {
			log.error("Response value error.");
			return false;
		}
		return true;
	}

	/**
	 * 温度取得コマンド送信
	 * @param socket Open済みのSocket
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendGetTemp(SocketClient socket) {

		log.info("★★温度取得");

		byte[] sendData;

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		com.contents.common.CommonUtil.sleep(150);

		// 温度取得コマンド送信
		sendData = GET_TEMP.getBytes();
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答チェック
		if (!responseCheckValue(rcvCmd.get(), RES_TEMP_MAX_LEN)) {
			log.error("Get temperature response value error.");
			return false;
		}
		return true;
	}

	/**
	 * 入力切替設定コマンド作成
	 * @param input 入力ポート
	 * @return コマンド or null (入力ポートが対象外)
	 */
	private String createCommandSetInput(Input input) {
		String cmd = null;
		String sel = null;

		switch (input) {
		case TOGGLE -> sel = INPUT_PARM_TOGGLE;
		case DSUB -> sel = INPUT_PARM_DSUB;
		case HDMI1 -> sel = INPUT_PARM_HDMI1;
		case USB -> sel = INPUT_PARM_USB;
		case HDMI2 -> sel = INPUT_PARM_HDMI2;
		case HDMI3 -> sel = INPUT_PARM_HDMI3;
		default -> log.info("createCommandSetInput Unknown = " + input);
		}

		if (sel != null) {
			//cmd = CMD_INPUT + sel + CMD_NEWLINE;
			cmd = CMD_INPUT + sel + CMD_NEWLINE;
		}

		return cmd;
	}

	/**
	 * タイルマトリクス設定
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @param toggle タイルマトリクス実行 (true:実行, false:解除)
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean setTileMatrix(SocketClient socket, int displayNo, int displaySplitNo, boolean toggle) {

		log.info("★★タイルマトリクス設定");

		byte[] sendData;

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

//		{
//			java.util.Random r = new java.util.Random();
//			Integer num = r.nextInt(100);
//			toggle =  org.apache.commons.lang3.BooleanUtils.toBoolean(num % 2);
//			log.info("★★toggle: {}, num: {}", toggle, num);
//			if (toggle)
//				log.info("★★toggle {}", toggle);
//		}

		com.contents.common.CommonUtil.sleep(100);
		
		{
			// エンラージコマンド送信
			String cmd = toggle ? (CMD_ENLG + ENLG_ON + MSG_NEWLINE) : (CMD_ENLG + ENLG_OFF + MSG_NEWLINE);
			log.info(cmd);
			sendData = cmd.getBytes();
			if (!socket.send(sendData, callback)) {
				return false;
			}

			// 応答チェック
			String recv = rcvCmd.get();

			String ok = (MSG_OK + MSG_NEWLINE);
			if (!Objects.equals(ok, recv)) {
				log.error("command error input change");
				return false;
			}
		}

		return true;
	}
}
