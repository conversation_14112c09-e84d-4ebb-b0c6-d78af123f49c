package com.contents.model;


import com.fasterxml.jackson.annotation.JsonSubTypes;

/**
* AnyOfcontentDataDisplayContentDataSourceData
*/
@JsonSubTypes({
  @JsonSubTypes.Type(value = VehicleContent.class, name = "1"),
  @JsonSubTypes.Type(value = VehicleContent.class, name = "18"),
  @JsonSubTypes.Type(value = DeploymentContent.class, name = "2"),
  @JsonSubTypes.Type(value = CaseContent.class, name = "3"),
  @JsonSubTypes.Type(value = CaseHalfContent.class, name = "4"),
  @JsonSubTypes.Type(value = CaseQuarterContent.class, name = "5"),
        // 6: 時刻コンテンツ
  @JsonSubTypes.Type(value = IncomingCallAContent.class, name = "7"),
  @JsonSubTypes.Type(value = IncomingCallBContent.class, name = "8"),
  @JsonSubTypes.Type(value = WeatherContent.class, name = "9"),
  @JsonSubTypes.Type(value = TotalFrequencyContent.class, name = "10"),
  @JsonSubTypes.Type(value = AlarmContent.class, name = "11"),
  @JsonSubTypes.Type(value = AttendanceContent.class, name = "12"),
  @JsonSubTypes.Type(value = DoctorOnDutyContent.class, name = "13"),
  @JsonSubTypes.Type(value = ScheduleContent.class, name = "14"),
  @JsonSubTypes.Type(value = HandoverContent.class, name = "15"),
  @JsonSubTypes.Type(value = DigitalRadioContent.class, name = "16"),
  @JsonSubTypes.Type(value = AmbulanceRateContent.class, name = "17")
})
public interface AnyOfcontentDataDisplayContentDataSourceData {

}
