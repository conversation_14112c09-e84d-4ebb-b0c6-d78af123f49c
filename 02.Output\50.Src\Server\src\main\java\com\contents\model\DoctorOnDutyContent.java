package com.contents.model;

import java.util.Objects;
import com.contents.model.DoctorOnDutyContentMedicalSubject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DoctorOnDutyContent
 */
@Validated


public class DoctorOnDutyContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("medical_subject")
  @Valid
  private List<DoctorOnDutyContentMedicalSubject> medicalSubject = null;

  public DoctorOnDutyContent medicalSubject(List<DoctorOnDutyContentMedicalSubject> medicalSubject) {
    this.medicalSubject = medicalSubject;
    return this;
  }

  public DoctorOnDutyContent addMedicalSubjectItem(DoctorOnDutyContentMedicalSubject medicalSubjectItem) {
    if (this.medicalSubject == null) {
      this.medicalSubject = new ArrayList<DoctorOnDutyContentMedicalSubject>();
    }
    this.medicalSubject.add(medicalSubjectItem);
    return this;
  }

  /**
   * Get medicalSubject
   * @return medicalSubject
   **/
  @Schema(description = "")
      @Valid
  @Size(max=6)   public List<DoctorOnDutyContentMedicalSubject> getMedicalSubject() {
    return medicalSubject;
  }

  public void setMedicalSubject(List<DoctorOnDutyContentMedicalSubject> medicalSubject) {
    this.medicalSubject = medicalSubject;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DoctorOnDutyContent doctorOnDutyContent = (DoctorOnDutyContent) o;
    return Objects.equals(this.medicalSubject, doctorOnDutyContent.medicalSubject);
  }

  @Override
  public int hashCode() {
    return Objects.hash(medicalSubject);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DoctorOnDutyContent {\n");
    
    sb.append("    medicalSubject: ").append(toIndentedString(medicalSubject)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
