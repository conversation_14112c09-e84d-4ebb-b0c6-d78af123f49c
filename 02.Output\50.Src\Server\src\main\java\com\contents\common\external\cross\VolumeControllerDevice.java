package com.contents.common.external.cross;

public class VolumeControllerDevice extends AbstractDevice {

	/** ネットワーク情報 */
	protected NetworkInfo networkInfo;

	/**
	 * ネットワーク情報を取得します。
	 * @return ネットワーク情報
	 */
	public NetworkInfo getNetworkInfo() {
	    return networkInfo;
	}

	/**
	 * ネットワーク情報を設定します。
	 * @param networkInfo ネットワーク情報
	 */
	public void setNetworkInfo(NetworkInfo networkInfo) {
	    this.networkInfo = networkInfo;
	}
}
