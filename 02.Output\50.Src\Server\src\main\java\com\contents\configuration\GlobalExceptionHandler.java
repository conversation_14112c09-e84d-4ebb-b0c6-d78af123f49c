package com.contents.configuration;

import java.util.ArrayList;
import java.util.stream.Collectors;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.contents.common.CommonUtil;
import com.contents.common.bean.ValidationError;
import com.contents.model.ApiResult;

import lombok.extern.slf4j.Slf4j;

/**
 * GlobalのExceptionをここで一括処理。処理した結果をResponseに返す
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * BindExceptionを処理して、エラー情報をResponseに返す
     * @param e BindException
     * @return エラー情報
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler({BindException.class})
    public String bindExceptionHandler(final BindException e) {
        String message = e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.joining(" ; "));
        return "{\"errors\":\"" + message + "\"}";
    }

    /**
     * APIのValidationを通らない場合、MethodArgumentNotValidExceptionが出ます。
     * ここで処理して、エラー情報をResponseに返す
     * @param e MethodArgumentNotValidException
     * @return エラー情報
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResult> handler(final MethodArgumentNotValidException e) {
    	
        var errorList = new ArrayList<String>();
        var  errors = e.getBindingResult().getFieldErrors();
        errors.forEach((error) -> {
            var validError = new ValidationError(error.getField(), error.getRejectedValue().toString(), error.getDefaultMessage());
            errorList.add(validError.toString());
        });

        String errorMsg = StringUtils.join(errorList, ",\n ");
        log.error(errorMsg);

        var responseResult = new ResponseEntity<ApiResult>(
                new ApiResult(CommonUtil.API_RESULT_FAIL, errorMsg), HttpStatus.OK);

        return responseResult;
   }

    /**
     * ConstraintViolationExceptionを処理して、エラー情報をResponseに返す
     * @param e ConstraintViolationException
     * @return エラー情報
     */
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ConstraintViolationException.class)
    public String handler(final ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(" ; "));
        return "{\"errors\":\"" + message + "\"}";
    }

    /**
     * APIのJsonにParseできない場合、HttpMessageNotReadableExceptionが出ます。
     * ここで処理して、エラー情報をResponseに返す
     * @param e HttpMessageNotReadableException
     * @return エラー情報
     */
    @ExceptionHandler({HttpMessageNotReadableException.class})
    public ResponseEntity<ApiResult> bindExceptionHandler(final HttpMessageNotReadableException e) {
        log.error("bindExceptionHandler.", e);

        var responseResult = new ResponseEntity<ApiResult>(
                new ApiResult(CommonUtil.API_RESULT_FAIL, e.getMessage()), HttpStatus.OK);

        return responseResult;
    }
}