import React from 'react';
import Home from './pages/Home';

import { Routes, Route } from 'react-router-dom';
/**
 * WebAppの入り口
 * @module App
 *
 * @return {*} Routeを作成してAppを返す
 */
function App() {
  return (
    <div className="App">
      <header className="App-header">
        <Routes>
          <Route index element={<Home />} />
          <Route path="/" element={<Home />}></Route>
          <Route path="*" element={<NotFound />} />
        </Routes>
      </header>
    </div>
  );
}

/**
 * 404 Pageを作成して返す
 * @returns 404 Page
 */
const NotFound = () => {
  const msg = `ServerUrl${process.env.REACT_APP_APP_URL}?display_no=xx&display_split_no=xx のようなアドレスで、表示盤IDとモニターIDを提供して、アクセスしてください。`;

  return (
    <div className="bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center">
      <div className="flex flex-col items-center text-green-400">
        <div className="m-10 text-4xl">存在しないページです。</div>
        <div className="m-10 text-2xl">{msg}</div>
      </div>
    </div>
  );
};

export default App;
