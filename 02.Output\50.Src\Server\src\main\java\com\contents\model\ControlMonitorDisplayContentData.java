package com.contents.model;

import java.util.Objects;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import org.springframework.validation.annotation.Validated;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * ControlMonitorDisplayContentData
 */
@Validated

public class ControlMonitorDisplayContentData {
	@JsonProperty("input_switch")
	private Integer inputSwitch = null;

	@JsonProperty("source_no")
	private Integer sourceNo = null;

	@JsonProperty("source_name")
	private String sourceName = null;

	@JsonProperty("source_split_no")
	private Integer sourceSplitNo = null;

	@JsonProperty("source_disp_pat")
	private Integer sourceDispPat = null;

	@JsonProperty("source_vol_control_type")
	private Integer sourceVolControlType = null;

	@JsonProperty("source_vol_mute_control")
	private Integer sourceVolMuteControl = null;

	@JsonProperty("is_vol_control")
	private Integer isVolControl = null;

	public ControlMonitorDisplayContentData inputSwitch(Integer inputSwitch) {
		this.inputSwitch = inputSwitch;
		return this;
	}

	/**
	 * モニター入力ポート選択 （0：コンテンツ系　1：マトリクススイッチャー系）
	 * minimum: 0
	 * maximum: 1
	 * @return inputSwitch
	 **/
	@Schema(description = "モニター入力ポート選択 （0：コンテンツ系　1：マトリクススイッチャー系）")
	@Min(0)
	@Max(1)
	public Integer getInputSwitch() {
		return inputSwitch;
	}

	public void setInputSwitch(Integer inputSwitch) {
		this.inputSwitch = inputSwitch;
	}

	public ControlMonitorDisplayContentData sourceNo(Integer sourceNo) {
		this.sourceNo = sourceNo;
		return this;
	}

	/**
	 * 表示盤に表示するソース番号
	 * minimum: 0
	 * @return sourceNo
	 **/
	@Schema(description = "表示盤に表示するソース番号")

	@Min(0)
	public Integer getSourceNo() {
		return sourceNo;
	}

	public void setSourceNo(Integer sourceNo) {
		this.sourceNo = sourceNo;
	}

	public ControlMonitorDisplayContentData sourceName(String sourceName) {
		this.sourceName = sourceName;
		return this;
	}

	/**
	 * ソース番号に紐づく名称
	 * @return sourceName
	 **/
	@Schema(description = "ソース番号に紐づく名称")

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public ControlMonitorDisplayContentData sourceSplitNo(Integer sourceSplitNo) {
		this.sourceSplitNo = sourceSplitNo;
		return this;
	}

	/**
	 * 同一のソース番号(コンテンツ)を分割した番号 例：事案や気象情報など複数コンテンツ表示可能な場合に設定
	 * minimum: 0
	 * @return sourceSplitNo
	 **/
	@Schema(description = "同一のソース番号(コンテンツ)を分割した番号 例：事案や気象情報など複数コンテンツ表示可能な場合に設定")

	@Min(0)
	public Integer getSourceSplitNo() {
		return sourceSplitNo;
	}

	public void setSourceSplitNo(Integer sourceSplitNo) {
		this.sourceSplitNo = sourceSplitNo;
	}

	public ControlMonitorDisplayContentData sourceDispPat(Integer sourceDispPat) {
		this.sourceDispPat = sourceDispPat;
		return this;
	}

	/**
	 * ソースを表示するサイズのパターン
	 * minimum: 0
	 * maximum: 3
	 * @return sourceDispPat
	 **/
	@Schema(description = "ソースを表示するサイズのパターン")
	@Min(0)
	@Max(3)
	public Integer getSourceDispPat() {
		return sourceDispPat;
	}

	public void setSourceDispPat(Integer sourceDispPat) {
		this.sourceDispPat = sourceDispPat;
	}

	public ControlMonitorDisplayContentData sourceVolControlType(Integer sourceVolControlType) {
		this.sourceVolControlType = sourceVolControlType;
		return this;
	}

	/**
	 * 0~100,音量
	 * minimum: 0
	 * maximum: 100
	 * @return sourceVolControlType
	 **/
	@Schema(description = "0~100,音量")
	@Min(0)
	@Max(100)
	public Integer getSourceVolControlType() {
		return sourceVolControlType;
	}

	public void setSourceVolControlType(Integer sourceVolControlType) {
		this.sourceVolControlType = sourceVolControlType;
	}

	public ControlMonitorDisplayContentData sourceVolMuteControl(Integer sourceVolMuteControl) {
		this.sourceVolMuteControl = sourceVolMuteControl;
		return this;
	}

	/**
	 * 0：Mute有効　1：Mute解除
	 * minimum: 0
	 * maximum: 1
	 * @return sourceVolMuteControl
	 **/
	@Schema(description = "0：Mute有効　1：Mute解除")

	@Min(0)
	@Max(1)
	public Integer getSourceVolMuteControl() {
		return sourceVolMuteControl;
	}

	public void setSourceVolMuteControl(Integer sourceVolMuteControl) {
		this.sourceVolMuteControl = sourceVolMuteControl;
	}

	public ControlMonitorDisplayContentData isVolControl(Integer isVolControl) {
		this.isVolControl = isVolControl;
		return this;
	}

	/**
	 * 音を出すソースを指定する項目。音を出すソースにのみ1を設定する。 ※初回開発においては、1表示盤につき最大1画面分割番号のみ指定可能 0：ソースの音量を制御しない（音を出さない） 1：ソースの音量を制御する（音を出す）
	 * minimum: 0
	 * maximum: 1
	 * @return isVolControl
	 **/
	@Schema(description = "音を出すソースを指定する項目。音を出すソースにのみ1を設定する。 ※初回開発においては、1表示盤につき最大1画面分割番号のみ指定可能 0：ソースの音量を制御しない（音を出さない） 1：ソースの音量を制御する（音を出す）")

	@Min(0)
	@Max(1)
	public Integer getIsVolControl() {
		return isVolControl;
	}

	public void setIsVolControl(Integer isVolControl) {
		this.isVolControl = isVolControl;
	}

	@Override
	public boolean equals(java.lang.Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		ControlMonitorDisplayContentData controlMonitorDisplayContentData = (ControlMonitorDisplayContentData) o;
		return Objects.equals(this.inputSwitch, controlMonitorDisplayContentData.inputSwitch) &&
				Objects.equals(this.sourceNo, controlMonitorDisplayContentData.sourceNo) &&
				Objects.equals(this.sourceName, controlMonitorDisplayContentData.sourceName) &&
				Objects.equals(this.sourceSplitNo, controlMonitorDisplayContentData.sourceSplitNo) &&
				Objects.equals(this.sourceDispPat, controlMonitorDisplayContentData.sourceDispPat) &&
				Objects.equals(this.sourceVolControlType, controlMonitorDisplayContentData.sourceVolControlType) &&
				Objects.equals(this.sourceVolMuteControl, controlMonitorDisplayContentData.sourceVolMuteControl) &&
				Objects.equals(this.isVolControl, controlMonitorDisplayContentData.isVolControl);
	}

	@Override
	public int hashCode() {
		return Objects.hash(inputSwitch, sourceNo, sourceName, sourceSplitNo, sourceDispPat, sourceVolControlType, sourceVolMuteControl, isVolControl);
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder();
		sb.append("class ControlMonitorDisplayContentData {\n");

		sb.append("    inputSwitch: ").append(toIndentedString(inputSwitch)).append("\n");
		sb.append("    sourceNo: ").append(toIndentedString(sourceNo)).append("\n");
		sb.append("    sourceName: ").append(toIndentedString(sourceName)).append("\n");
		sb.append("    sourceSplitNo: ").append(toIndentedString(sourceSplitNo)).append("\n");
		sb.append("    sourceDispPat: ").append(toIndentedString(sourceDispPat)).append("\n");
		sb.append("    sourceVolControlType: ").append(toIndentedString(sourceVolControlType)).append("\n");
		sb.append("    sourceVolMuteControl: ").append(toIndentedString(sourceVolMuteControl)).append("\n");
		sb.append("    isVolControl: ").append(toIndentedString(isVolControl)).append("\n");
		sb.append("}");
		return sb.toString();
	}

	/**
	 * Convert the given object to string with each line indented by 4 spaces
	 * (except the first line).
	 */
	private String toIndentedString(java.lang.Object o) {
		if (o == null) {
			return "null";
		}
		return o.toString().replace("\n", "\n    ");
	}
}
