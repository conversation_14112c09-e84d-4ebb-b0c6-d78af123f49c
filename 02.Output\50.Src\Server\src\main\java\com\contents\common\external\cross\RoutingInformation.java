package com.contents.common.external.cross;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


/**
 * 機器ルーティング情報
 */
public class RoutingInformation {

	/** ディスプレイグループ管理リスト */
	private List<DisplayGroupInfo> displayGroupManageList;
	
	/** ディスプレイルーティングリスト */
	private List<DisplayRoutingInfo> displayRoutingList;
	
	/** スイッチャー映像ソースルーティングリスト */
	private List<SwitcherVideoRoutingInfo> switcherVideoRoutingList;
	
	/** スイッチャー音声ソースルーティングリスト */
	private List<SwitcherAudioRoutingInfo> switcherAudioRoutingList;
	
	/** 音声レベルコントローラールーティングリスト */
	private List<VolumeControllerRoutingInfo> volumeControllerRoutingList;

	/**
	 * ディスプレイグループ管理リストを取得します。
	 * @return ディスプレイグループ管理リスト
	 */
	public List<DisplayGroupInfo> getDisplayGroupManageList() {
	    return displayGroupManageList;
	}

	/**
	 * ディスプレイグループ管理リストを設定します。
	 * @param displayGroupManageList ディスプレイグループ管理リスト
	 */
	public void setDisplayGroupManageList(List<DisplayGroupInfo> displayGroupManageList) {
	    this.displayGroupManageList = displayGroupManageList;
	}

	/**
	 * ディスプレイルーティングリストを取得します。
	 * @return ディスプレイルーティングリスト
	 */
	public List<DisplayRoutingInfo> getDisplayRoutingList() {
	    return displayRoutingList;
	}

	/**
	 * ディスプレイルーティングリストを設定します。
	 * @param displayRoutingList ディスプレイルーティングリスト
	 */
	public void setDisplayRoutingList(List<DisplayRoutingInfo> displayRoutingList) {
	    this.displayRoutingList = displayRoutingList;
	}

	/**
	 * スイッチャー映像ソースルーティングリストを取得します。
	 * @return スイッチャー映像ソースルーティングリスト
	 */
	public List<SwitcherVideoRoutingInfo> getSwitcherVideoRoutingList() {
	    return switcherVideoRoutingList;
	}

	/**
	 * スイッチャー映像ソースルーティングリストを設定します。
	 * @param switcherVideoRoutingList スイッチャー映像ソースルーティングリスト
	 */
	public void setSwitcherVideoRoutingList(List<SwitcherVideoRoutingInfo> switcherVideoRoutingList) {
	    this.switcherVideoRoutingList = switcherVideoRoutingList;
	}

	/**
	 * スイッチャー音声ソースルーティングリストを取得します。
	 * @return スイッチャー音声ソースルーティングリスト
	 */
	public List<SwitcherAudioRoutingInfo> getSwitcherAudioRoutingList() {
	    return switcherAudioRoutingList;
	}

	/**
	 * スイッチャー音声ソースルーティングリストを設定します。
	 * @param switcherAudioRoutingList スイッチャー音声ソースルーティングリスト
	 */
	public void setSwitcherAudioRoutingList(List<SwitcherAudioRoutingInfo> switcherAudioRoutingList) {
	    this.switcherAudioRoutingList = switcherAudioRoutingList;
	}

	/**
	 * 音声レベルコントローラールーティングリストを取得します。
	 * @return 音声レベルコントローラールーティングリスト
	 */
	public List<VolumeControllerRoutingInfo> getVolumeControllerRoutingList() {
	    return volumeControllerRoutingList;
	}

	/**
	 * 音声レベルコントローラールーティングリストを設定します。
	 * @param volumeControllerRoutingList 音声レベルコントローラールーティングリスト
	 */
	public void setVolumeControllerRoutingList(List<VolumeControllerRoutingInfo> volumeControllerRoutingList) {
	    this.volumeControllerRoutingList = volumeControllerRoutingList;
	}

	public Iterator<DeviceRoutingInfo> iteratorDeviceRoutingInfo() {
		List<DeviceRoutingInfo> list = new ArrayList<>();
		
		list.addAll(displayRoutingList);
		list.addAll(switcherVideoRoutingList);
		list.addAll(switcherAudioRoutingList);
		list.addAll(volumeControllerRoutingList);
		
		return list.iterator();
	}
}
