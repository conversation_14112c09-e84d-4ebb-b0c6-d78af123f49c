# 拡張車両コンテンツ（50行表示）概要設計書

## 1. 概要

### 1.1 目的
既存の車両コンテンツ表示システム（Vehicle.js）を基に、表示行数を32行から50行に拡張した新しいコンポーネントを開発する。

### 1.2 背景
- 現在の消防指令システムには以下の車両コンテンツ表示コンポーネントが存在
  - `Vehicle.js`: 標準車両コンテンツ（最大32行表示）
  - `CustomVehicle.js`: カスタム車両コンテンツ
- より多くの車両情報を単一画面で表示するニーズに対応

### 1.3 新コンポーネント仕様
- **コンポーネント名**: `VehicleLarge.js`
- **表示行数**: 50行（現行32行から拡張）
- **表示形式**: 単一画面での全行表示
- **データ形式**: 既存Vehicle.jsと同一のAPI仕様に準拠
- **独立性**: 既存コンポーネントを変更せず、新規作成

## 2. 現状分析結果

### 2.1 Vehicle.jsの構造分析

#### 2.1.1 主要定数
```javascript
const MAX_ROW = 32;  // 最大表示行数
const GRID_COLS_VEHICLE_DEPLOY = '16px 56px minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(6, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';
const GRID_COLS_VEHICLE_NODEPLOY = '16px repeat(4, 56px) minmax(4px,1fr) repeat(4, 56px) minmax(4px,1fr) repeat(2, 56px) minmax(4px,1fr) repeat(2, 56px) 16px';
```

#### 2.1.2 コンポーネント階層
1. **Vehicle（メインコンポーネント）**
   - データ検証とフィルタリング
   - 最大行数制限の実装
   - 2列グリッドレイアウト（`grid-cols-2 grid-rows-16`）

2. **Station（署所単位表示）**
   - サブタイトル表示
   - 車両詳細行の生成

3. **VehicleDetailRow（車両詳細行）**
   - 個別車両データの表示
   - 配備/非配備モードの切り替え
   - 点滅制御

#### 2.1.3 レイアウト制御
- **グリッドシステム**: Tailwind CSS Grid
- **配備モード**: `grid-cols-vehicle-deploy`
- **非配備モード**: `grid-cols-vehicle-nodeploy`
- **左右分割**: `MAX_ROW / 2`で左右半分を判定

### 2.2 CustomVehicle.jsとの差異
- データ構造: `items`配列 vs `title_name`配列
- グリッドクラス: `quarter-vehicle-*` vs `vehicle-*`
- 表示パターン: `sourceDispPattern`による動的制御

### 2.3 依存関係
- **CellBox**: セル表示コンポーネント
- **BlinkBlock**: 点滅制御コンポーネント
- **Util.js**: `getCellFace`, `isValidSource`関数
- **Tailwind Config**: グリッドテンプレート定義

## 3. 技術設計

### 3.1 新コンポーネント仕様

#### 3.1.1 基本パラメータ
```javascript
const MAX_ROW = 50;  // 32から50に変更
```

#### 3.1.2 グリッドレイアウト設計
現行の2列16行から2列25行に変更：
```javascript
// 現行: grid-cols-2 grid-rows-16
// 新規: grid-cols-2 grid-rows-25
```

#### 3.1.3 左右分割ロジック
```javascript
// 現行: (props.startRow + props.index) <= (MAX_ROW / 2) // 16行
// 新規: (props.startRow + props.index) <= (MAX_ROW / 2) // 25行
```

### 3.2 Tailwind CSS設定拡張

#### 3.2.1 新グリッドクラス定義
`tailwind.config.js`に以下を追加：
```javascript
gridTemplateColumns: {
  // 既存
  'vehicle-deploy': '1rem 56px minmax(0.25rem,1fr) repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
  'vehicle-nodeploy': '1rem repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
  
  // 新規追加
  'extended-vehicle-deploy': '1rem 56px minmax(0.25rem,1fr) repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
  'extended-vehicle-nodeploy': '1rem repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
},
gridTemplateRows: {
  // 新規追加
  '25': 'repeat(25, minmax(0, 1fr))',
}
```

### 3.3 コンポーネント実装方針

#### 3.3.1 ファイル構成
```
Client/src/components/
├── Vehicle.js              (既存・変更なし)
├── CustomVehicle.js        (既存・変更なし)
└── VehicleLarge.js      (新規作成)
```

#### 3.3.2 実装アプローチ
1. **Vehicle.jsをベースとしたコピー&修正**
2. **MAX_ROW定数の変更**: 32 → 50
3. **グリッドクラスの変更**: 新規CSSクラス使用
4. **コンポーネント名の変更**: Vehicle → VehicleLarge

#### 3.3.3 API互換性
- 既存のAPI仕様（「3.3車両コンテンツ情報更新」）に完全準拠
- propsインターフェースは変更なし

## 4. 実装詳細

### 4.1 主要変更点

#### 4.1.1 定数変更
```javascript
// Before
const MAX_ROW = 32;

// After  
const MAX_ROW = 50;
```

#### 4.1.2 グリッドレイアウト変更
```javascript
// Before
<div className="grid grid-cols-2 grid-rows-16 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]">

// After
<div className="grid grid-cols-2 grid-rows-25 grid-flow-col text-[3.5rem] leading-[1] gap-x-[2.25rem] gap-y-[0.75rem]">
```

#### 4.1.3 CSS クラス名変更
```javascript
// Before
gridCol = 'grid-cols-vehicle-deploy';
gridCol = 'grid-cols-vehicle-nodeploy';

// After
gridCol = 'grid-cols-extended-vehicle-deploy';
gridCol = 'grid-cols-extended-vehicle-nodeploy';
```

## 5. Server端実装要件

### 5.1 SourceNo管理の拡張

#### 5.1.1 現状分析結果
**既存SourceNo定義（SourceNo.java）:**
```java
/**
 * Source No仕様。
 1 車両コンテンツ情報更新
 2 配備状況コンテンツ情報更新
 3 事案コンテンツ情報更新
 4 事案コンテンツ情報更新（1/2サイズ）
 5 事案コンテンツ情報更新（1/4サイズ）
 6 現在時刻コンテンツ情報更新
 7 着信状況コンテンツA情報更新
 8 着信状況コンテンツB情報更新
 9 気象コンテンツ情報更新
 10 総合度数コンテンツ情報更新
 11 予警報コンテンツ情報更新
 12 出退コンテンツ情報更新
 13 当番医コンテンツ情報更新
 14 予定コンテンツ情報更新
 15 引継事項コンテンツ情報更新
 16 無線デジタルコンテンツ情報更新
 17 救急車稼働率コンテンツ情報更新
 18 拡張車両コンテンツ情報更新（50行表示）
 */
```

**現在の実装状況:**
- SourceNo.javaでは`Now(6)`のみ定義済み
- 実際の処理では数値（1-17）で直接参照
- SourceNo=1が既存車両コンテンツ（32行）
- SourceNo=18が新規拡張車両コンテンツ（50行）用として利用可能

#### 5.1.2 必要な修正

**1. SourceNo.java拡張**
JavaDocのみ：18 新規50行車両コンテンツ用

**2. SystemService.java修正点**
- `validateVehicleContent()`: SourceNo=18の処理追加
- `convertVehicleSourceData()`: SourceNo=18の変換処理追加
- `sendContent()`: SourceNo=18の特別処理（必要に応じて）

### 5.2 API仕様の拡張

#### 5.2.1 現状のAPI構造
**既存VehicleContent（SourceNo=1）:**
- API端点: `/content` (POST)
- データ構造: `VehicleContent`クラス
- 最大行数制限: Server側では制限なし（Client側で32行制限）

#### 5.2.2 新規SourceNo=18の対応
**APISpecification.yaml修正不要:**
- 既存の`VehicleContent`スキーマをそのまま利用
- `source_no: 18`として新規コンテンツを識別
- データ構造は完全に既存と同一

**使用例:**
```yaml
display_content_data:
  - source_no: 18                              # 新規SourceNo
    source_name: 拡張車両コンテンツ（50行表示）  # 新規名称
    source_split_no: 0
    source_data:                               # 既存VehicleContentと同一構造
      is_deployment: 1
      title_name: [...]
```

### 5.3 データベース対応

#### 5.3.1 既存テーブル構造
- `content`テーブル: `source_no`フィールドでコンテンツ種別管理
- `control`テーブル: 表示制御情報管理
- 既存構造でSourceNo=18に対応可能

#### 5.3.2 必要な設定
- 初期データ投入: SourceNo=18用のマスタデータ
- 制御情報設定: 新規コンテンツの表示制御設定

## 6. 実装スケジュール

### Phase 1: Server端基盤実装
1. SourceNo.java拡張（SourceNo=18追加）
2. SystemService.java修正（バリデーション・変換処理）
3. 初期データ・設定投入

### Phase 2: Client端実装
1. Tailwind CSS設定拡張
2. VehicleLarge.jsの基本実装
3. 基本表示機能の確認

### Phase 3: 統合実装
1. データフィルタリング機能
2. 点滅制御機能
3. レイアウト調整

## 7. 詳細実装方針

### 7.1 Server端実装詳細

#### 7.1.1 修正対象ファイル一覧
```
Server/src/main/java/com/contents/
├── common/bean/SourceNo.java                    # JavaDoc追加（18 新規50行車両コンテンツ用）
├── service/SystemService.java                   # バリデーション・変換処理追加
└── (その他関連ファイルは修正不要)
```

#### 7.1.2 SystemService.java修正概要
**validateVehicleContent()メソッド:**
```java
// 既存: monitors.stream().filter(item -> item.getSourceNo() == 1)
// 追加: monitors.stream().filter(item -> item.getSourceNo() == 18)
```

**convertVehicleSourceData()メソッド:**
```java
// 既存: if (temporaries.stream().anyMatch(d -> Objects.equals(d.getSourceNo(), 1)))
// 追加: SourceNo=18の処理分岐追加
```

### 7.2 Client端実装詳細

#### 7.2.1 修正対象ファイル一覧
```
Client/src/components/VehicleLarge.js         # 新規作成（50行車両コンテンツ）
Client/tailwind.config.js                       # CSS設定拡張（既存ファイル修正）
```

#### 7.2.2 VehicleLarge.js実装方針
**基本アプローチ:**
- Vehicle.jsの実装パターンを完全踏襲
- コンポーネント名: `VehicleLarge`
- 表示行数: MAX_ROW = 50（32→50）
- レイアウト: 2列25行グリッド（2×25=50）

**具体的変更点:**
```javascript
// 1. 定数定義変更
const MAX_ROW = 50;  // Vehicle.js: 32 → VehicleLarge.js: 50

// 2. グリッドレイアウト変更
<div className="grid grid-cols-2 grid-rows-25 grid-flow-col ...">
// Vehicle.js: grid-rows-16 → VehicleLarge.js: grid-rows-25

// 3. CSS クラス名変更
// 配備表示時
gridCol = 'grid-cols-extended-vehicle-deploy';
// 非配備表示時
gridCol = 'grid-cols-extended-vehicle-nodeploy';

// 4. 左右分割ロジック（変更なし）
(props.startRow + props.index) <= (MAX_ROW / 2)  // 25行で左右分割
```

**アーキテクチャ一貫性:**
- プロップス構造: Vehicle.jsと完全同一
- 状態管理: 既存パターン踏襲
- イベントハンドリング: 変更なし
- データフロー: SourceNo判定のみ追加

### 7.3 CSS設定拡張

#### 7.3.1 tailwind.config.js追加内容
```javascript
// gridTemplateRows に追加
gridTemplateRows: {
  // 既存設定（'16': 'repeat(16, minmax(0, 1fr))'等）維持
  '25': 'repeat(25, minmax(0, 1fr))',  // 50行÷2列=25行用
},

// gridTemplateColumns に追加
gridTemplateColumns: {
  // 既存設定維持
  'vehicle-deploy':           '1rem 56px            minmax(0.25rem,1fr) repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
  'vehicle-nodeploy':         '1rem repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',

  // 新規追加（50行用）
  'extended-vehicle-deploy':   '1rem 56px            minmax(0.25rem,1fr) repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
  'extended-vehicle-nodeploy': '1rem repeat(4, 56px) minmax(0.25rem,1fr) repeat(6, 56px) minmax(0.25rem,1fr) repeat(2, 56px) minmax(0.25rem,1fr) repeat(2, 56px) 1rem',
},
```

### 7.4 実装品質保証

#### 7.4.1 コード品質基準
- **既存アーキテクチャ準拠**: Vehicle.jsと同一の実装パターン維持
- **命名規則統一**: 既存コンポーネントとの一貫性保持
- **CSS設計原則**: Tailwind CSS設定の既存パターン踏襲

#### 7.4.2 段階的デプロイ対応
- **並行運用**: SourceNo=1（32行）とSourceNo=18（50行）の同時稼働
- **切り替え制御**: 表示盤設定による個別コンテンツ制御
- **ロールバック対応**: 新機能無効化による既存システム復帰可能


## 8. 統合テスト方針

### 8.1 テスト戦略概要
新規SourceNo=18機能については包括的なテストケースによる機能検証を実施し、既存機能については回帰テストによる動作保証を行う。

### 8.2 SourceNo=18新機能テスト
**Client/Server機能テスト:**
- 50行データでの表示確認（MAX_ROW=50の動作検証）
- 2列25行グリッドレイアウトの表示確認
- 配備/非配備モード切り替えテスト
- 点滅機能動作確認（BlinkBlock連携）
- SourceNo=18のAPI受信・処理テスト
- VehicleContentデータバリデーション機能テスト

### 8.3 既存機能回帰テスト
**SourceNo=1（32行車両コンテンツ）:**
- 既存Vehicle.jsコンポーネントの無変更動作確認
- 既存APIエンドポイントの正常動作確認

**システム全体:**
- SourceNo=1とSourceNo=18の並行運用テスト
- 表示盤制御での個別切り替えテスト
- 既存システム動作への影響なし確認

### 8.4 互換性保証テスト
- **API互換性**: 既存VehicleContent構造での完全互換性確認
- **設定互換性**: 既存設定への影響なし確認
- **段階的移行**: SourceNo切り替えによる段階移行可能性確認

## 9. 実装完了判定基準

### 9.1 機能要件達成基準
- ✅ 50行車両データの正常表示（2列25行レイアウト）
- ✅ SourceNo=18による新規コンテンツ識別
- ✅ 配備/非配備モード切り替え機能
- ✅ 既存SourceNo=1機能への影響なし

### 9.2 技術要件達成基準
- ✅ Server端: SourceNo=18処理ロジック実装
- ✅ Client端: VehicleLarge.jsコンポーネント作成
- ✅ CSS: Tailwind設定拡張（25行グリッド対応）
- ✅ API: 既存VehicleContent構造での完全互換性

### 9.3 品質要件達成基準
- ✅ 統合テスト: 全テストケース合格
- ✅ 回帰テスト: 既存機能への影響なし確認
- ✅ 互換性: 段階的移行可能性確認

