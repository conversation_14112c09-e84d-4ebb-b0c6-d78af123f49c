import React from 'react';
import PropTypes from 'prop-types';

const propTypes = {
  fontSize: PropTypes.string,
  title: PropTypes.string,
};

/**
 * コンテンツのタイトル
 * 
 * @module Title
 * @component
 * @param {propTypes} props
 * @returns タイトル
 */
const Title = (props) => {
  const style = {
    fontSize: props.fontSize || '3.5rem',
  };

  return (
    <div
      className="flex flex-col items-center bg-white text-black"
      style={style}
    >
      <div className="leading-none">{props.title}</div>
    </div>
  );
};

Title.propTypes = propTypes;
export default Title;
