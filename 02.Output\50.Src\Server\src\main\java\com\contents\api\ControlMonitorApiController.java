package com.contents.api;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import com.contents.common.CommonUtil;
import com.contents.common.db.History;
import com.contents.manager.TaskQueue;
import com.contents.model.ApiResult;
import com.contents.model.ControlMonitor;
import com.contents.model.ControlMonitorDisplayControlData;
import com.contents.service.SystemService;
import com.contents.service.UtilityService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;

@RestController
public class ControlMonitorApiController implements ControlMonitorApi {

	private static final Logger log = LoggerFactory.getLogger(ControlMonitorApiController.class);

	private final ObjectMapper objectMapper;

	private final HttpServletRequest request;

	private final SystemService systemService;

	private final UtilityService utilityService;

	private final TaskQueue taskQueue;

	@Autowired
	public ControlMonitorApiController(ObjectMapper objectMapper, HttpServletRequest request, SystemService systemService, UtilityService utilityService, TaskQueue taskQueue) {
		this.objectMapper = objectMapper;
		this.request = request;
		this.systemService = systemService;
		this.utilityService = utilityService;
		this.taskQueue = taskQueue;
	}

	public DeferredResult<ResponseEntity<ApiResult>> controlMonitorPost(@Parameter(in = ParameterIn.DEFAULT, description = "", required = true, schema = @Schema()) @Valid @RequestBody ControlMonitor body) {

		log.info("★ control_monitor ★★★★★★★★★★");

		String taskId;
		synchronized (taskQueue) {
			taskId = taskQueue.getTaskId("controlMonitorPost");
		}

		History history = null;
		// 1, 履歴を保存
		try {
			history = systemService.saveApiBody2History(request, objectMapper.writeValueAsString(body));
		} catch (JsonProcessingException e) {
			log.error("contentPost: save history error");
		}

		DeferredResult<ResponseEntity<ApiResult>> deferredResult = utilityService.getResponseEntityDeferredResult(request.getRequestURI(), taskId, history, body);

		String accept = request.getHeader("Accept");
		if (accept == null || !accept.contains("application/json")) {
			deferredResult.setResult(utilityService.getApiResultNotJson(request.getRequestURI()));
			return deferredResult;
		}
		if (body.getDisplayControlData() == null) {
			deferredResult.setResult(utilityService.getApiResultNoApiData(request.getRequestURI()));
			return deferredResult;
		}

		// APIを一つのTaskとして、IDを付けて管理する
		ApiResult apiResult = new ApiResult(CommonUtil.API_RESULT_SUCCESS);
		synchronized (taskQueue) {
			taskQueue.putDeferredResult(taskId, deferredResult);
			taskQueue.putApiResult(taskId, apiResult);
		}

		try {
			// 1. 制御情報をDBに保存を含めて、他の制御を実施
			List<ControlMonitorDisplayControlData> displayControlData = body.getDisplayControlData();
			
			List<String> errors = systemService.executeMonitorControl(displayControlData, taskId);
			
			if (errors.size() > 0) {
				
				String message = StringUtils.join(errors, System.lineSeparator());
				
				log.error("controlMonitorPost: {}", message);
				apiResult.setMsg(message);
				apiResult.setResult(CommonUtil.API_RESULT_FAIL);
			}
			
			synchronized (taskQueue) {
				taskQueue.taskSendMsgDone(taskId);
			}
			return deferredResult;
		} catch (Exception e) {
			log.error("Exception in Task: {}", taskId);
			deferredResult.setResult(utilityService.getApiResultInternalError(request.getRequestURI(), e));
			return deferredResult;
		}
	}

}
