import React from 'react';
import Title from './elements/Title';
import TextScroll from './elements/TextScroll';
import Cell from './elements/Cell';
import CellBox from './elements/CellBox';
import {
  getCellFace,
  isValidSource,
  formateDatetimeText,
  splitDateAndTime,
} from '../utils/Util.js';

/**
 * 予警報コンテンツ<br>
 * propsは、「3.12予警報コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module Alarm
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Alarm = (props) => {
  const MAX_ROW = 5;
  return (
    <>
      <Title title={'警報・注意報'} />
      {isValidSource(props) && (
        <div
          className="border-transparent border-x-[1rem] grid leading-[1]
          grid-cols-[repeat(4,4.5rem)_1.2rem_repeat(4,4.5rem)_minmax(0.25rem,1fr)_repeat(9,1fr)] items-end gap-y-[4rem] mt-[3rem]"
        >
          {props.issued_ts?.map((item, index) => {
            //・1コンテンツあたり最大5件の情報を表示可能。
            if (index >= MAX_ROW) return undefined;

            return <AlarmRow key={index} {...props} index={index} />;
          })}
        </div>
      )}
    </>
  );
};

const AlarmRow = (props) => {
  if (
    !props.issued_ts[props.index] ||
    !props.combined_forecast_warning ||
    !props.combined_forecast_warning[props.index]
  ) {
    return;
  }

  let cellDateProps = getCellFace(
    props.issued_ts[props.index],
    'text-4xl my-4'
  );
  let cellTimeProps = { ...cellDateProps };

  let cell2DateTimeSeperator = {
    background_color: cellDateProps.background_color,
    className: `${cellDateProps.className} col-start-5`,
    text: ' ',
  };

  let splitedDateAndTime = splitDateAndTime(cellDateProps.text);
  if (splitedDateAndTime) {
    cellDateProps.text = formateDatetimeText(splitedDateAndTime.date, '3rem');
    cellDateProps.className = `col-span-4 ${cellDateProps.className}`;

    cellTimeProps.text = formateDatetimeText(splitedDateAndTime.time, '3rem');
    cellTimeProps.className = `col-start-6 col-span-4 ${cellTimeProps.className}`;
  } else {
    cellDateProps.text = formateDatetimeText(cellDateProps.text, '3rem');
    cellDateProps.className = `col-span-9 ${cellDateProps.className}`;
  }

  let cellPropAlarm = getCellFace(
    props.combined_forecast_warning[props.index],
    'col-span-9 col-start-11 text-7xl'
  );

  let cellPropAlarmFrame = {
    className: cellPropAlarm.className,
    text_color: cellPropAlarm.text_color,
  };

  return (
    <>
      {!splitedDateAndTime && <Cell {...cellDateProps} />}
      {splitedDateAndTime && (
        <>
          <Cell {...cellDateProps} />
          <Cell {...cell2DateTimeSeperator} />
          <Cell {...cellTimeProps} />
        </>
      )}
      <CellBox {...cellPropAlarmFrame}>
        <div className="text-8xl">
          <TextScroll
            content={cellPropAlarm.text}
            background_color={cellPropAlarm.background_color}
          />
        </div>
      </CellBox>
    </>
  );
};

export default Alarm;
