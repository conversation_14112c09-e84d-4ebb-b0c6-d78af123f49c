# VehicleLarge.js 双模式兼容性技术分析

## 1. 现有架构差异对比

### 1.1 数据结构差异
```javascript
// VehicleLarge.js (单画面模式)
props = {
  title_name: [
    {
      display_text: "消防署",
      car_name: [{ display_text: "救急車1" }],
      town_name: [{ display_text: "中央区" }]
    }
  ]
}

// VehicleLargeQuarter.js (四等分模式)
props = {
  items: [
    { title: { display_text: "消防署" } },
    { car_name: { display_text: "救急車1" }, town_name: { display_text: "中央区" } }
  ]
}
```

### 1.2 布局逻辑差异
```javascript
// VehicleLarge.js - 固定2列25行布局
<div className="grid grid-cols-2 grid-rows-25 grid-flow-col text-[2.4rem]">

// VehicleLargeQuarter.js - 动态布局
const getGridClass = (props) => {
  if (!(props.sourceDispPattern === 1))
    return 'grid-cols-2 grid-rows-25 grid-flow-col text-[48px]';
  
  if (props.column_position == 'left')
    return 'grid-cols-1 grid-rows-12 grid-flow-col text-[48px] pr-[16px]';
}
```

### 1.3 CSS类使用差异
```javascript
// VehicleLarge.js
gridCol = 'grid-cols-extended-vehicle-deploy';

// VehicleLargeQuarter.js  
gridCol = 'grid-cols-quarter-extended-vehicle-deploy';
```

## 2. 双模式兼容改造方案

### 2.1 数据结构适配器
```javascript
/**
 * 数据结构适配器 - 统一处理两种数据格式
 */
const DataAdapter = {
  // 检测数据结构类型
  detectDataType: (props) => {
    return props.items ? 'items' : 'title_name';
  },
  
  // 标准化数据结构
  normalizeData: (props) => {
    const dataType = DataAdapter.detectDataType(props);
    
    if (dataType === 'items') {
      // items结构已经是扁平化的，直接返回
      return props.items;
    } else {
      // title_name结构需要转换为扁平化
      return DataAdapter.convertTitleNameToItems(props.title_name);
    }
  },
  
  // title_name转items的转换逻辑
  convertTitleNameToItems: (titleName) => {
    const items = [];
    
    titleName.forEach(section => {
      // 添加标题行
      items.push({ title: section });
      
      // 添加数据行
      const maxLength = Math.max(
        section.car_name?.length || 0,
        section.town_name?.length || 0,
        section.disaster_type?.length || 0,
        section.avm_dynamic_state?.length || 0,
        section.deployment?.length || 0
      );
      
      for (let i = 0; i < maxLength; i++) {
        items.push({
          car_name: section.car_name?.[i],
          town_name: section.town_name?.[i],
          disaster_type: section.disaster_type?.[i],
          avm_dynamic_state: section.avm_dynamic_state?.[i],
          deployment: section.deployment?.[i],
          lighting_setting: section.lighting_setting?.[i]
        });
      }
    });
    
    return items;
  }
};
```

### 2.2 布局管理器
```javascript
/**
 * 布局管理器 - 根据显示模式动态选择布局
 */
const LayoutManager = {
  // 获取主容器网格类
  getMainGridClass: (props) => {
    const isQuadSplit = props.sourceDispPattern === 1;
    
    if (!isQuadSplit) {
      // 单画面模式：2列25行
      return 'grid-cols-2 grid-rows-25 grid-flow-col text-[2.4rem] leading-[1] gap-x-[2.25rem] gap-y-[0.3rem]';
    } else {
      // 四等分模式：根据列位置决定
      const baseClass = 'grid-flow-col text-[48px] leading-[1] gap-y-[8px]';
      
      if (props.column_position === 'left') {
        return `grid-cols-1 grid-rows-12 ${baseClass} pr-[16px]`;
      } else if (props.column_position === 'right') {
        return `grid-cols-1 grid-rows-12 ${baseClass} pl-[16px]`;
      } else {
        return `grid-cols-1 grid-rows-12 ${baseClass}`;
      }
    }
  },
  
  // 获取详细行网格类
  getDetailGridClass: (props, showDelopy) => {
    const isQuadSplit = props.sourceDispPattern === 1;
    
    if (!isQuadSplit) {
      // 单画面模式
      return showDelopy ? 'grid-cols-extended-vehicle-deploy' : 'grid-cols-extended-vehicle-nodeploy';
    } else {
      // 四等分模式
      return showDelopy ? 'grid-cols-quarter-extended-vehicle-deploy' : 'grid-cols-quarter-extended-vehicle-nodeploy';
    }
  }
};
```

### 2.3 统一的VehicleLarge组件
```javascript
/**
 * 统一的VehicleLarge组件 - 支持双模式
 */
const VehicleLarge = (props) => {
  if (!isValidSource(props)) return;
  
  // 数据适配
  const normalizedData = DataAdapter.normalizeData(props);
  const showDelopy = props.is_deployment === 1;
  const mainGridClass = LayoutManager.getMainGridClass(props);
  
  return (
    <>
      {isValidSource(props) && (
        <div className={`grid ${mainGridClass}`}>
          {normalizedData.map((item, index) => (
            <UnifiedStation
              key={index}
              {...item}
              showDelopy={showDelopy}
              sourceDispPattern={props.sourceDispPattern}
              columnPosition={props.column_position}
            />
          ))}
        </div>
      )}
    </>
  );
};

/**
 * 统一的Station组件
 */
const UnifiedStation = (props) => {
  const gridCol = LayoutManager.getDetailGridClass(props, props.showDelopy);
  
  return (
    <>
      {props.title && (
        <div className={`grid ${gridCol}`}>
          <CellBox {...getCellFace(props.title, 'col-span-full flex flex-col items-center')}>
            <span>{props.title.display_text}</span>
          </CellBox>
        </div>
      )}
      {!props.title && (
        <UnifiedVehicleDetailRow {...props} gridCol={gridCol} />
      )}
    </>
  );
};
```

## 3. 优缺点对比分析

### 3.1 统一组件方案优点
✅ **代码维护性**
- 单一组件维护，减少重复代码
- 统一的bug修复和功能增强
- 一致的代码风格和架构

✅ **功能一致性**
- 两种模式使用相同的核心逻辑
- 减少功能差异导致的问题
- 统一的测试覆盖

✅ **扩展性**
- 新增显示模式时只需修改一个组件
- 配置化的布局管理
- 更容易添加新的数据结构支持

### 3.2 统一组件方案缺点
❌ **复杂性增加**
- 组件内部逻辑更复杂
- 需要额外的适配器和管理器
- 调试难度增加

❌ **性能考虑**
- 运行时数据转换开销
- 更多的条件判断
- 内存使用略有增加

### 3.3 独立组件方案优点
✅ **简单性**
- 每个组件职责单一
- 代码逻辑清晰
- 调试容易

✅ **性能**
- 无运行时转换开销
- 针对性优化
- 更少的条件判断

### 3.4 独立组件方案缺点
❌ **维护成本**
- 多个组件需要同步维护
- 重复代码较多
- 功能差异风险

## 4. 实施建议

### 4.1 推荐方案：渐进式统一
1. **第一阶段**：保持现有独立组件，完善功能
2. **第二阶段**：开发统一组件作为可选方案
3. **第三阶段**：充分测试后逐步迁移

### 4.2 实施优先级
- **高优先级**：数据适配器开发
- **中优先级**：布局管理器开发  
- **低优先级**：完全统一组件

### 4.3 风险控制
- 保持向后兼容性
- 充分的单元测试和集成测试
- 分阶段部署和验证
