<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="content-server" />
        <module name="swagger-spring" />
        <module name="Server" />
        <module name="ContentsAP" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="openapi-spring" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ContentsAP" options="-parameters" />
      <module name="Server" options="-parameters" />
      <module name="content-server" options="-parameters" />
      <module name="openapi-spring" options="-parameters" />
      <module name="swagger-spring" options="-parameters" />
    </option>
  </component>
</project>