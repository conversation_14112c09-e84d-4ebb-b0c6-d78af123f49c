<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<property name="catalinaBase" value="${catalina.base:-.}" />
	
	
    <!-- コンソールへの出力 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5le [%thread]  %logger [%file:%line] - %msg %n</pattern>
        </encoder>
    </appender>

    <!-- アプリケーションログファイルへの出力 -->
    <appender name="FILE_APP" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalinaBase}/logs/vene/ContentServer.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalinaBase}/logs/vene/ContentServer.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level %logger - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="RECEIVE_DATA_FILE_APP" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalinaBase}/logs/vene/ApiReceive.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${catalinaBase}/logs/vene/ApiReceive.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level %logger - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.contents.configuration.RequestLoggingFilter" level="INFO" additivity="true">
        <appender-ref ref="RECEIVE_DATA_FILE_APP"/>
    </logger>

    <!-- ルートロガー設定 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE_APP"/>
    </root>
    
</configuration>