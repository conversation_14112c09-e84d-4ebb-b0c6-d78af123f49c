const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

// 出力するWARファイルのパス
const outputDir = path.join(__dirname, '../release');
const outputPath = path.join(outputDir, 'contentapp.war');

// release フォルダが存在しない場合、作成する
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log('Release folder created:', outputDir);
}

// 出力ストリームを作成
const output = fs.createWriteStream(outputPath);

// アーカイバを作成（ZIP形式を使用）
const archive = archiver('zip', {
  zlib: { level: 9 } // 最大圧縮
});

// ストリームに書き込み
output.on('close', function () {
  console.log('WAR file has been created successfully:', outputPath);
});

archive.on('error', function (err) {
  throw err;
});

// WAR ファイルの作成（`WEB-INF` フォルダとその他必要なファイルを追加）
archive.pipe(output);

// `build` フォルダの内容を追加
archive.directory(path.join(__dirname, '../build'), false);

// 必要に応じて `WEB-INF` フォルダや設定ファイルを追加
const webInfPath = path.join(__dirname, '../WEB-INF');
archive.directory(webInfPath, 'WEB-INF');

// アーカイブ作成を開始
archive.finalize();
