package com.contents.common;

import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;

import com.contents.common.annotation.ExternalDeviceProduct;
import com.contents.devicemodel.DeviceModel;

import lombok.extern.slf4j.Slf4j;

/**
 * SpringBootで管理するBeanを操作する為のUtility
 */
@Component
@Slf4j
public class SpringContextUtil implements ApplicationContextAware {

	private static ApplicationContext applicationContext;

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		SpringContextUtil.applicationContext = applicationContext;
	}

	//applicationContextを取得
	public static ApplicationContext getApplicationContext() {
		return applicationContext;
	}

	//nameで、Beanを取得
	public static Object getBean(String name) {
		return getApplicationContext().getBean(name);
	}

	//classでBeanを取得
	public static <T> T getBean(Class<T> clazz) {
		return getApplicationContext().getBean(clazz);
	}

	//nameとClazzで、Beanを取得
	public static <T> T getBean(String name, Class<T> clazz) {
		return getApplicationContext().getBean(name, clazz);
	}
	
	public static <T> void registerBean(Class<T> beanClass) {
		GenericApplicationContext genericApplicationContext = (GenericApplicationContext) applicationContext;
		genericApplicationContext.registerBean(beanClass);
	}
	
	private static void initProductBeans() {
		
		if (productBeans == null) {
			Map<String, Class<DeviceModel>> map = new HashMap<String, Class<DeviceModel>>();
			ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
	        scanner.addIncludeFilter(new AnnotationTypeFilter(ExternalDeviceProduct.class));
	        
	        Set<BeanDefinition> beanDefinitions = scanner.findCandidateComponents("com.contents.devicemodel");
	        
	        for (BeanDefinition beanDefinition : beanDefinitions) {
	        	try {
	        		Class<?> clazz = Class.forName(beanDefinition.getBeanClassName());
	        		ExternalDeviceProduct edp = clazz.getAnnotation(ExternalDeviceProduct.class);
	        		String [] names = edp.names();
	        		
	        		if (names == null || names.length == 0)
	        			continue;
	        		
					for (String name : names) {
						try {
							map.put(name, (Class<DeviceModel>) clazz);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
	        }
	        
	        productBeans = map;
		}
	}

	private static Map<String, Class<DeviceModel>> productBeans = null;

	public static <T extends DeviceModel> T getDevice(String productName, Class<T> clazz) {

		initProductBeans();
		
		for (Iterator<Map.Entry<String, Class<DeviceModel>>> ite = productBeans.entrySet().iterator(); ite.hasNext();) {
			
			Map.Entry<String, Class<DeviceModel>> entry = ite.next();
			
			if (!entry.getKey().equals(productName))
				continue;
			
			Class<DeviceModel> cls = entry.getValue();
			
			try {
				
				Constructor<DeviceModel> constructor = cls.getConstructor();
				
				DeviceModel instance = constructor.newInstance();
			
				log.info("ExternalDevice Create Instance ClassName: {}", cls.getName());
				
				return (T)instance;
				
			} catch (Exception e) {
				log.error("ClassName: {}", cls.getName());
				log.error("Error create instance ", e);
			}
		}

		return null; // 該当するProductが見つからなかった場合
	}
}
