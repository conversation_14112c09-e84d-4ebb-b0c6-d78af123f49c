package com.contents.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ControlMonitorDisplayControlData
 */
@Validated


public class ControlMonitorDisplayControlData   {
  @JsonProperty("display_type")
  private Integer displayType = null;

  @JsonProperty("display_no")
  private Integer displayNo = null;

  @JsonProperty("display_name")
  private String displayName = null;

  @JsonProperty("display_split_data")
  @Valid
  private List<ControlMonitorDisplaySplitData> displaySplitData = null;

  public ControlMonitorDisplayControlData displayType(Integer displayType) {
    this.displayType = displayType;
    return this;
  }

  /**
   * 表示盤の種類毎に付与される識別番号 0：４面マルチ表示盤、1：単面表示盤、２：指令台ディスプレイ
   * minimum: 0
   * maximum: 2
   * @return displayType
   **/
  @Schema(description = "表示盤の種類毎に付与される識別番号 0：４面マルチ表示盤、1：単面表示盤、２：指令台ディスプレイ")
  
  @Min(0) @Max(2)   public Integer getDisplayType() {
    return displayType;
  }

  public void setDisplayType(Integer displayType) {
    this.displayType = displayType;
  }

  public ControlMonitorDisplayControlData displayNo(Integer displayNo) {
    this.displayNo = displayNo;
    return this;
  }

  /**
   * 表示盤毎に付与される表示盤の識別番号
   * @return displayNo
   **/
  @Schema(description = "表示盤毎に付与される表示盤の識別番号")
  
    public Integer getDisplayNo() {
    return displayNo;
  }

  public void setDisplayNo(Integer displayNo) {
    this.displayNo = displayNo;
  }

  public ControlMonitorDisplayControlData displayName(String displayName) {
    this.displayName = displayName;
    return this;
  }

  /**
   * 表示盤表示盤番号に紐づく名称
   * @return displayName
   **/
  @Schema(description = "表示盤表示盤番号に紐づく名称")
  
    public String getDisplayName() {
    return displayName;
  }

  public void setDisplayName(String displayName) {
    this.displayName = displayName;
  }

  public ControlMonitorDisplayControlData displaySplitData(List<ControlMonitorDisplaySplitData> displaySplitData) {
    this.displaySplitData = displaySplitData;
    return this;
  }

  public ControlMonitorDisplayControlData addDisplaySplitDataItem(ControlMonitorDisplaySplitData displaySplitDataItem) {
    if (this.displaySplitData == null) {
      this.displaySplitData = new ArrayList<ControlMonitorDisplaySplitData>();
    }
    this.displaySplitData.add(displaySplitDataItem);
    return this;
  }

  /**
   * Get displaySplitData
   * @return displaySplitData
   **/
  @Schema(description = "")
      @Valid
    public List<ControlMonitorDisplaySplitData> getDisplaySplitData() {
    return displaySplitData;
  }

  public void setDisplaySplitData(List<ControlMonitorDisplaySplitData> displaySplitData) {
    this.displaySplitData = displaySplitData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControlMonitorDisplayControlData controlMonitorDisplayControlData = (ControlMonitorDisplayControlData) o;
    return Objects.equals(this.displayType, controlMonitorDisplayControlData.displayType) &&
        Objects.equals(this.displayNo, controlMonitorDisplayControlData.displayNo) &&
        Objects.equals(this.displayName, controlMonitorDisplayControlData.displayName) &&
        Objects.equals(this.displaySplitData, controlMonitorDisplayControlData.displaySplitData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayType, displayNo, displayName, displaySplitData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControlMonitorDisplayControlData {\n");
    
    sb.append("    displayType: ").append(toIndentedString(displayType)).append("\n");
    sb.append("    displayNo: ").append(toIndentedString(displayNo)).append("\n");
    sb.append("    displayName: ").append(toIndentedString(displayName)).append("\n");
    sb.append("    displaySplitData: ").append(toIndentedString(displaySplitData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
