package com.contents.devicemodel.videosource;

import java.util.List;

import com.contents.common.db.Control;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.common.external.cross.VideoSourceInfo;
import com.contents.devicemodel.DeviceModel;
import com.contents.devicemodel.ExecutionPlan;

public class VideoSourceDeviceModel extends DeviceModel {

	protected Integer videoSourceNumber; 
	
	protected String videoSourceName; 
	
	protected VideoSourceInfo videoSourceInfo;
	
	@Override
	protected void healthCheck() {
		// 何もしない
	}

	public void bind(VideoSourceInfo videoSourceInfo) {
		this.videoSourceInfo = videoSourceInfo;
		this.deviceNumber = videoSourceInfo.getDeviceNumber();
	    this.productName = videoSourceInfo.getProductName();
	    this.videoSourceNumber = videoSourceInfo.getVideoSourceNumber();
	    this.videoSourceName = videoSourceInfo.getVideoSourceName();
	}
	
	public void expand(RoutingInformation linkInformation) {

	}

	/**
	 * videoSourceNumberを取得します。
	 * @return videoSourceNumber
	 */
	public Integer getVideoSourceNumber() {
	    return videoSourceNumber;
	}

	/**
	 * videoSourceNameを取得します。
	 * @return videoSourceName
	 */
	public String getVideoSourceName() {
	    return videoSourceName;
	}

	@Override
	public List<ExecutionPlan> createExecutionPlan(Control control) {
		// 何もしない
		return null;
	}
}
