package com.contents.devicemodel;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.apache.commons.lang3.StringUtils;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.db.Control;
import com.contents.common.external.cross.NetworkInfo;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.manager.CrossSocketClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 端末抽象クラス 
 */
@Slf4j
public abstract class DeviceModel {

	protected final Lock TRANSMIT_LOCK_OBJECT = new ReentrantLock();
	
	public final Lock SOCKET_LOCK_OBJECT = new ReentrantLock();

	/** 端末番号 */
	protected Integer deviceNumber;

	/** 機器名称 */
	protected String productName;
	
	/** 機器ルーティング情報 */
	protected RoutingInformation routingInformation;

	/** ネットワーク情報 */
	protected NetworkInfo networkInfo;
	
	public String traceAccessInfo() {
		
		try {
			if (this.networkInfo == null)
				return "なし";
			
			String ipAddress = networkInfo.getIpAddress();
			
			if (StringUtils.isEmpty(ipAddress))
				return "なし";
			
			ipAddress = ipAddress.trim();
			
			if (StringUtils.isEmpty(ipAddress))
				return "なし";
			
			String result = ipAddress;
		
			Integer port = networkInfo.getPort();
			
			if (port == null)
				return result;
			
			result = MessageFormatUtil.format("{}:{}", ipAddress, port);
			
			return result;
		} catch (Exception e) {
			log.warn(MessageFormatUtil.format("ProductName: {}, DeviceNumber: {} ", productName, deviceNumber), e);
		}
		
		return "";
	}

	/** 機器接続状態 */
	private boolean deviceConnect = false;

	protected void updateDeviceConnectStatus(boolean status) {
		this.deviceConnect = status;
		this.checkInstant = Instant.now();
	}

	/** デバイスモデル情報 */
	protected DeviceModelInfo deviceModelInfo;

	protected interface ResultMessageValidator<TRetuen, TResult> {

		TRetuen validate(TResult result) throws Exception;
	}

	/** ソケットクライアント */
	protected CrossSocketClient socket;

	protected CrossSocketClient loadSocketClient() throws Exception {
		synchronized (SOCKET_LOCK_OBJECT) {
			try {
				if (this.socket == null) {
					this.socket = new CrossSocketClient(networkInfo.getIpAddress(), networkInfo.getPort(), (status) -> {
						updateDeviceConnectStatus(status);
					});
				}
				return this.socket;
			} catch (Exception e) {
				updateDeviceConnectStatus(false);
				throw e;
			}
		}
	}

	protected Instant checkInstant = null;

	protected Instant lastInstant = Instant.now();

	protected void waitSleep(Integer slepTimer) {

		Instant nowInstant = Instant.now();

		long elapsedTime = Duration.between(lastInstant, nowInstant).toMillis();

		if (slepTimer == null) {
			slepTimer = 0;
		}

		long sleepTime = slepTimer - elapsedTime;

		if (sleepTime <= 0)
			return;

		log.info("★ {} is sleep {} ms", productName, sleepTime);

		try {
			Thread.sleep(sleepTime);
		} catch (InterruptedException e) {
			log.error("An interrupt occurred while waiting for a message to be sent.");
		}
	}

	/**
	 * 接続状態を取得します。
	 * @return true:接続中, false:未接続
	 */
	public boolean isConnect() {
		return this.deviceConnect;
	}

	public void checkDeviceHealth() {

		synchronized (SOCKET_LOCK_OBJECT) {

			if (checkInstant == null) {
				healthCheck();
				return;
			}

			if (!this.deviceConnect) {
				healthCheck();
				return;
			}
		}

		Instant nowInstant = Instant.now();

		long elapsedTime = Duration.between(this.checkInstant, nowInstant).toMillis();

		long sleepTime = (5 * 60 * 1000) - elapsedTime;

		if (sleepTime > 0)
			return;

		healthCheck();
	}

	/**
	 * 定期監視を実行します。
	 */
	protected abstract void healthCheck();
	
	public void bind(RoutingInformation routingInformation) {
		this.routingInformation = routingInformation;
		expand(routingInformation);
	}

	public abstract void expand(RoutingInformation routingInformation);
	
	public boolean validateDevice() {
		boolean ret = true;
		if (networkInfo != null) {
			ret &= CommonUtil. checkIpAddressFormat(networkInfo.getIpAddress());
		}
		return ret;
	}

	public void bind(DeviceModelInfo deviceModelInfo) {
		this.deviceModelInfo = deviceModelInfo;
	}

	public List<ExecutionPlan> createExecutionPlan(List<Control> controlList) {

		List<ExecutionPlan> executionPlanList = new ArrayList<ExecutionPlan>();

		for (Iterator<Control> ite = controlList.iterator(); ite.hasNext();) {

			Control control = ite.next();

			try {
				List<ExecutionPlan> plans = createExecutionPlan(control);

				if (plans != null && plans.iterator().hasNext())
					executionPlanList.addAll(plans);

			} catch (Exception e) {

			}
		}

		return executionPlanList;
	}

	/**
	 * 実行計画を生成します。
	 * */
	protected abstract List<ExecutionPlan> createExecutionPlan(Control control) throws Exception;

	/**
	 * 端末番号を取得します。
	 * @return 端末番号
	 */
	public Integer getDeviceNumber() {
		return deviceNumber;
	}

	/**
	 * 端末番号を設定します。
	 * @param deviceNumber 端末番号
	 */
	public void setDeviceNumber(Integer deviceNumber) {
		this.deviceNumber = deviceNumber;
	}

	/**
	 * 機器名称を取得します。
	 * @return 機器名称
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * 機器名称を設定します。
	 * @param productName 機器名称
	 */
	public void setProductName(String productName) {
		this.productName = productName;
	}
}
