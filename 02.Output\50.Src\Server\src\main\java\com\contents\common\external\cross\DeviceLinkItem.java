package com.contents.common.external.cross;

public class DeviceLinkItem {

	/** コネクター番号 */
	private Integer connectorNumber;
	
	/** デバイス番号 */
	protected Integer deviceNumber;

	/**
	 * コネクター番号を取得します。
	 * @return コネクター番号
	 */
	public Integer getConnectorNumber() {
	    return connectorNumber;
	}

	/**
	 * コネクター番号を設定します。
	 * @param connectorNumber コネクター番号
	 */
	public void setConnectorNumber(Integer connectorNumber) {
	    this.connectorNumber = connectorNumber;
	}

	/**
	 * デバイス番号を取得します。
	 * @return デバイス番号
	 */
	public Integer getDeviceNumber() {
	    return deviceNumber;
	}

	/**
	 * デバイス番号を設定します。
	 * @param deviceNumber デバイス番号
	 */
	public void setDeviceNumber(Integer deviceNumber) {
	    this.deviceNumber = deviceNumber;
	}
}
