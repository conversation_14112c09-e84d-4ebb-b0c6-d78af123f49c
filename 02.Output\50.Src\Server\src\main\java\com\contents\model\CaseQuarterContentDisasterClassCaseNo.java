package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseQuarterContentDisasterClassCaseNo
 */
@Validated


public class CaseQuarterContentDisasterClassCaseNo   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  public CaseQuarterContentDisasterClassCaseNo displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 指令整番
   * @return displayText
   **/
  @Schema(description = "指令整番")
  
  @Size(max=7)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public CaseQuarterContentDisasterClassCaseNo textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseQuarterContentDisasterClassCaseNo caseQuarterContentDisasterClassCaseNo = (CaseQuarterContentDisasterClassCaseNo) o;
    return Objects.equals(this.displayText, caseQuarterContentDisasterClassCaseNo.displayText) &&
        Objects.equals(this.textColor, caseQuarterContentDisasterClassCaseNo.textColor);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseQuarterContentDisasterClassCaseNo {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
