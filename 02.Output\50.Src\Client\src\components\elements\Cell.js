import React from 'react';
import {
  getHexColor,
  getTextOrDisplayColor,
  replaceWithHtmlSpace,
} from '../../utils/Util.js';
import PropTypes from 'prop-types';
import ScaleBlock from './ScaleBlock';

const propTypes = {
  scale: PropTypes.bool,
  unit: PropTypes.string,
  unitStyle: PropTypes.object,
  className: PropTypes.string,
  display_text: PropTypes.string,
  text: PropTypes.string,
  text_color: PropTypes.string,
  background_color: PropTypes.string,
};

/**
 * 色/背景色/文字列の情報を従い、表示する
 * @module Cell
 * @component
 * @param {propTypes} props
 * @returns 表示内容
 */
const Cell = (props) => {
  const style = {};
  if (props.text_color) {
    style.color = getTextOrDisplayColor(props);
  }

  if (props.background_color) {
    style.backgroundColor = getHexColor(props.background_color);
  }

  let className = props.className;
  let refinedText = replaceWithHtmlSpace(props?.display_text || props?.text);

  return (
    <div className={className} style={style}>
      {props.scale && <ScaleBlock {...props}></ScaleBlock>}
      {!props.scale && (
        <span dangerouslySetInnerHTML={{ __html: refinedText }}></span>
      )}
      {
        // Scaleの計算は、Unitの文字を含めてない。画面Layoutによって、このUnitを含めて、ロジックを見直す必要
        props.unit && <span style={props.unitStyle}>{props.unit}</span>
      }
    </div>
  );
};

Cell.propTypes = propTypes;
export default Cell;
