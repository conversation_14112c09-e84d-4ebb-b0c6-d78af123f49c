package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseQuarterContentAwarenessTime
 */
@Validated


public class CaseQuarterContentAwarenessTime   {
  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("display_text")
  private String displayText = null;

  public CaseQuarterContentAwarenessTime textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public CaseQuarterContentAwarenessTime backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public CaseQuarterContentAwarenessTime displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 覚知時刻または指令時刻。※仕様の7文字と違って、1個スペースを増やして、8文字
   * @return displayText
   **/
  @Schema(description = "覚知時刻または指令時刻。※仕様の7文字と違って、1個スペースを増やして、8文字")
  
  @Size(max=8)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseQuarterContentAwarenessTime caseQuarterContentAwarenessTime = (CaseQuarterContentAwarenessTime) o;
    return Objects.equals(this.textColor, caseQuarterContentAwarenessTime.textColor) &&
        Objects.equals(this.backgroundColor, caseQuarterContentAwarenessTime.backgroundColor) &&
        Objects.equals(this.displayText, caseQuarterContentAwarenessTime.displayText);
  }

  @Override
  public int hashCode() {
    return Objects.hash(textColor, backgroundColor, displayText);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseQuarterContentAwarenessTime {\n");
    
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
