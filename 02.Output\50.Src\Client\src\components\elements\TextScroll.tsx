import React, { useRef, useEffect, useState } from 'react';
import './TextScroll.css';
import styled from 'styled-components';

/**
 * 文字列を自動的にスクロールできるコンポーネント
 *
 * @module TextScroll
 * @component
 * @param {propTypes} props
 * @returns 表示内容
 */
function TextScroll(props: {
  content: string;
  color: string;
  background_color: string;
  duration: number;
}) {
  const defaultState = {
    contentWidth: 0,
    boxWidth: 0,
    duration: props.duration,
  };

  const [state, setState] = useState(defaultState);
  const [isScroll, setIsScroll] = useState(false);
  let ref = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    let scroll = false;
    const { offsetWidth, parentElement } = ref.current as HTMLParagraphElement;
    setState({
      contentWidth: offsetWidth,
      boxWidth: parentElement!.offsetWidth,
      duration: Math.ceil((offsetWidth) / parentElement!.offsetWidth * 10000), //Durationは、実際のContent長さと外側の箱の横幅の倍数の2倍
    });

    if (parentElement && offsetWidth > parentElement!.offsetWidth) {
      scroll = true;
    }
    setIsScroll(scroll);
  }, [props.content]);

  if (isScroll) {
    const animationName = `marquee_${state.contentWidth}`;
    return (
      <div className="marquee_box">
        <Text
          ref={ref}
          animationName={animationName}
          duration={state.duration}
          contentWidth={state.contentWidth}
          boxWidth={state.boxWidth}
          color={props.color}
          background_color={props.background_color}
        >
          {props.content}
        </Text>
      </div>
    );
  } else {
    return (
      <div className="marquee_box">
        <StaticText
          ref={ref}
          color={props.color}
          background_color={props.background_color}
        >
          {props.content}
        </StaticText>
      </div>
    );
  }
}

interface pProps {
  readonly animationName: string;
  readonly duration: number;
  readonly contentWidth: number;
  readonly boxWidth: number;
  readonly color: string;
  readonly background_color: string;
}

const Text = styled.p<pProps>`
  color: ${(props) => props.color};
  background-color: ${(props) => props.background_color};
  position: relative;
  will-change: transform;
  animation: ${(props) => props.animationName} ${(props) => props.duration}ms
    linear infinite running both normal;

  @keyframes ${(props) => props.animationName} {
    0%,5% {
      transform: translateX(0px);
    }
    100% {
      transform: translateX(
        -${(props) => props.contentWidth}px
      );
    }
  }
`;

interface pStaticProps {
  readonly color: string;
  readonly background_color: string;
}

const StaticText = styled.p<pStaticProps>`
  position: relative;
  color: ${(props) => props.color};
  background-color: ${(props) => props.background_color};
`;

TextScroll.defaultProps = {
  content: '',
  duration: 3,
};

export default TextScroll;
