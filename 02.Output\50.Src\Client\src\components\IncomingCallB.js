import React from 'react';
import Cell from './elements/Cell';
import { getCellFace, isValidSource } from '../utils/Util.js';
import BlinkBlock from './elements/BlinkBlock';

/**
 * 着信状況コンテンツB<br>
 * propsは、「3.9着信状況コンテンツB情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module IncomingCallB
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const IncomingCallB = (props) => {
  const MAX_ROW = 32;
  return (
    <div className="text-4xl leading-[1]">
      {isValidSource(props) && (
        <div className="grid grid-cols-2 grid-rows-16 grid-flow-col gap-[0.1rem] gap-x-[1rem] auto-cols-fr mt-5">
          {props.line_name.map((item, index) => {
            //最大32個まで表示する
            if (index >= MAX_ROW) return undefined;

            return <IncomingCallBRow key={index} {...item} />;
          })}
        </div>
      )}
    </div>
  );
};

const IncomingCallBRow = (props) => {
  let cell1Props = getCellFace(props, 'col-span-4 w-fit');
  let spaceArray;
  const callTag = '■';
  const MAX_CALL = 6;

  if (!props.incoming_call) {
    spaceArray = getSpaceArray(MAX_CALL);
  } else if (props.incoming_call.length < MAX_CALL) {
    spaceArray = getSpaceArray(MAX_CALL - props.incoming_call.length);
  }

  return (
    <div className="border-transparent border-x-[1rem] grid grid-cols-14 grid-rows-1 auto-cols-fr">
      <Cell {...cell1Props} />
      <div className="col-start-9 col-span-6">
        <div className="flex flex-row">
          {props.incoming_call &&
            props.incoming_call.map((item, index) => {
              //最大6個まで表示する
              if (index >= MAX_CALL) {
                return undefined;
              }

              let blinkItem = { ...item };
              blinkItem.display_text = callTag;

              const showBlock = [{ showInfo: blinkItem }];

              return (
                <BlinkBlock
                  key={index}
                  block={showBlock}
                  index={index}
                  blink_setting={item.blink_setting}
                />
              );
            })}
          {spaceArray &&
            spaceArray.map((item, index) => {
              return (
                <div key={index} className="text-[#404040]">
                  {item}
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

function getSpaceArray(count) {
  const callTag = '■';

  let resultArray = [];
  for (let i = 0; i < count; i++) {
    resultArray.push(callTag);
  }
  return resultArray;
}

export default IncomingCallB;
