import React, { useRef, useState, useEffect } from 'react';
import Cell from './Cell';
import { useDeepCompareEffect } from 'ahooks';
import PropTypes from 'prop-types';

const propTypes = {
  max: PropTypes.number,
  change_setting: PropTypes.object,
  display_data: PropTypes.array,
  gridLevelProps: PropTypes.string,
  cellLevelProps: PropTypes.array,
};

/**
 * 切替設定に従い、配列をスクロールして表示する <br>
 * ただ、切替設定/切替サイズが0の場合は切り替えを行わない。
 * @module ArrayScroll
 * @component
 * @param {propTypes} props
 * @returns 表示内容
 */
const ArrayScroll = (props) => {
  const maxNum = props?.max;
  //最大のChangeSizeを表示箱に超えないようにガイド
  if (props?.change_setting?.change_size > maxNum) {
    props.change_setting.change_size = maxNum;
  }

  //最初の表示は、最大サイズ(props.max)で配列を初期化する
  let defaultSubArray = props?.display_data;
  if (maxNum) {
    defaultSubArray = props?.display_data?.slice(0, maxNum);
    
    defaultSubArray = fillArrayWithDummy(defaultSubArray, maxNum);
  }

  const [showInfo, setShowInfo] = useState({
    subArray: defaultSubArray,
    changeSize: 0,
  });
  // RefでsetIntervalの各Intervalで、表示配列を共有する
  const showInfoRef = useRef(showInfo);

  useDeepCompareEffect(() => {
    let id = null;

    // change_time/change_sizeが0の場合は切り替えを行わない
    if (canScroll(props)) {
      id = setInterval(() => {
        let nextSize;
        if (
          showInfoRef.current.changeSize + maxNum >=
          props?.display_data?.length
        ) {
          nextSize = 0;
        } else {
          nextSize =
            showInfoRef.current.changeSize + props?.change_setting?.change_size;
        }

        //次の表示サイズは、最大の表示サイズMaxNumより、少ない場合、MaxNumを表示する
        if (nextSize + maxNum >= props?.display_data?.length) { 
          nextSize = props?.display_data?.length - maxNum;
        }

        const newState = {
          changeSize: nextSize,
          subArray: props?.display_data?.slice(nextSize, nextSize + maxNum),
        };
        setShowInfo(newState);
        showInfoRef.current = newState;
      }, props?.change_setting?.change_time * 1000);
    } else if (props?.display_data) {
        const newState = {
          changeSize: 0,
          subArray: props?.display_data?.slice(0, 0 + maxNum),
        };
        newState.subArray = fillArrayWithDummy(newState.subArray, maxNum);

        setShowInfo(newState);
        showInfoRef.current = newState;
    }

    if (id) {
      return () => clearInterval(id);
    }
  }, [props?.change_setting, props?.display_data]);

  if (!props) {
    console.error('ArrayScroll: props is null.');
    return;
  }

  return (
    <div className={`grid ${props?.gridLevelProps}`}>
      {showInfo.subArray?.map((item, index) => {
        if (Array.isArray(item)) {
          return (
            <span> 配列表示をサポートしません </span>
            // <ArrayRow key={index} items={item} cellLevelProps={props?.cellLevelProps} />
          );
        } else {
          const cellPropsIndex = index % props?.cellLevelProps?.length;
          let row4Cell = {
            text_color: item.text_color,
            text: item.display_text,
            background_color: item.background_color,
            className: props?.cellLevelProps[cellPropsIndex],
          };

          return (
            <Cell
              key={index}
              {...row4Cell}
            />
          );
        }
      })}
    </div>
  );
};

// 表示箱より少ない場合、Dummyデータを作成
function fillArrayWithDummy(defaultSubArray, maxNum) {
  if (defaultSubArray.length < maxNum) {
    const dummyCounter = maxNum - defaultSubArray.length;
    for (let i = 0; i < dummyCounter; i++) {
      defaultSubArray.push({ display_text: '　' });
    }
  }
  return defaultSubArray;
}

/**
 * change_size/change_timeが有効な値があれば、Scrollすると返します。
 * @param {*} obj
 * @returns true: Scroll. false: Scrollしない
 */
function canScroll(obj) {
  if (!obj) return false;

  return (
    obj?.display_data &&
    obj?.display_data.length > obj?.max &&
    obj?.change_setting &&
    obj?.change_setting?.change_time > 0 &&
    obj?.change_setting?.change_size > 0
  );
}

ArrayScroll.propTypes = propTypes;

export default ArrayScroll;
