{"name": "contentapp", "homepage": "/contentapp", "version": "1.0.7", "private": true, "dependencies": {"ahooks": "^3.7.1", "axios": "^1.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^3.1.4", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-stomp-hooks": "^2.1.0", "react-uuid": "^1.0.3", "styled-components": "^5", "tailwindcss": "^3.0.2", "use-deep-compare-effect": "^1.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "war": "node scripts/release.js", "docs": "jsdoc -c jsdoc.conf.json", "release": "npm run build && npm run war"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/styled-components": "^5.1.26", "archiver": "^7.0.1", "better-docs-own": "^2.7.3", "docdash": "^1.2.0", "jsdoc": "^3.6.11", "postcss": "^8.4.14", "tailwindcss": "^3.1.8", "typescript": "3.7.5"}}