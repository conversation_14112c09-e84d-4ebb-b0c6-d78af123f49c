package com.contents.model;

import java.util.Objects;
import com.contents.model.CaseHalfContentCarName;
import com.contents.model.CaseQuarterContentAwarenessTime;
import com.contents.model.CaseQuarterContentDisasterClass;
import com.contents.model.CaseQuarterContentTownName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseHalfContentCase
 */
@Validated


public class CaseHalfContentCase   {
  @JsonProperty("disaster_class")
  private CaseQuarterContentDisasterClass disasterClass = null;

  @JsonProperty("town_name")
  private CaseQuarterContentTownName townName = null;

  @JsonProperty("awareness_time")
  private CaseQuarterContentAwarenessTime awarenessTime = null;

  @JsonProperty("car_name")
  private CaseHalfContentCarName carName = null;

  public CaseHalfContentCase disasterClass(CaseQuarterContentDisasterClass disasterClass) {
    this.disasterClass = disasterClass;
    return this;
  }

  /**
   * Get disasterClass
   * @return disasterClass
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentDisasterClass getDisasterClass() {
    return disasterClass;
  }

  public void setDisasterClass(CaseQuarterContentDisasterClass disasterClass) {
    this.disasterClass = disasterClass;
  }

  public CaseHalfContentCase townName(CaseQuarterContentTownName townName) {
    this.townName = townName;
    return this;
  }

  /**
   * Get townName
   * @return townName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentTownName getTownName() {
    return townName;
  }

  public void setTownName(CaseQuarterContentTownName townName) {
    this.townName = townName;
  }

  public CaseHalfContentCase awarenessTime(CaseQuarterContentAwarenessTime awarenessTime) {
    this.awarenessTime = awarenessTime;
    return this;
  }

  /**
   * Get awarenessTime
   * @return awarenessTime
   **/
  @Schema(description = "")
  
    @Valid
    public CaseQuarterContentAwarenessTime getAwarenessTime() {
    return awarenessTime;
  }

  public void setAwarenessTime(CaseQuarterContentAwarenessTime awarenessTime) {
    this.awarenessTime = awarenessTime;
  }

  public CaseHalfContentCase carName(CaseHalfContentCarName carName) {
    this.carName = carName;
    return this;
  }

  /**
   * Get carName
   * @return carName
   **/
  @Schema(description = "")
  
    @Valid
    public CaseHalfContentCarName getCarName() {
    return carName;
  }

  public void setCarName(CaseHalfContentCarName carName) {
    this.carName = carName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseHalfContentCase caseHalfContentCase = (CaseHalfContentCase) o;
    return Objects.equals(this.disasterClass, caseHalfContentCase.disasterClass) &&
        Objects.equals(this.townName, caseHalfContentCase.townName) &&
        Objects.equals(this.awarenessTime, caseHalfContentCase.awarenessTime) &&
        Objects.equals(this.carName, caseHalfContentCase.carName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(disasterClass, townName, awarenessTime, carName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseHalfContentCase {\n");
    
    sb.append("    disasterClass: ").append(toIndentedString(disasterClass)).append("\n");
    sb.append("    townName: ").append(toIndentedString(townName)).append("\n");
    sb.append("    awarenessTime: ").append(toIndentedString(awarenessTime)).append("\n");
    sb.append("    carName: ").append(toIndentedString(carName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
