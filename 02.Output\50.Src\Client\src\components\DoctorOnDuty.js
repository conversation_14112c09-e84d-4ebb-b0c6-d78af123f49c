import React from 'react';
import Title from './elements/Title';
import TextScroll from './elements/TextScroll';
import Cell from './elements/Cell';
import CellBox from './elements/CellBox';
import { getCellFace, isValidSource } from '../utils/Util.js';

/**
 * 当番医コンテンツ<br>
 * propsは、「3.14当番医コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module DoctorOnDuty
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const DoctorOnDuty = (props) => {
  const MAX_ROW = 6;
  return (
    <div className="text-8xl">
      <Title title={'当番病院'} />
      {isValidSource(props) && (
        <div className="border-transparent border-x-[1rem] grid grid-cols-[repeat(2,8rem)_minmax(0.25rem,1fr)_repeat(5,8rem)_minmax(0.25rem,1fr)_repeat(7,7rem)] leading-[1]  gap-y-[2.3rem] mt-[1rem] items-end">
          {props.medical_subject.map((item, index) => {
            if (index >= MAX_ROW) return undefined;

            return <DoctorOnDutyRow key={index} item={item} />;
          })}
        </div>
      )}
    </div>
  );
};

const DoctorOnDutyRow = (props) => {
  const rowData = props?.item;
  if (!rowData) return;

  let cell1Props = getCellFace(rowData, 'col-span-2');

  // 結合するかをチェック
  if (
    rowData.medical_institution_name?.length > 1 ||
    rowData.medical_institution_telephone_no?.length > 1
  ) {
    let showMsg = '';

    // まず医療期間で表示文字列を作る
    rowData.medical_institution_name?.forEach((item, index) => {
      if (
        rowData.medical_institution_telephone_no[index] &&
        rowData.medical_institution_telephone_no[index].display_text
      ) {
        showMsg = `${showMsg} ${item.display_text} ${rowData.medical_institution_telephone_no[index].display_text}`;
      } else {
        showMsg = `${showMsg} ${item.display_text}`;
      }
    });

    // 残る電話番号で表示文字列を作る
    if (
      rowData.medical_institution_name?.length <
      rowData.medical_institution_telephone_no?.length
    ) {
      for (
        let index = rowData.medical_institution_name?.length || 0;
        index < rowData.medical_institution_telephone_no?.length;
        index++
      ) {
        const teleNo = rowData.medical_institution_telephone_no[index];
        if (teleNo.display_text) {
          showMsg = `${showMsg} ${teleNo.display_text}`;
        }
      }
    }

    // 結合した文字列の表示Styleは、1個目の電話番号のStyleを使う。なければ、1個目の医療機関のStyleを使う
    let cell2Props;
    if (
      rowData.medical_institution_telephone_no &&
      rowData.medical_institution_telephone_no[0]
    ) {
      cell2Props = getCellFace(
        rowData.medical_institution_telephone_no[0],
        'col-span-13 col-start-4 text-7xl'
      );
    } else {
      cell2Props = getCellFace(
        rowData.medical_institution_name[0],
        'col-span-13 col-start-4 text-7xl'
      );
    }

    cell2Props.text = showMsg;

    let cell2PropFrame = {
      className: cell2Props.className,
      text_color: cell2Props.text_color,
    };

    return (
      <>
        <Cell {...cell1Props} />
        <CellBox {...cell2PropFrame}>
          <TextScroll
            content={cell2Props.text}
            background_color={cell2Props.background_color}
          />
        </CellBox>
      </>
    );
  } else {
    // 医療機関と電話番号をそれぞれ表示
    let cell2Props = { className: 'col-span-5 col-start-4' };
    let cell3Props = { className: 'col-span-7 col-start-10' };

    if (rowData.medical_institution_name?.length === 1) {
      cell2Props = getCellFace(
        rowData.medical_institution_name[0],
        'col-span-5 col-start-4'
      );
    }

    if (rowData.medical_institution_telephone_no?.length === 1) {
      cell3Props = getCellFace(
        rowData.medical_institution_telephone_no[0],
        'col-span-7 col-start-10 text-7xl w-fit'
      );
    }

    let cell2PropFrame = {
      className: cell2Props.className,
      text_color: cell2Props.text_color,
    };

    return (
      <>
        <Cell {...cell1Props} />
        <CellBox {...cell2PropFrame}>
          <TextScroll
            content={cell2Props.text}
            background_color={cell2Props.background_color}
          />
        </CellBox>
        <Cell {...cell3Props} />
      </>
    );
  }
};

export default DoctorOnDuty;
