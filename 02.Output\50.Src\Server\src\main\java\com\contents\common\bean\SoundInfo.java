package com.contents.common.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表示盤喚起音吹鳴情報
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SoundInfo {
    /**
     * 再生ファイル番号
     */
    private Integer soundNo;
    /**
     * リピート回数
     */
    private Integer repeatCount;
    /**
     * 実際のContent内容
     */
    private Object sourceData;
    /**
     * 該当コンテンツのID
     */
    private String id;

    public SoundInfo(Integer soundNo, Integer repeatCount) {
        this.soundNo = soundNo;
        this.repeatCount = repeatCount;
    }
}
