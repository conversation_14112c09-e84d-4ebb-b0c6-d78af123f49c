package com.contents.common.external.cross;

import java.util.List;


/**
 * 外部機器情報
 */
public class ExternalDeviceInformation {

	/** ビデオソースリスト */
	private List<VideoSourceInfo> videoSourceList;

	/**
	 * ビデオソースリストを取得します。
	 * @return ビデオソースリスト
	 */
	public List<VideoSourceInfo> getVideoSourceList() {
	    return videoSourceList;
	}

	/**
	 * ビデオソースリストを設定します。
	 * @param videoSourceList ビデオソースリスト
	 */
	public void setVideoSourceList(List<VideoSourceInfo> videoSourceList) {
	    this.videoSourceList = videoSourceList;
	}
}
