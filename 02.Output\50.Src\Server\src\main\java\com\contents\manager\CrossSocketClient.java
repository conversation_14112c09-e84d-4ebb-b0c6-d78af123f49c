package com.contents.manager;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.time.Instant;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import javax.xml.bind.DatatypeConverter;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrossSocketClient implements AutoCloseable {

	private final Lock LOCK_OBJECT = new ReentrantLock(); 
	
	private final int RECEIVE_BUFFER_SIZE = 256;
	
	private Instant lastInstant = Instant.now();

	private String ipAddress;
	private Integer port;
	private Socket socket;
	private OutputStream out;
	private InputStream in;
	
	private final ConnectStatusCallback connectStatusCallback;

	public CrossSocketClient(String ipAddress, Integer port, ConnectStatusCallback callback) {
		this.ipAddress = ipAddress;
		this.port = port;
		this.connectStatusCallback = callback;
	}
	
	public boolean isSocket() {
		return socket != null;
	}
	
	public interface ConnectCallback {
		
		void callback(byte[] response) throws Exception;
	}
	
	public interface ConnectStatusCallback {
		
		void callback(boolean status);
	}
	
	
	/**
	 * lastInstantを取得します。
	 * @return lastInstant
	 */
	public Instant getLastInstant() {
	    return lastInstant;
	}
	
	/**
	 *  ソケットを接続します。
	 * @throws Exception 
	 * */
	public void connect(Integer connectionTimeout, Integer receiveTimeout) throws Exception {
		
		connect(connectionTimeout, receiveTimeout, null);
	}
	
	/**
	 *  ソケットを接続します。
	 * @throws java.net.SocketTimeoutException, IOException 
	 * @throws Exception 
	 * */
	public void connect(Integer connectionTimeout, Integer receiveTimeout, ConnectCallback deviceCallback) throws java.net.SocketTimeoutException, IOException {

		try {
			// クローズ漏れの保険
			this.close();
		} catch (Exception e) {
			log.warn("connect init close error", e);
		}
		
		try {
			InetSocketAddress endpoint= new InetSocketAddress(ipAddress, port);
			socket = new Socket();
			socket.connect(endpoint, connectionTimeout);
			socket.setSoTimeout(receiveTimeout);
			out = socket.getOutputStream();
			in = socket.getInputStream();
			
			lastInstant = Instant.now();
			
			connectStatusCallback.callback(true);
			
			if (deviceCallback == null)
				return;
			
			try {
				// 受信処理
				byte[] readBuff = new byte[RECEIVE_BUFFER_SIZE];
				int inputLen = in.read(readBuff, 0, RECEIVE_BUFFER_SIZE);

				// 受信データの格納
				byte[] recvData = new byte[inputLen];
				System.arraycopy(readBuff, 0, recvData, 0, inputLen);
				
				deviceCallback.callback(recvData);
				
			} catch (java.net.SocketTimeoutException e) {
				
				log.warn("non connect input stream", e);
				
			} catch (Exception e) {
				log.warn("non connect input stream", e);
			}

			return;
			
		} catch (java.nio.channels.IllegalBlockingModeException e) {
			
			log.warn("socket connect error. ", e);
			log.warn("ip={}, port={} connectionTimeout={}, receiveTimeout={}", ipAddress, port, connectionTimeout, receiveTimeout);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
			
		} catch (SocketException e) {
			
			log.warn("socket connect error. ", e);
			log.warn("ip={}, port={} connectionTimeout={}, receiveTimeout={}", ipAddress, port, connectionTimeout, receiveTimeout);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
			
		} catch (java.net.SocketTimeoutException e) {
			
			log.warn("socket connect error. ", e);
			log.warn("ip={}, port={} connectionTimeout={}, receiveTimeout={}", ipAddress, port, connectionTimeout, receiveTimeout);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
			
		} catch (IOException e) {
			
			log.warn("socket connect error. ", e);
			log.warn("ip={}, port={} connectionTimeout={}, receiveTimeout={}", ipAddress, port, connectionTimeout, receiveTimeout);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
		}
	}
	/** 
	 * バイナリデータを送信します。
	 *  */
	public byte[] request(byte[] data) throws Exception {
		
		return send(data, true);
	}
	
	/** 
	 * バイナリデータを送信します。
	 *  */
	public void send(byte[] data) throws Exception {
		
		send(data, false);
	}

	/** 
	 * バイナリデータを送信します。
	 *  */
	private byte[] send(byte[] data, boolean isReturn) throws Exception {
	
		try {
			// データを送信
			out.write(data);
			if (!isReturn)
				return null;
			
			log.info("ip={}, port={}", ipAddress, port);
			log.info("SendData: {}", DatatypeConverter.printHexBinary(data));

			// 応答を受信
			byte[] readBuff = new byte[RECEIVE_BUFFER_SIZE];
			int inputLen = in.read(readBuff, 0, RECEIVE_BUFFER_SIZE);

			// 受信データの格納
			byte[] recvData = new byte[inputLen];
			System.arraycopy(readBuff, 0, recvData, 0, inputLen);

			log.info("RecvData: {}", DatatypeConverter.printHexBinary(recvData));
			
			return recvData;
			
		} catch (SocketTimeoutException e) {
			
			log.error("CrossSocketClient error.", e);
			log.error("ipAddress={}, port={}", ipAddress, port);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
			
		} catch (SocketException e) {

			log.error("CrossSocketClient error.", e);
			log.error("ipAddress={}, port={}", ipAddress, port);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
			
		} catch (Exception e) {
			log.error("CrossSocketClient error.", e);
			log.error("ipAddress={}, port={}", ipAddress, port);
			this.close();
			connectStatusCallback.callback(false);
			throw e;
		}
	}

	/**
	 *  ソケットをクローズします。
	 * */
	public void close() {
		try {
			if (in != null) {
				in.close();
			}
			if (out != null) {
				out.close();
			}
			if (socket != null) {
				socket.close();
				socket = null;
			}
		} catch (IOException e) {
			log.warn("close error", e);
		}
	}

	
}
