package com.contents.common.external.cross;

public class DisplaySplitInfo {

	/** 表示盤分割面番号 */
	private Integer displaySplitNumber;
	
	/** デバイス番号 */
	private  Integer deviceNumber;

	/**
	 * 表示盤分割面番号を取得します。
	 * @return 表示盤分割面番号
	 */
	public Integer getDisplaySplitNumber() {
	    return displaySplitNumber;
	}

	/**
	 * 表示盤分割面番号を設定します。
	 * @param displaySplitNumber 表示盤分割面番号
	 */
	public void setDisplaySplitNumber(Integer displaySplitNumber) {
	    this.displaySplitNumber = displaySplitNumber;
	}

	/**
	 * デバイス番号を取得します。
	 * @return デバイス番号
	 */
	public Integer getDeviceNumber() {
	    return deviceNumber;
	}

	/**
	 * デバイス番号を設定します。
	 * @param deviceNumber デバイス番号
	 */
	public void setDeviceNumber(Integer deviceNumber) {
	    this.deviceNumber = deviceNumber;
	} 
}
