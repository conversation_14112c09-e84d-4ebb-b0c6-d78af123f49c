#############環境に合わせて設定を変更する必要(<PERSON><PERSON>に配置する前提)##############
#PostgreのDB設定。DBサーバの設定 必ず環境に合わせて設定する
spring.datasource.url=*******************************************
spring.datasource.username=postgres
spring.datasource.password=admin

#DBのAPI履歴の最大保存期間(月単位)。これ以上を超えると、自動的に削除される
api.history.max.keep.month=60

# APIのタイムアウト時間(ms単位)。10000の場合、10秒
api.timeout=10000

#############あまり変更する必要がないデフォルト設定##############
# Log
logging.file.name=ContentServer.log
logging.level.root=DEBUG

# 外部構成のJSONファイル名 (resourceフォルダ内のファイル)
app.configuration.external.filename=external_configuration.json

# 機器制御有効化設定。 true: 機器制御有効, false: 機器制御無効(デバッグ用)
app.configuration.external.machine-control-enable=false