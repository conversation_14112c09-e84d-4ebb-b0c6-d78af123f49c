package com.contents.model;

import java.util.Objects;
import com.contents.model.BlinkSetting;
import com.contents.model.DeploymentContentCarName;
import com.contents.model.VehicleContentAvmDynamicState;
import com.contents.model.VehicleContentDeployment;
import com.contents.model.VehicleContentDisasterType;
import com.contents.model.VehicleContentTownName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * VehicleContentTitleName
 */
@Validated


public class VehicleContentTitleName   {
  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("deployment")
  @Valid
  private List<VehicleContentDeployment> deployment = null;

  @JsonProperty("car_name")
  @Valid
  private List<DeploymentContentCarName> carName = null;

  @JsonProperty("town_name")
  @Valid
  private List<VehicleContentTownName> townName = null;

  @JsonProperty("disaster_type")
  @Valid
  private List<VehicleContentDisasterType> disasterType = null;

  @JsonProperty("avm_dynamic_state")
  @Valid
  private List<VehicleContentAvmDynamicState> avmDynamicState = null;

  @JsonProperty("lighting_setting")
  @Valid
  private List<BlinkSetting> lightingSetting = null;

  public VehicleContentTitleName textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public VehicleContentTitleName backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public VehicleContentTitleName displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 署所(部隊)名または車両種別
   * @return displayText
   **/
  @Schema(description = "署所(部隊)名または車両種別")
  
  @Size(max=15)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public VehicleContentTitleName deployment(List<VehicleContentDeployment> deployment) {
    this.deployment = deployment;
    return this;
  }

  public VehicleContentTitleName addDeploymentItem(VehicleContentDeployment deploymentItem) {
    if (this.deployment == null) {
      this.deployment = new ArrayList<VehicleContentDeployment>();
    }
    this.deployment.add(deploymentItem);
    return this;
  }

  /**
   * Get deployment
   * @return deployment
   **/
  @Schema(description = "")
      @Valid
    public List<VehicleContentDeployment> getDeployment() {
    return deployment;
  }

  public void setDeployment(List<VehicleContentDeployment> deployment) {
    this.deployment = deployment;
  }

  public VehicleContentTitleName carName(List<DeploymentContentCarName> carName) {
    this.carName = carName;
    return this;
  }

  public VehicleContentTitleName addCarNameItem(DeploymentContentCarName carNameItem) {
    if (this.carName == null) {
      this.carName = new ArrayList<DeploymentContentCarName>();
    }
    this.carName.add(carNameItem);
    return this;
  }

  /**
   * Get carName
   * @return carName
   **/
  @Schema(description = "")
      @Valid
    public List<DeploymentContentCarName> getCarName() {
    return carName;
  }

  public void setCarName(List<DeploymentContentCarName> carName) {
    this.carName = carName;
  }

  public VehicleContentTitleName townName(List<VehicleContentTownName> townName) {
    this.townName = townName;
    return this;
  }

  public VehicleContentTitleName addTownNameItem(VehicleContentTownName townNameItem) {
    if (this.townName == null) {
      this.townName = new ArrayList<VehicleContentTownName>();
    }
    this.townName.add(townNameItem);
    return this;
  }

  /**
   * Get townName
   * @return townName
   **/
  @Schema(description = "")
      @Valid
    public List<VehicleContentTownName> getTownName() {
    return townName;
  }

  public void setTownName(List<VehicleContentTownName> townName) {
    this.townName = townName;
  }

  public VehicleContentTitleName disasterType(List<VehicleContentDisasterType> disasterType) {
    this.disasterType = disasterType;
    return this;
  }

  public VehicleContentTitleName addDisasterTypeItem(VehicleContentDisasterType disasterTypeItem) {
    if (this.disasterType == null) {
      this.disasterType = new ArrayList<VehicleContentDisasterType>();
    }
    this.disasterType.add(disasterTypeItem);
    return this;
  }

  /**
   * Get disasterType
   * @return disasterType
   **/
  @Schema(description = "")
      @Valid
    public List<VehicleContentDisasterType> getDisasterType() {
    return disasterType;
  }

  public void setDisasterType(List<VehicleContentDisasterType> disasterType) {
    this.disasterType = disasterType;
  }

  public VehicleContentTitleName avmDynamicState(List<VehicleContentAvmDynamicState> avmDynamicState) {
    this.avmDynamicState = avmDynamicState;
    return this;
  }

  public VehicleContentTitleName addAvmDynamicStateItem(VehicleContentAvmDynamicState avmDynamicStateItem) {
    if (this.avmDynamicState == null) {
      this.avmDynamicState = new ArrayList<VehicleContentAvmDynamicState>();
    }
    this.avmDynamicState.add(avmDynamicStateItem);
    return this;
  }

  /**
   * Get avmDynamicState
   * @return avmDynamicState
   **/
  @Schema(description = "")
      @Valid
    public List<VehicleContentAvmDynamicState> getAvmDynamicState() {
    return avmDynamicState;
  }

  public void setAvmDynamicState(List<VehicleContentAvmDynamicState> avmDynamicState) {
    this.avmDynamicState = avmDynamicState;
  }

  public VehicleContentTitleName lightingSetting(List<BlinkSetting> lightingSetting) {
    this.lightingSetting = lightingSetting;
    return this;
  }

  public VehicleContentTitleName addLightingSettingItem(BlinkSetting lightingSettingItem) {
    if (this.lightingSetting == null) {
      this.lightingSetting = new ArrayList<BlinkSetting>();
    }
    this.lightingSetting.add(lightingSettingItem);
    return this;
  }

  /**
   * Get lightingSetting
   * @return lightingSetting
   **/
  @Schema(description = "")
      @Valid
    public List<BlinkSetting> getLightingSetting() {
    return lightingSetting;
  }

  public void setLightingSetting(List<BlinkSetting> lightingSetting) {
    this.lightingSetting = lightingSetting;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VehicleContentTitleName vehicleContentTitleName = (VehicleContentTitleName) o;
    return Objects.equals(this.textColor, vehicleContentTitleName.textColor) &&
        Objects.equals(this.backgroundColor, vehicleContentTitleName.backgroundColor) &&
        Objects.equals(this.displayText, vehicleContentTitleName.displayText) &&
        Objects.equals(this.deployment, vehicleContentTitleName.deployment) &&
        Objects.equals(this.carName, vehicleContentTitleName.carName) &&
        Objects.equals(this.townName, vehicleContentTitleName.townName) &&
        Objects.equals(this.disasterType, vehicleContentTitleName.disasterType) &&
        Objects.equals(this.avmDynamicState, vehicleContentTitleName.avmDynamicState) &&
        Objects.equals(this.lightingSetting, vehicleContentTitleName.lightingSetting);
  }

  @Override
  public int hashCode() {
    return Objects.hash(textColor, backgroundColor, displayText, deployment, carName, townName, disasterType, avmDynamicState, lightingSetting);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VehicleContentTitleName {\n");
    
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    deployment: ").append(toIndentedString(deployment)).append("\n");
    sb.append("    carName: ").append(toIndentedString(carName)).append("\n");
    sb.append("    townName: ").append(toIndentedString(townName)).append("\n");
    sb.append("    disasterType: ").append(toIndentedString(disasterType)).append("\n");
    sb.append("    avmDynamicState: ").append(toIndentedString(avmDynamicState)).append("\n");
    sb.append("    lightingSetting: ").append(toIndentedString(lightingSetting)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
