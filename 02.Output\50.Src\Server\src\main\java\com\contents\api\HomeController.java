package com.contents.api;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.contents.common.bean.ClientError;
import com.contents.common.bean.WSResult;
import com.contents.manager.TaskQueue;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 指令系AP向けのAPI以外、ContentAP内部用のAPIは、ここで定義する
 */
@Slf4j
@RestController
public class HomeController {

    private final TaskQueue taskQueue;

    @Autowired
    public HomeController(ObjectMapper objectMapper, TaskQueue taskQueue, HttpServletRequest request) {
        this.taskQueue = taskQueue;
    }

    /**
     * WebSocketで受信したメッセージに対して、受信済みであることをサーバーに送る
     * @param requestMessage WebSocket Msg
     */
    @MessageMapping("/wsresult")
    public void webSocketReceiver(WSResult requestMessage) {
    	try {
    		log.info("☆☆ wsresult start webSocketReceiver. msg: {}", requestMessage);
            synchronized (taskQueue) {
                taskQueue.subTaskDone(requestMessage.getId());
            }
            log.info("☆☆ wsresult end webSocketReceiver. msg: {}", requestMessage);
		} catch (Exception e) {
			log.error("☆☆ wsresult error", e);
		}
        
    }

    /**
     * BrowserのReactJSで発生したExceptionのエラー情報をこのAPIでサーバーに送る
     * @param error Error情報
     */
    @PostMapping(value = "/saveFrontError")
    @ResponseBody
    public void saveFrontError(@RequestBody ClientError error) {
        log.error("saveFrontError. {}", error);
    }
}
