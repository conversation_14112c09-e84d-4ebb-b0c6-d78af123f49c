/**
 * NOTE: This class is auto generated by the swagger code generator program (3.0.36).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.contents.api;

import com.contents.model.ApiResult;
import com.contents.model.ControlSound;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.async.DeferredResult;

import javax.validation.Valid;

@Validated
public interface ControlSoundApi {

    @Operation(summary = "表示盤喚起音吹鳴", description = "", tags={ "制御系API" })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = "200", description = "APIデータをJson形式で正しく受信できた", content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApiResult.class))) })
    @RequestMapping(value = "/control_sound",
        produces = { "application/json" }, 
        consumes = { "application/json" }, 
        method = RequestMethod.POST)
    DeferredResult<ResponseEntity<ApiResult>> controlSoundPost(@Parameter(in = ParameterIn.DEFAULT, description = "", required=true, schema=@Schema()) @Valid @RequestBody ControlSound body);

}

