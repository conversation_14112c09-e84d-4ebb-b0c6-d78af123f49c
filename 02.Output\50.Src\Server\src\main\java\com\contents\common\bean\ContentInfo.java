package com.contents.common.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ブラウザに送るコンテンツ
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContentInfo {
    /**
     * ソース番号
     */
    private Integer sourceNo;
    /**
     * ソースの分割番号
     */
    private Integer sourceSplitNo;
    /**
     * 面分割番号
     */
    private Integer detailSplitNo;
    /**
     * コンテンツタイトル(SourceName)
     */
    private String sourceName;
    /**
     * 実際のContent内容
     */
    private Object sourceData;
    /**
     * 表示盤表示位置
     */
    private Integer viewNumber;
    
    /**
     * ソース表示パターン
     */
    private Integer sourceDispPattern;
    /**
     * 
     * 該当コンテンツのID
     */
    private String id;

    public ContentInfo(Integer sourceNo, Integer sourceSplitNo, Integer detailSplitNo) {
        this.sourceNo = sourceNo;
        this.sourceSplitNo = sourceSplitNo;
        this.detailSplitNo = detailSplitNo;
    }
    
    
}





















