import React, { useState, useEffect } from 'react';
import SplitScreen from '../components/SplitScreen';
import Sound from '../sounds/Sound';
import { useSubscription, useStompClient } from 'react-stomp-hooks';
import { useSearchParams } from 'react-router-dom';
import { sendResultMsg, getWsEndpoint } from '../utils/Util.js';
import Message from '../components/elements/Message';
import { useUpdateEffect } from 'ahooks';

/**
 * 単面、或いは4分割の箱を作成する<br>
 * WebSocketを経由で、サーバーから、1面または1/4面でコンテンツ表示の制御を受けて、<br>
 * ①ブラウザの画面構成を調整する<br>
 * ②HtmlのRoot Fontサイズを設定。1面：16PX、1/4面：8px<br>
 * @module Home
 *
 * @return {*} Homeページ
 */
function Home() {
  const [rowColInfo, setRowColInfo] = useState({ rowNum: 1, colNum: 1 });

  // http://operationcenter/contentapp?display_no=1&display_split_no=0
  const [search, setSearch] = useSearchParams();

  let splitNo = search.get('display_split_no');
  let displayNo = search.get('display_no');

  useEffect(() => {
    document.title = '表示盤番号：' + displayNo + ', 面番号：' + splitNo;
  }, [displayNo, splitNo]);

  const receiveControl = (message) => {
    console.info('receiveControl: ' + message.body);
    let command = JSON.parse(message.body);
    if (command) {
      if (command.rowNum === 1 && command.colNum === 1) {
        // HtmlのFontSizeを16pxにする
        setRootFontSize(16);
      } else {
        // HtmlのFontSizeを8pxにする
        setRootFontSize(8);
      }
      setRowColInfo(command);
    }
  };

  const screenInfoPara = {
    displayNo: displayNo,
    splitNo: splitNo,
  };
  const wsEndpoint = getWsEndpoint(displayNo, splitNo);

  useSubscription(wsEndpoint + '/setControl', receiveControl, screenInfoPara); // Url:  /monitor/0_1/setControl

  const stompClient = useStompClient();

  useEffect(() => {
    if (rowColInfo?.id) {
      sendResultMsg(stompClient, rowColInfo.id, 0);
    }
  }, [rowColInfo?.id, stompClient]);

  if (splitNo == null || displayNo == null) {
    return (
      <Message
        msg={
          '表示盤番号、及び面番号を設定してください。例：http://operationcenter/contentapp/?display_no=0&display_split_no=0'
        }
      />
    );
  }

  splitNo = Number(splitNo);
  displayNo = Number(displayNo);

  // モニターの自動画角調整機能対応
  // モニターが描画範囲を検知して自動で画角調整を行うため、描画範囲を示すための目印を四隅に追加する。
  const cornerCommonStyle = "absolute h-[1px] w-[3px] bg-white"; //四隅の横並び3pxを白色(#FFFFFF)に塗りつぶす

  return (
    <div className="static">
      <div className={`${cornerCommonStyle} left-0 top-0`}></div>
      <div className={`${cornerCommonStyle} top-0 right-0`}></div>
      <div className={`${cornerCommonStyle} bottom-0 left-0`}></div>
      <div className={`${cornerCommonStyle} bottom-0 right-0`}></div>

      {rowColInfo.rowNum === 2 && rowColInfo.colNum === 2 && (

        <div className="bg-black grid min-h-screen grid-rows-2 grid-cols-2 place-content-stretch select-none overflow-hidden">

          {/* border-xxのClassで、分割したContent間の線を引く */}
          <div className="flex flex-col text-white w-full h-full border-r-[1px] border-b-[1px] border-white p-[1px]">
            <SplitScreen
              displayNo={displayNo}
              splitNo={splitNo}
              detailSplitNo={0}
            />
          </div>
          <div className="flex flex-col text-white w-full h-full border-l-[1px] border-b-[1px] border-white p-[1px]">
            <SplitScreen
              displayNo={displayNo}
              splitNo={splitNo}
              detailSplitNo={1}
            />
          </div>
          <div className="flex flex-col text-white w-full h-full border-r-[1px] border-t-[1px] border-white p-[1px]">
            <SplitScreen
              displayNo={displayNo}
              splitNo={splitNo}
              detailSplitNo={2}
            />
          </div>
          <div className="flex flex-col text-white w-full h-full border-l-[1px] border-t-[1px] border-white p-[1px]">
            <SplitScreen
              displayNo={displayNo}
              splitNo={splitNo}
              detailSplitNo={3}
            />
          </div>
        </div>
      )}
      {rowColInfo.rowNum === 1 && rowColInfo.colNum === 1 && (
        <div className="bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-stretch select-none overflow-hidden">
          <div className="flex flex-col text-white w-full h-full p-[1px]">
            <SplitScreen
              displayNo={displayNo}
              splitNo={splitNo}
              detailSplitNo={0}
            />
          </div>
        </div>
      )}
      {
          <div className="bg-black">
          <div className="bg-black text-white">
            <Sound
              displayNo={displayNo}
              splitNo={splitNo}
            />
          </div>
        </div>
      }
    </div>
  );
}

/**
 * HtmlRootのFontSize変更
 *
 * @param {Number} size px単位のFontSizeの数字
 */
 function setRootFontSize(size) {
  let fontSize = size || 16;

  document.getElementsByTagName('html')[0].style['font-size'] = fontSize + 'px';
}

export default Home;
