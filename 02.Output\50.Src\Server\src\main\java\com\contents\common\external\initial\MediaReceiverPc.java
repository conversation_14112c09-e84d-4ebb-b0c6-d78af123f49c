package com.contents.common.external.initial;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報) メディアレシーバーPC
 */
public class MediaReceiverPc {

    @JsonProperty("display_no")
    private Integer displayNo;
    
    @JsonProperty("display_split_no")
    private Integer displaySplitNo;
    
    @JsonProperty("pc_name")
    private String pcName;
    
    @JsonProperty("ip_address")
    private String ipAddress;
    
    @JsonProperty("usb_speaker_flag")
    private Boolean usbSpeakerFlag;

    @JsonProperty("display_no")
    public Integer getDisplayNo() {
        return displayNo;
    }

    @JsonProperty("display_no")
    public void setDisplayNo(Integer displayNo) {
        this.displayNo = displayNo;
    }

    @JsonProperty("display_split_no")
    public Integer getDisplaySplitNo() {
        return displaySplitNo;
    }

    @JsonProperty("display_split_no")
    public void setDisplaySplitNo(Integer displaySplitNo) {
        this.displaySplitNo = displaySplitNo;
    }

    @JsonProperty("pc_name")
    public String getPcName() {
        return pcName;
    }

    @JsonProperty("pc_name")
    public void setPcName(String pcName) {
        this.pcName = pcName;
    }

    @JsonProperty("ip_address")
    public String getIpAddress() {
        return ipAddress;
    }

    @JsonProperty("ip_address")
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    @JsonProperty("usb_speaker_flag")
    public Boolean getUsbSpeakerFlag() {
        return usbSpeakerFlag;
    }

    @JsonProperty("usb_speaker_flag")
    public void setUsbSpeakerFlag(Boolean usbSpeakerFlag) {
        this.usbSpeakerFlag = usbSpeakerFlag;
    }

}