package com.contents.external.sound_volume;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import javax.xml.bind.DatatypeConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.contents.common.external.initial.Display;
import com.contents.common.external.initial.SoundVolumeCtrl;
import com.contents.configuration.ExternalConfiguration;
import com.contents.manager.SocketClient;
import com.contents.manager.SocketManager;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;

/**
 * 音量コントローラ制御<br>
 * ALC-88B
 */
@Slf4j
@Component
public class SoundVolumeControl {
	private static final int PING_TIMEOUT = 1;
	private static final int CONNECT_TIMEOUT = 1000;
	private static final int RECEIVE_TIMEOUT = 3000;

	private static final int CH_MIN = 1;
	private static final int CH_MAX = 8;

	private static final int VOL_MIN = 0;
	private static final int VOL_MAX = 100;
	private static final int MUTE_ON = 1;
	private static final double MAX_VOL_STEP_VOLUME_CTRL = 255.0;
	private static final double MAX_VOL_STEP_REQUEST_IF = 100.0;

	private static final String CMD_SETVOL = "#A";
	private static final String CMD_GETVOL = "#a";
	private static final String CMD_NEWLINE = "\r";
	private static final String MSG_NEWLINE = "\r";

	public static final int CMD_LEN = (6 + MSG_NEWLINE.length());

	private final ExternalConfiguration extConf;
	//private final SocketManager socketMgr;
	/** 接続状態 true:接続, false:未接続 */
	private boolean connectStatus = false;

	@Autowired
	public SoundVolumeControl(
			ExternalConfiguration extConf) {
		this.extConf = extConf;

		//healthCheck();
	}

	//    @Autowired
	//    public SoundVolumeControl(
	//            ExternalConfiguration extConf,
	//            SocketManager socketMgr) {
	//        this.extConf = extConf;
	//        this.socketMgr = socketMgr;
	//        healthCheck();
	//    }

	/**
	 * 接続状態
	 * @return 接続状態 true:接続中, false:未接続
	 */
	public boolean isConnect() {
		return this.connectStatus;
	}

	/**
	 * 定期監視
	 */
	public void healthCheck() {
		log.info("Health check start.");
		boolean isConnect = true;

		// ping送信
		SoundVolumeCtrl volCtrl = extConf.getExternal().getSoundVolumeCtrl();
		Ping ping = new Ping(volCtrl.getIpAddress(), PING_TIMEOUT);
		if (ping.ping()) {
			for (int i = CH_MIN; i <= CH_MAX; i++) {
				// アラーム状態コマンド送信
				if (!cmdHealthCheck(volCtrl.getIpAddress(), volCtrl.getPort(), i)) {
					isConnect = false;
					break;
				}
			}
		} else {
			log.warn("Health check ping error. ip={}", volCtrl.getIpAddress());
			isConnect = false;
		}
		this.connectStatus = isConnect;
		log.info("ip={}, port={}, connectStatus={}", volCtrl.getIpAddress(), volCtrl.getPort(), this.connectStatus);

		log.info("Health check end.");
	}

	/**
	* 音量設定要求
	* @param displayNo 表示盤番号
	* @param volume 音量値
	* @param mute ミュート制御
	* @return 処理結果 true:成功, false:失敗
	*/
	public boolean volumeControl(int displayNo, int volume, int mute) {
		log.info("volumeControl(): displayNo={}, volume={}, mute={}", displayNo, volume, mute);

		// 機器管理情報を取得
		SoundVolumeCtrl volCtrl = extConf.getExternal().getSoundVolumeCtrl();
		Display display = extConf.getDisplay(displayNo);

		if (display != null) {
			// 制御CH
			Integer ch = display.getSoundVolumeCtrlCh();
			if (ch != null) {
				log.info("machineInfo ip={}, port={}, ch={}", volCtrl.getIpAddress(), volCtrl.getPort(), ch);

				// 音量設定
				boolean result = volumeControlExec(volCtrl.getIpAddress(), volCtrl.getPort(), ch, volume, mute);
				if (!result) {
					log.error("SoundVolumeController: volume setting error.");
					return false;
				}
			} else {
				// 音声出力先が見つからない
				log.warn("unknown configuration: Channel");
				return false;
			}
		} else {
			// 対象の表示盤が見つからない場合はログ出力して正常終了
			log.warn("Target display not found. displayNo={}", displayNo);
		}
		return true;
	}

	/**
	 * 音量設定実施
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @param ch 制御CH
	 * @param volume 音量値
	 * @param mute ミュート制御
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean volumeControlExec(String ip, int port, int ch, int volume, int mute) {
		int setVolume = volume;
		boolean ret;

		// ミュート処理
		if (Objects.equals(mute, MUTE_ON)) {
			setVolume = 0;
			log.info("ch={}: Mute ON", ch);
		}

		// ソケット接続
		SocketClient socket = SocketManager.getSocketClient(ip, port);
		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
			// コマンド送信
			ret = sendVolumeControl(socket, ch, setVolume);
			socket.close();

		} else {
			log.error("Socket connection error.");
			ret = false;
		}

		return ret;
	}

	/**
	 * 音量設定コマンド作成
	 * @param ch 制御チャンネル
	 * @param vol 音量値
	 * @return コマンド or null (制御チャンネル、音量値が対象外)
	 */
	private String createCmdSetVol(int ch, int vol) {
		String cmd = null;

		if (createCmdParamCheck(ch, vol)) {
			String param = createCmdVolConvert(ch, vol);
			cmd = CMD_SETVOL + param + CMD_NEWLINE;
		}
		return cmd;
	}

	/**
	 * 設定パラメータ確認
	 * @param ch 制御チャンネル
	 * @param vol 音量値
	 * @return 処理結果 true:OK, false:NG
	 */
	private boolean createCmdParamCheck(int ch, int vol) {
		// チャンネル
		if (ch < CH_MIN || CH_MAX < ch) {
			log.warn("createCmdParamCheck: out of range ch={}", ch);
			return false;
		}
		// ボリューム
		if (vol < VOL_MIN || VOL_MAX < vol) {
			log.warn("createCmdParamCheck: out of range vol={}", vol);
			return false;
		}
		return true;
	}

	/**
	 * 設定値からコマンドパラメータ部を作成する
	 * @param ch 制御チャンネル
	 * @param vol 音量値
	 * @return コマンド or null (制御チャンネル、音量値が対象外)
	 */
	private String createCmdVolConvert(int ch, int vol) {
		// volume値変換
		double volDouble = (MAX_VOL_STEP_VOLUME_CTRL / MAX_VOL_STEP_REQUEST_IF) * vol;
		int setVol = (int) Math.ceil(volDouble);
		//log.info("vol_double={}, vol_double_roundup={}", vol_double, vol_double_roundup);

		// コマンド作成
		return String.format("%1d", ch) + String.format("%03d", setVol);
	}

	/**
	 * 音量取得コマンド作成
	 * @param ch 制御チャンネル
	 * @return コマンド or null (制御チャンネルが対象外)
	 */
	private String createCmdGetVol(Integer ch) {
		String cmd = null;
		final int vol = 0; // 読み出し時は0固定

		if (createCmdParamCheck(ch, vol)) {
			String param = createCmdVolConvert(ch, vol);
			cmd = CMD_GETVOL + param + CMD_NEWLINE;
		}
		return cmd;
	}

	/**
	 * 音量設定値取得(ヘルスチェック用)
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @param ch 制御チャンネル
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean cmdHealthCheck(String ip, int port, Integer ch) {
		boolean ret;

		// ソケット接続
		SocketClient socket = SocketManager.getSocketClient(ip, port);
		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
			// コマンド送信
			ret = sendGetVolume(socket, ch);
			socket.close();

		} else {
			log.error("Socket connection error.");
			ret = false;
		}

		return ret;
	}

	/**
	 * 音量セットコマンド送信
	 * @param socket Open済みのSocket
	 * @param ch 制御CH
	 * @param volume 音量値
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendVolumeControl(SocketClient socket, Integer ch, Integer volume) {
		byte[] sendData;

		// 音量設定コマンド作成
		String strSetVol = createCmdSetVol(ch, volume);
		if (strSetVol == null) {
			log.warn("strSetVol() Command null");
			return false;
		}
		
		log.info("★ send command={} ", strSetVol);
		sendData = strSetVol.getBytes();

		// 音量設定コマンド送信
		if (!socket.send(sendData, null)) {
			return false;
		}

		// 音量取得コマンド作成
		String strGetVol = createCmdGetVol(ch);
		if (strGetVol == null) {
			log.info("cmdGetVol() Command null");
			return false;
		}
		log.info("★ send command={} ", strGetVol);
		sendData = strGetVol.getBytes();

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		// 音量取得コマンド送信
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答コマンド長チェック
		if ((rcvCmd.get() == null) || (rcvCmd.get().length() > CMD_LEN)) {
			log.error("Get volume response length error.");
			return false;
		}

		// 応答コマンドを大文字に変換して一致した場合は設定成功
		if (strSetVol.startsWith(rcvCmd.get().toUpperCase())) {
			log.info("setting value compare ok");
		} else {
			log.error("setting value compare error");
			return false;
		}
		return true;
	}

	/**
	 * 音量取得コマンド送信
	 * @param socket Open済みのSocket
	 * @param ch 制御CH
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendGetVolume(SocketClient socket, Integer ch) {
		byte[] sendData;

		// 音量取得コマンド作成
		String cmdGetVol = createCmdGetVol(ch);
		if (cmdGetVol == null) {
			log.info("cmdGetVol() Command null");
			return false;
		}
		
		log.info("★ send command={} ", cmdGetVol);
		
		sendData = cmdGetVol.getBytes();

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		// 音量取得コマンド送信
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答コマンドの内容確認はチェックしない
		if (!responseCheckValue(rcvCmd.get())) {
			log.error("Get volume response length error.");
			return false;
		}
		return true;
	}

	/**
	 * コマンドレスポンス確認
	 *
	 * @param str レスポンス文字列
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean responseCheckValue(String str) {
		// コマンド長
		if (str == null || str.length() > CMD_LEN) {
			log.error("Response length error.");
			return false;
		}
		return true;
	}
}
