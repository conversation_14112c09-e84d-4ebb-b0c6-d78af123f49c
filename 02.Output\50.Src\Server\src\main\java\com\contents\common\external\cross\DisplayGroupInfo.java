package com.contents.common.external.cross;

import java.util.List;

public class DisplayGroupInfo extends AbstractDevice {

	/** 表示盤種別 */
	private Integer displayType;
	
	/** 表示盤番号 */
	private Integer displayNumber;
	
	/** USBスピーカーデバイス番号 */
	private Integer onUsbSpeakerDeviceNumber;
	
	/** 面分割情報 */
	private List<DisplaySplitInfo> splitList;

	/**
	 * 表示盤種別を取得します。
	 * @return 表示盤種別
	 */
	public Integer getDisplayType() {
	    return displayType;
	}

	/**
	 * 表示盤種別を設定します。
	 * @param displayType 表示盤種別
	 */
	public void setDisplayType(Integer displayType) {
	    this.displayType = displayType;
	}

	/**
	 * 表示盤番号を取得します。
	 * @return 表示盤番号
	 */
	public Integer getDisplayNumber() {
	    return displayNumber;
	}

	/**
	 * 表示盤番号を設定します。
	 * @param displayNumber 表示盤番号
	 */
	public void setDisplayNumber(Integer displayNumber) {
	    this.displayNumber = displayNumber;
	}

	/**
	 * USBスピーカーデバイス番号を取得します。
	 * @return USBスピーカーデバイス番号
	 */
	public Integer getOnUsbSpeakerDeviceNumber() {
	    return onUsbSpeakerDeviceNumber;
	}

	/**
	 * USBスピーカーデバイス番号を設定します。
	 * @param onUsbSpeakerDeviceNumber USBスピーカーデバイス番号
	 */
	public void setOnUsbSpeakerDeviceNumber(Integer onUsbSpeakerDeviceNumber) {
	    this.onUsbSpeakerDeviceNumber = onUsbSpeakerDeviceNumber;
	}

	/**
	 * 面分割情報を取得します。
	 * @return 面分割情報
	 */
	public List<DisplaySplitInfo> getSplitList() {
	    return splitList;
	}

	/**
	 * 面分割情報を設定します。
	 * @param splitList 面分割情報
	 */
	public void setSplitList(List<DisplaySplitInfo> splitList) {
	    this.splitList = splitList;
	}
	
}
