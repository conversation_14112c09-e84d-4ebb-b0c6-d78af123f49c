package com.contents.model;

import java.util.Objects;
import com.contents.model.CaseHalfContentCase;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseHalfContent
 */
@Validated


public class CaseHalfContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("case")
  @Valid
  private List<CaseHalfContentCase> _case = null;

  public CaseHalfContent _case(List<CaseHalfContentCase> _case) {
    this._case = _case;
    return this;
  }

  public CaseHalfContent addCaseItem(CaseHalfContentCase _caseItem) {
    if (this._case == null) {
      this._case = new ArrayList<CaseHalfContentCase>();
    }
    this._case.add(_caseItem);
    return this;
  }

  /**
   * ※とでも重要。
   * caseがJava言語のKeywordで、変数名として使えない。これを回避する為、自動生成した変数名は、_caseになったが、SpringBeanの不具合あり、Exceptionが出た。
   * これを対策する為、Getter/Setterを変数名の_caseに合わせて修正する
   */
  /**
   * Get _case
   * @return _case
   **/
  @Schema(description = "")
      @Valid
  @Size(max=2)   public List<CaseHalfContentCase> get_case() {
    return _case;
  }

  public void set_case(List<CaseHalfContentCase> _case) {
    this._case = _case;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseHalfContent caseHalfContent = (CaseHalfContent) o;
    return Objects.equals(this._case, caseHalfContent._case);
  }

  @Override
  public int hashCode() {
    return Objects.hash(_case);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseHalfContent {\n");
    
    sb.append("    _case: ").append(toIndentedString(_case)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
