package com.contents.common.external.initial;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 設定ファイル(機器情報)
 */
public class External {

    @JsonProperty("display_list")
    private List<Display> displayList = null;
    
    @JsonProperty("matrix_sw")
    private MatrixSw matrixSw;
    
    @JsonProperty("sound_volume_ctrl")
    private SoundVolumeCtrl soundVolumeCtrl;
    
    @JsonProperty("media_receiver_pc_list")
    private List<MediaReceiverPc> mediaReceiverPcList = null;

    @JsonProperty("display_list")
    public List<Display> getDisplayList() {
        return displayList;
    }

    @JsonProperty("display_list")
    public void setDisplayList(List<Display> displayList) {
        this.displayList = displayList;
    }

    @JsonProperty("matrix_sw")
    public MatrixSw getMatrixSw() {
        return matrixSw;
    }

    @JsonProperty("matrix_sw")
    public void setMatrixSw(MatrixSw matrixSw) {
        this.matrixSw = matrixSw;
    }

    @JsonProperty("sound_volume_ctrl")
    public SoundVolumeCtrl getSoundVolumeCtrl() {
        return soundVolumeCtrl;
    }

    @JsonProperty("sound_volume_ctrl")
    public void setSoundVolumeCtrl(SoundVolumeCtrl soundVolumeCtrl) {
        this.soundVolumeCtrl = soundVolumeCtrl;
    }

    @JsonProperty("media_receiver_pc_list")
    public List<MediaReceiverPc> getMediaReceiverPcList() {
        return mediaReceiverPcList;
    }

    @JsonProperty("media_receiver_pc_list")
    public void setMediaReceiverPcList(List<MediaReceiverPc> mediaReceiverPcList) {
        this.mediaReceiverPcList = mediaReceiverPcList;
    }

}
