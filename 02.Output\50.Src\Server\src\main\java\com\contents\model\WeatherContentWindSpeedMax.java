package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * WeatherContentWindSpeedMax
 */
@Validated


public class WeatherContentWindSpeedMax   {
  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("display_text")
  private String displayText = null;

  public WeatherContentWindSpeedMax textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public WeatherContentWindSpeedMax backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public WeatherContentWindSpeedMax displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 最大風速
   * @return displayText
   **/
  @Schema(description = "最大風速")
  
  @Pattern(regexp="^[\\d,.]*$") @Size(max=6)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WeatherContentWindSpeedMax weatherContentWindSpeedMax = (WeatherContentWindSpeedMax) o;
    return Objects.equals(this.textColor, weatherContentWindSpeedMax.textColor) &&
        Objects.equals(this.backgroundColor, weatherContentWindSpeedMax.backgroundColor) &&
        Objects.equals(this.displayText, weatherContentWindSpeedMax.displayText);
  }

  @Override
  public int hashCode() {
    return Objects.hash(textColor, backgroundColor, displayText);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WeatherContentWindSpeedMax {\n");
    
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
