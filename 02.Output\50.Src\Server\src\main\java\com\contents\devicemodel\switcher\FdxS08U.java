package com.contents.devicemodel.switcher;

import java.io.IOException;
import java.time.Instant;

import javax.xml.bind.DatatypeConverter;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.annotation.ExternalDeviceProduct;
import com.contents.manager.CrossSocketClient;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;


/**
 * マトリックススイッチャーモデルクラス
 * <p>
 * 対応機種: FDX-S08U, FDX-S08, FDX-S16U, FDX-S16, FDX-S32U, FDX-S32
 * <p>
 * ※2024/11/28時点では搭載ボードによる処理の切り分けの必要性は発生していない。<br />
 * 今後、搭載ボードに対応する実装が必要になった場合は、機器構成ファイルからの見直しを推奨する。
 * <p>
 * ※FDX-S64は@SASの命令にてカスタマイズが必要。<br />
 * 「FDX-S64の場合」と「FDX-S64以外の場合」で若干制御が異なるとマニュアルに記載がある。
 */
@Slf4j
@ExternalDeviceProduct(names = { "FDX-S08U", "FDX-S08", "FDX-S16U", "FDX-S16", "FDX-S32U", "FDX-S32" })
public class FdxS08U extends MatrixSwitcherDeviceModel {

	/* PINGタイムアウト設定値 (sec) */
	private final int PING_TIMEOUT = 1;
	/* ソケット 各種タイムアウト設定値 (msec) */
	private final int CONNECT_TIMEOUT = 1000;
	private final int RECEIVE_TIMEOUT = 10 * 1000;
	
	

	private boolean checkReceiveData(byte[] recvData) {

		String recvStr = new String(recvData);
		log.info("recvData(String): {}", recvStr);
		log.info("recvData: " + DatatypeConverter.printHexBinary(recvData));

		// エラーチェック
		if (recvStr.startsWith("@ERR")) {
			log.error("Matrix switcher error response.");
			String[] split = recvStr.split(",");
			if (split.length >= 2) {
				switch (split[1]) {
				case "1" -> log.warn("Parameter format or value is incorrect.");
				case "2" -> log.warn("Undefined command or bad command format.");
				case "3" -> log.warn("Command is currently unavailable.");
				case "4" -> log.warn("Failed to read EDID from sink device.");
				default -> log.warn("Unknown error.");
				}
			}

			return false;
		}

		return true;
	}

	/**
	 * メッセージ送信
	 * @param socket Open済みのSocket
	 * @param message 送信メッセージ
	 * @param callback 受信コールバック
	 * @return 処理結果 true:成功, false:失敗
	 * @throws IOException 
	 */
	private byte[] requestMessage(CrossSocketClient socket, byte[] command) throws Exception {

		try {
			
			waitSleep(0);
			// 送信
			return socket.request(command);

		} catch (IOException e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now().plusMillis(100L);
		}
	}

	/**
	 * メッセージ送信
	 * @param socket Open済みのSocket
	 * @param message 送信メッセージ
	 * @param callback 受信コールバック
	 * @return 処理結果 true:成功, false:失敗
	 * @throws IOException 
	 */
	private void sendMessage(CrossSocketClient socket, byte[] command) throws Exception {

		try {
			
			waitSleep(0);
			// 送信
			socket.send(command);

		} catch (IOException e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now().plusMillis(50L);
		}
	}

	@Override
	protected void healthCheck() {
		

		try {

			String ipAddress = matrixSwitcherDevice.getNetworkInfo().getIpAddress();

			if (!CommonUtil.checkIpAddressFormat(ipAddress)) {
				updateDeviceConnectStatus(false);
				return;
			}

			// ping送信
			Ping ping = new Ping(ipAddress, PING_TIMEOUT);
			if (ping.ping()) {
				final boolean connectStatus = sendAlarmStatus();
				updateDeviceConnectStatus(connectStatus);
			} else {
				updateDeviceConnectStatus(false);
			}

		} finally {
			
		}
	}

	private synchronized boolean sendAlarmStatus() {

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {
				
				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT);
				
				// アラーム状態
				byte[] command = "@GAA\r\n".getBytes();

				// 受信処理

				ResultMessageValidator<Boolean, byte[]> validator = (recvData) -> {

					String message = new String(recvData);
					String[] split = message.trim().split(",");
					for (int i = 4; i < split.length; i++) {
						if (Integer.parseInt(split[i]) > 0) {
							log.error("Error in alarm condition. index={}", i);
							return false;
						}
					}
					return true;
				};

				byte[] recvData = requestMessage(socket, command);

				if (!checkReceiveData(recvData)) {
					// エラー
					return false;
				}

				if (!validator.validate(recvData)) {
					// エラー
					return false;
				}

				return true;
			//}

		} catch (IOException e) {

			log.error("socket connection error.", e);
			return false;
		} catch (Exception e) {
			
			log.error("socket connection error.", e);
			return false;
		}
	}

	public synchronized boolean videoSwitch(Integer inputConnectorNumber, Integer outoutConnectorNumber) {

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {
				
				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT);
				
				// 入出力チャンネル切替
				String format = "@SSW,{},{}\r\n";
				String message = MessageFormatUtil.format(format, inputConnectorNumber, outoutConnectorNumber);

				byte[] command = message.getBytes();

				byte[] recvData = requestMessage(socket, command);

				if (!checkReceiveData(recvData)) {
					// エラー
					return false;
				}

				return true;
			//}

		} catch (IOException e) {

			log.error("socket connection error.", e);
			return false;
		} catch (Exception e) {
			
			log.error("socket connection error.", e);
			return false;
		}

	}

	/**
	 * 音声切替
	 * @param sourceNo ソース番号
	 * @param displayNo 表示盤番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public synchronized boolean audioSwitch(Integer inputConnectorNumber, Integer outoutConnectorNumber) {

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {
				
				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT);
				
				// オーディオディエンベデッド
				String format = "@SAS,{},{}\r\n";
				outoutConnectorNumber = outoutConnectorNumber + 300;
				String message = MessageFormatUtil.format(format, outoutConnectorNumber, inputConnectorNumber);

				byte[] command = message.getBytes();

				byte[] recvData = requestMessage(socket, command);

				if (!checkReceiveData(recvData)) {
					// エラー
					return false;
				}

				return true;
			//}

		} catch (IOException e) {

			log.error("socket connection error.", e);
			return false;
		} catch (Exception e) {
			
			log.error("socket connection error.", e);
			return false;
		}

	}

}
