package com.contents.model;

import java.util.Objects;
import com.contents.model.IncomingCallBContentBlinkSetting;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * IncomingCallBContentIncomingCall
 */
@Validated


public class IncomingCallBContentIncomingCall   {
  @JsonProperty("display_color")
  private String displayColor = null;

  @JsonProperty("blink_setting")
  private IncomingCallBContentBlinkSetting blinkSetting = null;

  public IncomingCallBContentIncomingCall displayColor(String displayColor) {
    this.displayColor = displayColor;
    return this;
  }

  /**
   * Get displayColor
   * @return displayColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getDisplayColor() {
    return displayColor;
  }

  public void setDisplayColor(String displayColor) {
    this.displayColor = displayColor;
  }

  public IncomingCallBContentIncomingCall blinkSetting(IncomingCallBContentBlinkSetting blinkSetting) {
    this.blinkSetting = blinkSetting;
    return this;
  }

  /**
   * Get blinkSetting
   * @return blinkSetting
   **/
  @Schema(description = "")
  
    @Valid
    public IncomingCallBContentBlinkSetting getBlinkSetting() {
    return blinkSetting;
  }

  public void setBlinkSetting(IncomingCallBContentBlinkSetting blinkSetting) {
    this.blinkSetting = blinkSetting;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IncomingCallBContentIncomingCall incomingCallBContentIncomingCall = (IncomingCallBContentIncomingCall) o;
    return Objects.equals(this.displayColor, incomingCallBContentIncomingCall.displayColor) &&
        Objects.equals(this.blinkSetting, incomingCallBContentIncomingCall.blinkSetting);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayColor, blinkSetting);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IncomingCallBContentIncomingCall {\n");
    
    sb.append("    displayColor: ").append(toIndentedString(displayColor)).append("\n");
    sb.append("    blinkSetting: ").append(toIndentedString(blinkSetting)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
