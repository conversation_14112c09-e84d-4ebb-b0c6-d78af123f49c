package com.contents.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import java.util.Objects;

/**
 * ContentDataDisplayContentData
 */
@Validated


public class ContentDataDisplayContentData   {
  @JsonProperty("source_no")
  private Integer sourceNo = null;

  @JsonProperty("source_name")
  private String sourceName = null;

  @JsonProperty("source_split_no")
  private Integer sourceSplitNo = null;

  @JsonProperty("source_data")
  @JsonTypeInfo(
          use = JsonTypeInfo.Id.NAME,
          include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
          property = "source_no")
  private AnyOfcontentDataDisplayContentDataSourceData sourceData = null;

  public ContentDataDisplayContentData sourceNo(Integer sourceNo) {
    this.sourceNo = sourceNo;
    return this;
  }

  /**
   * 表示盤に表示するソース番号
   * minimum: 0
   * @return sourceNo
   **/
  @Schema(description = "表示盤に表示するソース番号")
  
  @Min(0)  public Integer getSourceNo() {
    return sourceNo;
  }

  public void setSourceNo(Integer sourceNo) {
    this.sourceNo = sourceNo;
  }

  public ContentDataDisplayContentData sourceName(String sourceName) {
    this.sourceName = sourceName;
    return this;
  }

  /**
   * ソース番号に紐づく名称
   * @return sourceName
   **/
  @Schema(description = "ソース番号に紐づく名称")
  
    public String getSourceName() {
    return sourceName;
  }

  public void setSourceName(String sourceName) {
    this.sourceName = sourceName;
  }

  public ContentDataDisplayContentData sourceSplitNo(Integer sourceSplitNo) {
    this.sourceSplitNo = sourceSplitNo;
    return this;
  }

  /**
   * 同一のソース番号(コンテンツ)を分割した番号 例：事案や気象情報など複数コンテンツ表示可能な場合に設定
   * minimum: 0
   * @return sourceSplitNo
   **/
  @Schema(description = "同一のソース番号(コンテンツ)を分割した番号 例：事案や気象情報など複数コンテンツ表示可能な場合に設定")
  
  @Min(0)  public Integer getSourceSplitNo() {
    return sourceSplitNo;
  }

  public void setSourceSplitNo(Integer sourceSplitNo) {
    this.sourceSplitNo = sourceSplitNo;
  }

  public ContentDataDisplayContentData sourceData(AnyOfcontentDataDisplayContentDataSourceData sourceData) {
    this.sourceData = sourceData;
    return this;
  }

  /**
   * とでも重要。@Validを追加しないと、各ContentへのValidationを実行されない
   * Get sourceData
   * @return sourceData
   **/
  @Schema(description = "")
  @Valid
    public AnyOfcontentDataDisplayContentDataSourceData getSourceData() {
    return sourceData;
  }

  public void setSourceData(AnyOfcontentDataDisplayContentDataSourceData sourceData) {
    this.sourceData = sourceData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ContentDataDisplayContentData contentDataDisplayContentData = (ContentDataDisplayContentData) o;
    return Objects.equals(this.sourceNo, contentDataDisplayContentData.sourceNo) &&
        Objects.equals(this.sourceName, contentDataDisplayContentData.sourceName) &&
        Objects.equals(this.sourceSplitNo, contentDataDisplayContentData.sourceSplitNo) &&
        Objects.equals(this.sourceData, contentDataDisplayContentData.sourceData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sourceNo, sourceName, sourceSplitNo, sourceData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ContentDataDisplayContentData {\n");
    
    sb.append("    sourceNo: ").append(toIndentedString(sourceNo)).append("\n");
    sb.append("    sourceName: ").append(toIndentedString(sourceName)).append("\n");
    sb.append("    sourceSplitNo: ").append(toIndentedString(sourceSplitNo)).append("\n");
    sb.append("    sourceData: ").append(toIndentedString(sourceData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
