package com.contents.model;

import java.util.Objects;
import com.contents.model.DigitalRadioContentWirelessChannelName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DigitalRadioContent
 */
@Validated


public class DigitalRadioContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("wireless_channel_name")
  @Valid
  private List<DigitalRadioContentWirelessChannelName> wirelessChannelName = null;

  public DigitalRadioContent wirelessChannelName(List<DigitalRadioContentWirelessChannelName> wirelessChannelName) {
    this.wirelessChannelName = wirelessChannelName;
    return this;
  }

  public DigitalRadioContent addWirelessChannelNameItem(DigitalRadioContentWirelessChannelName wirelessChannelNameItem) {
    if (this.wirelessChannelName == null) {
      this.wirelessChannelName = new ArrayList<DigitalRadioContentWirelessChannelName>();
    }
    this.wirelessChannelName.add(wirelessChannelNameItem);
    return this;
  }

  /**
   * Get wirelessChannelName
   * @return wirelessChannelName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=4)   public List<DigitalRadioContentWirelessChannelName> getWirelessChannelName() {
    return wirelessChannelName;
  }

  public void setWirelessChannelName(List<DigitalRadioContentWirelessChannelName> wirelessChannelName) {
    this.wirelessChannelName = wirelessChannelName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DigitalRadioContent digitalRadioContent = (DigitalRadioContent) o;
    return Objects.equals(this.wirelessChannelName, digitalRadioContent.wirelessChannelName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(wirelessChannelName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DigitalRadioContent {\n");
    
    sb.append("    wirelessChannelName: ").append(toIndentedString(wirelessChannelName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
