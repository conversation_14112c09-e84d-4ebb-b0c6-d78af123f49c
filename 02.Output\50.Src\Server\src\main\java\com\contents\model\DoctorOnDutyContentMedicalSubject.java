package com.contents.model;

import java.util.Objects;
import com.contents.model.DoctorOnDutyContentMedicalInstitutionName;
import com.contents.model.DoctorOnDutyContentMedicalInstitutionTelephoneNo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DoctorOnDutyContentMedicalSubject
 */
@Validated


public class DoctorOnDutyContentMedicalSubject   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("medical_institution_name")
  @Valid
  private List<DoctorOnDutyContentMedicalInstitutionName> medicalInstitutionName = null;

  @JsonProperty("medical_institution_telephone_no")
  @Valid
  private List<DoctorOnDutyContentMedicalInstitutionTelephoneNo> medicalInstitutionTelephoneNo = null;

  public DoctorOnDutyContentMedicalSubject displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 診療科目
   * @return displayText
   **/
  @Schema(description = "診療科目")
  
  @Size(max=2)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public DoctorOnDutyContentMedicalSubject textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public DoctorOnDutyContentMedicalSubject backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public DoctorOnDutyContentMedicalSubject medicalInstitutionName(List<DoctorOnDutyContentMedicalInstitutionName> medicalInstitutionName) {
    this.medicalInstitutionName = medicalInstitutionName;
    return this;
  }

  public DoctorOnDutyContentMedicalSubject addMedicalInstitutionNameItem(DoctorOnDutyContentMedicalInstitutionName medicalInstitutionNameItem) {
    if (this.medicalInstitutionName == null) {
      this.medicalInstitutionName = new ArrayList<DoctorOnDutyContentMedicalInstitutionName>();
    }
    this.medicalInstitutionName.add(medicalInstitutionNameItem);
    return this;
  }

  /**
   * Get medicalInstitutionName
   * @return medicalInstitutionName
   **/
  @Schema(description = "")
      @Valid
    public List<DoctorOnDutyContentMedicalInstitutionName> getMedicalInstitutionName() {
    return medicalInstitutionName;
  }

  public void setMedicalInstitutionName(List<DoctorOnDutyContentMedicalInstitutionName> medicalInstitutionName) {
    this.medicalInstitutionName = medicalInstitutionName;
  }

  public DoctorOnDutyContentMedicalSubject medicalInstitutionTelephoneNo(List<DoctorOnDutyContentMedicalInstitutionTelephoneNo> medicalInstitutionTelephoneNo) {
    this.medicalInstitutionTelephoneNo = medicalInstitutionTelephoneNo;
    return this;
  }

  public DoctorOnDutyContentMedicalSubject addMedicalInstitutionTelephoneNoItem(DoctorOnDutyContentMedicalInstitutionTelephoneNo medicalInstitutionTelephoneNoItem) {
    if (this.medicalInstitutionTelephoneNo == null) {
      this.medicalInstitutionTelephoneNo = new ArrayList<DoctorOnDutyContentMedicalInstitutionTelephoneNo>();
    }
    this.medicalInstitutionTelephoneNo.add(medicalInstitutionTelephoneNoItem);
    return this;
  }

  /**
   * Get medicalInstitutionTelephoneNo
   * @return medicalInstitutionTelephoneNo
   **/
  @Schema(description = "")
      @Valid
    public List<DoctorOnDutyContentMedicalInstitutionTelephoneNo> getMedicalInstitutionTelephoneNo() {
    return medicalInstitutionTelephoneNo;
  }

  public void setMedicalInstitutionTelephoneNo(List<DoctorOnDutyContentMedicalInstitutionTelephoneNo> medicalInstitutionTelephoneNo) {
    this.medicalInstitutionTelephoneNo = medicalInstitutionTelephoneNo;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DoctorOnDutyContentMedicalSubject doctorOnDutyContentMedicalSubject = (DoctorOnDutyContentMedicalSubject) o;
    return Objects.equals(this.displayText, doctorOnDutyContentMedicalSubject.displayText) &&
        Objects.equals(this.textColor, doctorOnDutyContentMedicalSubject.textColor) &&
        Objects.equals(this.backgroundColor, doctorOnDutyContentMedicalSubject.backgroundColor) &&
        Objects.equals(this.medicalInstitutionName, doctorOnDutyContentMedicalSubject.medicalInstitutionName) &&
        Objects.equals(this.medicalInstitutionTelephoneNo, doctorOnDutyContentMedicalSubject.medicalInstitutionTelephoneNo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor, backgroundColor, medicalInstitutionName, medicalInstitutionTelephoneNo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DoctorOnDutyContentMedicalSubject {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    medicalInstitutionName: ").append(toIndentedString(medicalInstitutionName)).append("\n");
    sb.append("    medicalInstitutionTelephoneNo: ").append(toIndentedString(medicalInstitutionTelephoneNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
