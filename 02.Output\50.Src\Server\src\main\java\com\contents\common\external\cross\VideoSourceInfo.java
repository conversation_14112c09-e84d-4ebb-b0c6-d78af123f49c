package com.contents.common.external.cross;

public class VideoSourceInfo extends AbstractDevice {

	/** ビデオソース番号 */
	private Integer videoSourceNumber;
	
	/** ビデオソース名称 */
	private String videoSourceName;

	/**
	 * ビデオソース番号を取得します。
	 * @return ビデオソース番号
	 */
	public Integer getVideoSourceNumber() {
	    return videoSourceNumber;
	}

	/**
	 * ビデオソース番号を設定します。
	 * @param videoSourceNumber ビデオソース番号
	 */
	public void setVideoSourceNumber(Integer videoSourceNumber) {
	    this.videoSourceNumber = videoSourceNumber;
	}

	/**
	 * ビデオソース名称を取得します。
	 * @return ビデオソース名称
	 */
	public String getVideoSourceName() {
	    return videoSourceName;
	}

	/**
	 * ビデオソース名称を設定します。
	 * @param videoSourceName ビデオソース名称
	 */
	public void setVideoSourceName(String videoSourceName) {
	    this.videoSourceName = videoSourceName;
	}
}
