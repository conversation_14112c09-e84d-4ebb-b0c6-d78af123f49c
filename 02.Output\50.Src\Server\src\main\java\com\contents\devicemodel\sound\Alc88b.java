package com.contents.devicemodel.sound;

import java.io.IOException;
import java.time.Instant;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import javax.xml.bind.DatatypeConverter;

import org.apache.commons.lang3.StringUtils;

import com.contents.common.annotation.ExternalDeviceProduct;
import com.contents.manager.CrossSocketClient;
import com.contents.manager.SocketClient;
import com.contents.manager.SocketManager;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;

/**
 * 音量レベルコントローラーモデルクラス
 * <p>
 * 対応機種: ALC-88B 
 * <p>
 * SI-60Fにソケット通信で命令を送り、シリアル変換してコントローラーに伝達させている。<br />
 * SI-60Fでは無い機種を使う場合に命令の送り方が変わるのであれば、<br />
 * 機器制御ファイルに中継器に何を使っているか、といった要素が必要かもしれない。
 * 
 */
@Slf4j
@ExternalDeviceProduct(names = { "ALC-88B" })
public class Alc88b extends SoundVolumeDeviceModel {

	private static final int PING_TIMEOUT = 1;
	private static final int CONNECT_TIMEOUT = 1000;
	private static final int RECEIVE_TIMEOUT = 10000;

	private static final int CH_MIN = 1;
	private static final int CH_MAX = 8;

	private static final int VOL_MIN = 0;
	private static final int VOL_MAX = 100;
	private static final int MUTE_ON = 1;
	private static final double MAX_VOL_STEP_VOLUME_CTRL = 255.0;
	private static final double MAX_VOL_STEP_REQUEST_IF = 100.0;

	private static final String CMD_SETVOL = "#A";
	private static final String CMD_GETVOL = "#a";
	private static final String CMD_NEWLINE = "\r";
	private static final String MSG_NEWLINE = "\r";

	public static final int CMD_LEN = (6 + MSG_NEWLINE.length());

	
	private byte[] requestMessage(CrossSocketClient socket, byte[] sendData) throws Exception {
		
		try {
			waitSleep(0);
			// 送信
			return socket.request(sendData);

		} catch (IOException e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now();
		}
	}

	private void sendMessage(CrossSocketClient socket, byte[] sendData) throws Exception {

		try {
			synchronized (this) {
				waitSleep(0);
				// 送信
				
				socket.send(sendData);
			}

		} catch (IOException e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now();
		}
	}

	@Override
	public boolean volumeControl(Integer inputChannel, Integer outputChannel, Integer volumeLevel, boolean mute) {

		if (inputChannel == null || outputChannel == null) {
			// ALC-88Bは入力または出力のチャンネルが未設定
			log.warn("miss setting channel.");
			return false;
		}

		if (!Objects.equals(inputChannel, outputChannel)) {
			// ALC-88Bは入力と出力が同じチャンネルでなくてはならない
			log.warn("miss setting channel.");
			return false;
		}

		return volumeControl(inputChannel, volumeLevel, mute);
	}

	private boolean volumeControl(Integer channel, Integer volumeLevel, boolean mute) {

		log.info("machineInfo ip={}, port={}, channel={}", networkInfo.getIpAddress(), networkInfo.getPort(), channel);

		// 音量設定
		boolean result = volumeControlExec(channel, volumeLevel, mute);

		if (!result) {

			log.error("SoundVolumeController: volume setting error.");
			return false;
		}

		return true;
	}

	@Override
	protected void healthCheck() {
		log.info("Health check start.");

		// ping送信

		Ping ping = new Ping(networkInfo.getIpAddress(), PING_TIMEOUT);
		if (ping.ping()) {
			for (int i = CH_MIN; i <= CH_MAX; i++) {
				// アラーム状態コマンド送信
				if (!cmdHealthCheck(networkInfo.getIpAddress(), networkInfo.getPort(), i)) {
					updateDeviceConnectStatus(false);
					break;
				}
			}
		} else {
			log.warn("Health check ping error. ip={}", networkInfo.getIpAddress());
			updateDeviceConnectStatus(false);
		}

		log.info("Health check end.");
	}

	/**
	 * 音量設定実施
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @param ch 制御CH
	 * @param volume 音量値
	 * @param mute ミュート制御
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean volumeControlExec(Integer channel, Integer volumeLevel, boolean mute) {
		int setVolume = volumeLevel;
		boolean ret = true;

		// ミュート処理
		if (mute) {
			setVolume = 0;
			log.info("★ ch={}: Mute ON", channel);
		}

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			synchronized (socket) {
				
				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT);
				
				return sendVolumeControl(socket, channel, setVolume);
			}

		} catch (IOException e) {

			log.error("socket connection error.", e);
			return false;
		} catch (Exception e) {
			
			log.error("socket connection error.", e);
			return false;
		}
	}

	/**
	 * 音量セットコマンド送信
	 * @param socket Open済みのSocket
	 * @param ch 制御CH
	 * @param volume 音量値
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendVolumeControl(CrossSocketClient socket, Integer ch, Integer volume) {

		String newVolume = createCmdSetVol(ch, volume);

		try {
			// 音量設定コマンド作成
			String command = newVolume;
			if (command == null) {
				log.warn("strSetVol() Command null");
				return false;
			}

			// 音量設定コマンド送信
			log.info("★ send command={} ", command);
			byte[] sendData = command.getBytes();
			sendMessage(socket, sendData);

		} catch (Exception e) {
			log.error("send command error. ", e);
			return false;
		}

		try {
			// 音量取得コマンド作成
			String command = createCmdGetVol(ch);
			
			log.info("★ send command={} ", command);
			
			if (command == null) {
				log.info("cmdGetVol() Command null");
				return false;
			}

			// 音量設定コマンド送信
			byte[] sendData = command.getBytes();
			String recv = new String(requestMessage(socket, sendData));

			if (StringUtils.isEmpty(recv)) {
				log.error("Get volume response length error.");
				return false;
			}

			if (recv.length() > CMD_LEN) {
				log.error("Get volume response length error.");
				return false;
			}

			if (newVolume.startsWith(recv.toUpperCase())) {
				log.info("setting value compare ok");
				return true;
			} else {
				log.error("setting value compare error");
				return false;
			}
		} catch (Exception e) {
			log.error("send command error.", e);
			return false;
		}

	}

	/**
	 * 音量設定コマンド作成
	 * @param ch 制御チャンネル
	 * @param vol 音量値
	 * @return コマンド or null (制御チャンネル、音量値が対象外)
	 */
	private String createCmdSetVol(int ch, int vol) {
		String cmd = null;

		if (createCmdParamCheck(ch, vol)) {
			String param = createCmdVolConvert(ch, vol);
			cmd = CMD_SETVOL + param + CMD_NEWLINE;
		}
		return cmd;
	}

	/**
	 * 音量設定値取得(ヘルスチェック用)
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @param ch 制御チャンネル
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean cmdHealthCheck(String ip, int port, Integer ch) {
		boolean ret = true;

		// ソケット接続
		SocketClient socket = SocketManager.getSocketClient(ip, port);
		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
			// コマンド送信
			ret = sendGetVolume(socket, ch);
			socket.close();
		} else {
			log.error("Socket connection error.");
			ret = false;
		}

		return ret;
	}

	/**
	 * 音量取得コマンド送信
	 * @param socket Open済みのSocket
	 * @param ch 制御CH
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendGetVolume(SocketClient socket, Integer ch) {
		byte[] sendData;

		// 音量取得コマンド作成
		String cmdGetVol = createCmdGetVol(ch);
		if (cmdGetVol == null) {
			log.info("cmdGetVol() Command null");
			return false;
		}
		sendData = cmdGetVol.getBytes();

		// 受信コールバック
		AtomicReference<String> rcvCmd = new AtomicReference<>();
		Consumer<byte[]> callback = (msg) -> {
			log.info("rcvData: " + DatatypeConverter.printHexBinary(msg));
			String str = new String(msg);
			rcvCmd.set(str);
			log.info("response: " + str.replace(MSG_NEWLINE, ""));
		};

		// 音量取得コマンド送信
		if (!socket.send(sendData, callback)) {
			return false;
		}

		// 応答コマンドの内容確認はチェックしない
		if (!responseCheckValue(rcvCmd.get())) {
			log.error("Get volume response length error.");
			return false;
		}
		return true;
	}

	/**
	 * 音量取得コマンド作成
	 * @param ch 制御チャンネル
	 * @return コマンド or null (制御チャンネルが対象外)
	 */
	private String createCmdGetVol(Integer ch) {
		String cmd = null;
		final int vol = 0; // 読み出し時は0固定

		if (createCmdParamCheck(ch, vol)) {
			String param = createCmdVolConvert(ch, vol);
			cmd = CMD_GETVOL + param + CMD_NEWLINE;
		}
		return cmd;
	}

	/**
	 * 設定値からコマンドパラメータ部を作成する
	 * @param ch 制御チャンネル
	 * @param vol 音量値
	 * @return コマンド or null (制御チャンネル、音量値が対象外)
	 */
	private String createCmdVolConvert(int ch, int vol) {
		// volume値変換
		double volDouble = (MAX_VOL_STEP_VOLUME_CTRL / MAX_VOL_STEP_REQUEST_IF) * vol;
		int setVol = (int) Math.ceil(volDouble);
		//log.info("vol_double={}, vol_double_roundup={}", vol_double, vol_double_roundup);

		// コマンド作成
		return String.format("%1d", ch) + String.format("%03d", setVol);
	}

	/**
	 * 設定パラメータ確認
	 * @param ch 制御チャンネル
	 * @param vol 音量値
	 * @return 処理結果 true:OK, false:NG
	 */
	private boolean createCmdParamCheck(int ch, int vol) {
		// チャンネル
		if (ch < CH_MIN || CH_MAX < ch) {
			log.warn("createCmdParamCheck: out of range ch={}", ch);
			return false;
		}
		// ボリューム
		if (vol < VOL_MIN || VOL_MAX < vol) {
			log.warn("createCmdParamCheck: out of range vol={}", vol);
			return false;
		}
		return true;
	}

	/**
	 * コマンドレスポンス確認
	 *
	 * @param str レスポンス文字列
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean responseCheckValue(String str) {
		// コマンド長
		if (str == null || str.length() > CMD_LEN) {
			log.error("Response length error.");
			return false;
		}
		return true;
	}

}
