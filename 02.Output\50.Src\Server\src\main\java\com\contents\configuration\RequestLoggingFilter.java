package com.contents.configuration;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.web.filter.OncePerRequestFilter;

import com.contents.common.CommonUtil;


public class RequestLoggingFilter extends OncePerRequestFilter {
	
	private static final Logger log = LoggerFactory.getLogger(RequestLoggingFilter.class);

	private static final String REQUEST_JSON_LOG_FORMAT = "request. {}" + System.lineSeparator() + "{}";
	
	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Filter<PERSON>hain filterChain)
			throws ServletException, IOException {

		CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(request);

        if ("POST".equalsIgnoreCase(wrappedRequest.getMethod()) && wrappedRequest.getContentType().contains("application/json")) {
            String body = wrappedRequest.getBody();

            // パスをログに出力
            String requestURI = wrappedRequest.getRequestURI();
            String contextPath = wrappedRequest.getContextPath();
            String pathWithoutContext = requestURI.substring(contextPath.length());
            log.info("Request path: {}", pathWithoutContext);
            
         // JSONを整形
            String jsonString = CommonUtil.loggingJsonConvert(body);
            
            String logValue = MessageFormatter.format(REQUEST_JSON_LOG_FORMAT, pathWithoutContext, jsonString).getMessage();
            log.info(logValue);
        }

        // フィルターチェーンを続ける
        filterChain.doFilter(wrappedRequest, response);
	}
}
