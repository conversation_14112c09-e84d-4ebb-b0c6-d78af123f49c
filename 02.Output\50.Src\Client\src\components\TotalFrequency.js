import React from 'react';
import Title from './elements/Title';
import Cell from './elements/Cell';
import { getCellFace, isValidSource, toThousands } from '../utils/Util.js';

/**
 * 総合度数コンテンツ<br>
 * propsは、「3.11総合度数コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module TotalFrequency
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const TotalFrequency = (props) => {
  let cell1, cell2, cell3;
  if (isValidSource(props) && props.agg_unit) {
    cell1 = getCellFace(props.agg_unit[0], 'text-right col-start-2 justify');
    cell2 = getCellFace(props.agg_unit[1], 'text-right col-start-2 justify');
    cell3 = getCellFace(props.agg_unit[2], 'text-right col-start-2 justify');
  }

  const MAX_ROW = 5;

  return (
    <div>
      <Title title={'119着信度数'} />
      {isValidSource(props) && (
        <div className="border-transparent border-x-[1rem] grid grid-cols-[0.9fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1fr_minmax(0.1rem,0.2fr)_1.4fr] text-4.5xl leading-[1] mt-[4rem] items-end gap-y-[4rem]">
          {props.agg_unit && (
            <>
              <div className="col-start-3 grid grid-cols-[1fr,13.5rem]">
                <Cell {...cell1} />
              </div>
              <div className="col-start-5 grid grid-cols-[1fr,13.5rem]">
                <Cell {...cell2} />
              </div>
              <div className="col-start-7 grid grid-cols-[1fr,13.5rem]">
                <Cell {...cell3} />
              </div>
            </>
          )}
          {props.agg_info &&
            props.agg_info.map((infoItem, infoIndex) => {
              if (infoIndex >= MAX_ROW) return undefined;

              return <TotalFrequencyRow key={infoIndex} agg_info={infoItem} />;
            })}
        </div>
      )}
    </div>
  );
};

const TotalFrequencyRow = (props) => {
  let typeProp = getCellFace(props.agg_info, 'text-5xl justify');
  let numCell1;
  let numCell2;
  let numCell3;

  props.agg_info?.number_list?.forEach((numItem, numIndex) => {
    if (numIndex === 0) {
      numCell1 = getCellFace(
        numItem,
        'justify-self-end w-fit text-6xl col-start-3'
      );
      numCell1.text = toThousands(numCell1.text);
      addUnit(numCell1);
    } else if (numIndex === 1) {
      numCell2 = getCellFace(
        numItem,
        'justify-self-end w-fit text-6xl col-start-5'
      );
      numCell2.text = toThousands(numCell2.text);
      addUnit(numCell2);
    } else if (numIndex === 2) {
      numCell3 = getCellFace(
        numItem,
        'justify-self-end w-fit text-6xl col-start-7'
      );
      numCell3.text = toThousands(numCell3.text);
      addUnit(numCell3);
    }
  });

  // props.agg_info.number_listのLength<3の場合に、Dummyの列情報を作る
  if (!numCell1) {
    numCell1 = { className: 'col-start-3' };
  }
  if (!numCell2) {
    numCell2 = { className: 'col-start-5' };
  }
  if (!numCell3) {
    numCell3 = { className: 'col-start-7' };
  }

  return (
    <>
      <Cell {...typeProp} />
      <Cell {...numCell1} />
      <Cell {...numCell2} />
      <Cell {...numCell3} />
    </>
  );
};

function addUnit(countObj) {
  if (countObj.text && countObj.text.endsWith('件')) {
    countObj.text = countObj.text.substring(countObj.text.length - 1);
  }

  countObj.unit = '件';
  countObj.unitStyle = { fontSize: '3.5rem' };
}

export default TotalFrequency;
