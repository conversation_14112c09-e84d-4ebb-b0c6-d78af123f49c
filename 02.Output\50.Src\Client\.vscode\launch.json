{
    // IntelliSense を使用して利用可能な属性を学べます。
    // 既存の属性の説明をホバーして表示します。
    // 詳細情報は次を確認してください: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "[localhost]Chromeデバッグ",
            "type": "node-terminal",
            "request": "launch",
            "command": "npm run start",
            "serverReadyAction": {
                "pattern": "started server on .+, url: (https?://.+)",
                "uriFormat": "%s",
                "action": "debugWithChrome"
            },
            "sourceMaps": true,
            "trace": true,
            "sourceMapPathOverrides": {
                "webpack:///./*": "${webRoot}/src/*"
            }
        }
    ]
}