package com.contents.common.db;

import com.vladmihalcea.hibernate.type.json.JsonType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.hibernate.annotations.TypeDefs;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * API履歴テーブル
 */
@Entity
@Data
@TypeDefs({
        @TypeDef(name = "json", typeClass = JsonType.class)
})
public class History implements Serializable {
    @Id
    @SequenceGenerator(sequenceName="history_table_sequence", name="history_table_sequence" )
    @GeneratedValue(strategy=GenerationType.SEQUENCE, generator="history_table_sequence")
    private Long id;

    @Column(length = 128)
    private String api_path;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    @Basic(fetch = FetchType.LAZY)
    private String api_body;

    private Integer api_status;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    @Basic(fetch = FetchType.LAZY)
    private String api_result;

    @Column(length = 128)
    private String client_info;

    private Date receive_time;
}
