package com.contents.external.monitor.lcd_un;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import javax.xml.bind.DatatypeConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.contents.common.external.initial.Display;
import com.contents.common.external.initial.DisplaySplit;
import com.contents.configuration.ExternalConfiguration;
import com.contents.configuration.ExternalConfiguration.HdmiInput;
import com.contents.manager.SocketClient;
import com.contents.manager.SocketManager;
import com.contents.pojo.Ping;

import lombok.extern.slf4j.Slf4j;

/**
 * 4面マルチモニタ制御<br>
 * LCD-UNシリーズ1
 */
@Slf4j
@Component
public class MultiMonitorControl {
	/* コマンドパケット ヘッダー 各要素位置 */
	public static final int HDR_IDX_SOH = 0;
	public static final int HDR_IDX_RESERVED = 1;
	public static final int HDR_IDX_DEST = 2;
	public static final int HDR_IDX_SOURCE = 3;
	public static final int HDR_IDX_MSG_TYPE = 4;
	public static final int HDR_IDX_MSG_LEN = 5;

	/* コマンドパケット ヘッダー サイズ */
	public static final int HDR_SIZE_MSG_LEN = 2;
	public static final int HDR_SIZE_ALL = 7;

	/* コマンドパケット ヘッダー 定数 */
	public static final byte HDR_SOH = 0x01;
	public static final byte HDR_RESERVED = 0x30;
	public static final byte HDR_MSG_TYPE_CMD = 0x41;
	public static final byte HDR_MSG_TYPE_CMD_REPLY = 0x42;
	public static final byte HDR_MSG_TYPE_GET_PARAM = 0x43;
	public static final byte HDR_MSG_TYPE_GET_PARAM_REPLY = 0x44;
	public static final byte HDR_MSG_TYPE_SET_PARAM = 0x45;
	public static final byte HDR_MSG_TYPE_SET_PARAM_REPLY = 0x46;
	public static final byte MSG_STX = 0x02;
	public static final byte MSG_ETX = 0x03;

	/* 簡単タイルマトリクス 設定値 */
	public static final int AUTO_TILE_MATRIX_H_MON_NUM = 2;
	public static final int AUTO_TILE_MATRIX_V_MON_NUM = 2;

	/* 各種送信禁止期間 設定値 */
	/** 入力切替コマンド実行後 コマンド送信禁止期間 (msec) */
	public static final long NO_SENDING_PERIOD_MSEC_INPUT_SW = 10 * 1000;
	/** 電源切替コマンド実行後 コマンド送信禁止期間 (msec) */
	public static final long NO_SENDING_PERIOD_MSEC_POWER_SW = 15 * 1000;

	/* PINGタイムアウト設定値 (sec) */
	private static final int PING_TIMEOUT = 1;
	/* ソケット 各種タイムアウト設定値 (msec) */
	private static final int CONNECT_TIMEOUT = 1000;
	private static final int RECEIVE_TIMEOUT = 10 * 1000;

	/**
	 * 入力 (モニターコマンド用)
	 */
	private enum Input {
		NO_MEAN(0), VGA(1), DVI(3), VIDEO(5), YGA(12), OPTION(13), DISPLAY_PORT1(15), DISPLAY_PORT2(16), HDMI1(17), HDMI2(18), HDMI3(130), MP(135), COMPUTE_MODULE(136);

		private final int id;

		Input(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	//	/**
	//	 * 電源状態 (モニターコマンド用)
	//	 */
	//	private enum PowerStatus {
	//		OFF(0), ON(1), POWER_SAVE(2), STANDBY(4), UNKNOWN(9);
	//
	//		private final int id;
	//
	//		PowerStatus(int id) {
	//			this.id = id;
	//		}
	//
	//		public int getId() {
	//			return id;
	//		}
	//
	//		public static PowerStatus valueOf(int id) {
	//			return Arrays.stream(PowerStatus.values())
	//					.filter(data -> data.getId() == id)
	//					.findFirst()
	//					.orElse(null);
	//		}
	//	}

	/**
	 * 入力ポート (モニターコマンド用)
	 */
	private enum InputPort {
		VGA_RGB(1), DVI(3), VIDEO(5), VGA_YPBPR(12), OPTION(13), DISPLAY_PORT1(15), DISPLAY_PORT2(16), HDMI1(17), HDMI2(18), HDMI3(130), MP(135), COMPUTE_MODULE(136);

		private final int id;

		InputPort(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	/**
	 * タイルマトリクス設定保存 (モニターコマンド用)
	 */
	private enum SaveTileSetting {
		COMMON(0), INPUT(1);

		private final int id;

		SaveTileSetting(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	/**
	 * モニター設定値変更戻り値
	 */
	private enum SetParamRetCode {
		NG(-1), PARAM_ERROR(-2), OK(1), SKIP(2);

		private final int id;

		SetParamRetCode(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	/** 外部設定情報　*/
	private final ExternalConfiguration extConf;
	//private final SocketManager socketMgr;

	/** コマンド送信禁止期間(ミリ秒)　(key: IP:Port 例)127.0.0.1:13000 Value:msec) */
	private final Map<String, Long> noSendingPeriodMsecMap = new HashMap<>();

	/** 接続状態MAP true:接続, false:未接続　(key:displayNo,displaySplitNo Value:isConnect) */
	private final Map<Integer, Map<Integer, Boolean>> connectStatusMap = new HashMap<>();

	@Autowired
	public MultiMonitorControl(ExternalConfiguration extConf) {
		this.extConf = extConf;
		//healthCheck();
	}

	//    @Autowired
	//    public MultiMonitorControl(ExternalConfiguration extConf, SocketManager socketMgr) {
	//        this.extConf = extConf;
	//        this.socketMgr = socketMgr;
	//        healthCheck();
	//    }

	/**
	 * 接続状態
	 * @return 接続状態 true:全接続OK, false:いずれかの接続がNG or 接続先が無い
	 */
	public boolean isConnect() {
		boolean retIsConnect = true;
		for (Map.Entry<Integer, Map<Integer, Boolean>> entry : this.connectStatusMap.entrySet()) {
			if (retIsConnect) {
				for (Map.Entry<Integer, Boolean> connect : entry.getValue().entrySet()) {
					// 未接続状態があればfalseにしてループを抜ける
					if (!connect.getValue()) {
						retIsConnect = false;
						break;
					}
				}
			} else {
				// ひとつでも未接続状態があればループを抜ける
				break;
			}
		}

		// 接続先がない場合もfalseを返す
		return (!this.connectStatusMap.isEmpty()) && retIsConnect;
	}

	/**
	 * 定期監視
	 */
	public void healthCheck() {
		log.info("Health check start.");

		// 表示盤情報
		List<Display> displayList = extConf.getAllDisplay(extConf.DISPLAY_TYPE_MULTI_MONITOR);
		for (Display display : displayList) {
			// 画面分割(面)情報
			List<DisplaySplit> displaySplitList = extConf.getDisplaySplitList(display);
			boolean isConnect;
			for (DisplaySplit displaySplit : displaySplitList) {
				// ping送信
				Ping ping = new Ping(displaySplit.getIpAddress(), PING_TIMEOUT);
				if (ping.ping()) {
					// 自己診断ステータス取得コマンド送信
					isConnect = sendGetSelfDiagStatus(displaySplit);

				} else {
					log.error("Health check ping error. ip={}", displaySplit.getIpAddress());
					isConnect = false;
				}

				// 接続状態MAP 更新
				Map<Integer, Boolean> connect = this.connectStatusMap.get(display.getDisplayNo());
				if (connect != null) {
					// displaySplitNoのMAPが既にあれば上書き (or 追加)
					connect.put(displaySplit.getDisplaySplitNo(), isConnect);
				} else {
					// displaySplitNoのMAPが無ければMAPを新規作成
					Map<Integer, Boolean> newConnect = new HashMap<>();
					newConnect.put(displaySplit.getDisplaySplitNo(), isConnect);
					this.connectStatusMap.put(display.getDisplayNo(), newConnect);
				}
			}
		}
		// 結果ログ出力
		this.connectStatusMap.forEach((dispNo, connect) -> connect.forEach((dispSplitNo, isConnect) -> log
				.info("displayNo={}, displaySplitNo={}, isConnect={}", dispNo, dispSplitNo, isConnect)));

		log.info("Health check end.");
	}

	/**
	 * 自己診断ステータス取得コマンド送信 (Self-diagnosis status read command)
	 * @param displaySplit 画面分割(面)情報
	 * @return コマンド応答結果 true:正常応答, false:送信エラー or エラー応答 or 機器異常
	 */
	private boolean sendGetSelfDiagStatus(DisplaySplit displaySplit) {
		boolean ret;
		// Socket接続
		SocketClient socket = SocketManager.getSocketClient(displaySplit.getIpAddress(), displaySplit.getPort());
		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
			// Self-diagnosis status read command
			byte[] message = new byte[] { MSG_STX,
					'B', '1', // Command code
					MSG_ETX };
			int monitorId = displaySplit.getDisplaySplitNo() + 1;

			// 受信処理
			AtomicBoolean isError = new AtomicBoolean(false);
			Consumer<byte[]> callback = (msg) -> {
				// Message の簡易チェック
				if (msg[0] == MSG_STX) {
					// Message の セルフテストの結果 を取得
					String result = new String(Arrays.copyOfRange(msg, 3, (msg.length - 1)));
					for (int i = 0; i < result.length(); i += 2) {
						String st = result.substring(i, (i + 2)); // セルフテストの結果
						if (!("00".equals(st))) {
							String detail = switch (st) {
							case "70" -> "スタンバイ電源+3.3V 異常";
							case "71" -> "スタンバイ電源+5V 異常";
							case "72" -> "パネル電源+12V 異常";
							case "78" -> "インバータ電源/オプション・スロット２電源+24V 異常";
							case "80" -> "冷却ファン-1 異常";
							case "81" -> "冷却ファン-2 異常";
							case "82" -> "冷却ファン-3 異常";
							case "83" -> "COMPUTE MODULE 冷却ファン異常";
							case "90" -> "インバータ異常";
							case "91" -> "LED バックライト異常";
							case "A0" -> "温度異常– シャットダウン";
							case "A1" -> "温度異常– 輝度低下";
							case "A2" -> "Sensor がユーザ指定温度に達した";
							case "B0" -> "NO SIGNAL";
							case "D0" -> "Proof of Play ログメモリ減少";
							case "D1" -> "RTC エラー";
							case "E0" -> "システムエラー";
							default -> "不明なエラー";
							};
							log.error("Self-diagnosis error. st={}({})", detail, st);
							isError.set(true);
						}
					}
				} else {
					log.error("Failed to receive message.");
					isError.set(true);
				}
			};

			// コマンド送信
			boolean sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, callback);
			socket.close();

			ret = (sendResult && !(isError.get()));

		} else {
			log.error("Socket connection error.");
			ret = false;
		}

		return ret;
	}

	/**
	 * 入力切替
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @param inputSw 入力切替
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean selectInput(int displayNo, int displaySplitNo, int inputSw) {
		log.info("selectInput(): displayNo={}, displaySplitNo={}, inputSw={}", displayNo, displaySplitNo, inputSw);

		// 画面分割(面)情報 取得
		DisplaySplit displaySplit = extConf.getDisplaySplit(
				extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_MULTI_MONITOR), displaySplitNo);

		if (displaySplit != null) {
			Input input = null;
			HdmiInput target = HdmiInput.valueOf(inputSw);
			String targetName = (target != null) ? target.toString() : null;
			// 入力切替先をモニタ情報から取得
			if (Objects.equals(displaySplit.getHdmiInput1(), targetName)) {

				input = Input.DVI;
			} else if (Objects.equals(displaySplit.getHdmiInput2(), targetName)) {
				//input = Input.HDMI2;
				input = Input.HDMI1;
			}

			// 入力切替先が見つかればモニタに入力切替コマンドを送信
			if (input != null) {
				// 入力切替
				String ip = displaySplit.getIpAddress();
				int port = displaySplit.getPort();
				String opCodePage = "00";
				String opCode = "60";
				int setValue = input.getId();

				SetParamRetCode result = setMonitorParam(displaySplitNo, ip, port, opCodePage, opCode, setValue);
				if (result == SetParamRetCode.NG || result == SetParamRetCode.PARAM_ERROR) {
					log.error("Monitor parameter setting error. ip={}, port={}", ip, port);
					return false;

				} else if (result == SetParamRetCode.OK) {
					// コマンド送信禁止期間の設定
					String key = ip + ":" + port; // keyの組立 (例)127.0.0.1:13000
					long msec = System.currentTimeMillis() + NO_SENDING_PERIOD_MSEC_INPUT_SW;
					this.noSendingPeriodMsecMap.put(key, msec);
				}
			} else {
				// 入力切替先が見つからない
				log.warn("Input switch destination not found. displayNo={}, displaySplitNo={}, inputSw={}",
						displayNo, displaySplitNo, inputSw);
			}
		} else {
			// 対象のモニタが見つからない場合はログ出力して正常終了
			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
		}
		return true;
	}

	//	/**
	//	 * 電源状態取得
	//	 * @param displayNo 表示盤番号
	//	 * @param displaySplitNo 画面分割番号
	//	 * @return 処理結果 true:成功, false:失敗
	//	 */
	//	public PowerStatus getPowerStatus(int displayNo, int displaySplitNo) {
	//		log.info("getPowerStatus(): displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
	//
	//		PowerStatus powerStatus = PowerStatus.UNKNOWN;
	//
	//		// 画面分割(面)情報 取得
	//		DisplaySplit displaySplit = extConf.getDisplaySplit(
	//				extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_MULTI_MONITOR), displaySplitNo);
	//
	//		if (displaySplit != null) {
	//			String ip = displaySplit.getIpAddress();
	//			int port = displaySplit.getPort();
	//
	//			powerStatus = powerStatusRead(displaySplitNo, ip, port);
	//		} else {
	//			// 対象のモニタが見つからない場合はログ出力して正常終了
	//			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
	//		}
	//		log.info("Power status: {}({})", powerStatus.toString(), powerStatus.getId());
	//		return powerStatus;
	//	}

	//	/**
	//	 * 電源切替
	//	 * @param displayNo 表示盤番号
	//	 * @param displaySplitNo 画面分割番号
	//	 * @param powerStatus 電源設定
	//	 * @return 処理結果 true:成功, false:失敗
	//	 */
	//	public boolean switchPower(int displayNo, int displaySplitNo, PowerStatus powerStatus) {
	//		log.info("switchPower(): displayNo={}, displaySplitNo={}, powerStatus={}", displayNo, displaySplitNo, powerStatus.toString());
	//		boolean ret;
	//
	//		// 画面分割(面)情報 取得
	//		DisplaySplit displaySplit = extConf.getDisplaySplit(extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_MULTI_MONITOR), displaySplitNo);
	//
	//		if (displaySplit != null) {
	//			String ip = displaySplit.getIpAddress();
	//			int port = displaySplit.getPort();
	//
	//			PowerStatus curtPowerStatus = powerStatusRead(displaySplitNo, ip, port);
	//			if (powerStatus.getId() == curtPowerStatus.getId()) {
	//				// 既に目的の電源設定となっている場合は電源設定せずに正常終了
	//				return true;
	//			}
	//
	//			ret = powerControl(displaySplitNo, ip, port, powerStatus);
	//			if (ret) {
	//				// コマンド送信禁止期間の設定
	//				String key = ip + ":" + port; // keyの組立 (例)127.0.0.1:13000
	//				long msec = System.currentTimeMillis() + NO_SENDING_PERIOD_MSEC_POWER_SW;
	//				this.noSendingPeriodMsecMap.put(key, msec);
	//			}
	//		} else {
	//			// 対象のモニタが見つからない場合はログ出力して正常終了
	//			log.info("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
	//			ret = true;
	//		}
	//		return ret;
	//	}

	/**
	 * タイルマトリクス設定
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @param toggle タイルマトリクス実行 (true:実行, false:解除)
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean setTileMatrix(int displayNo, int displaySplitNo, boolean toggle) {
		log.info("setTileMatrix(): displayNo={}, displaySplitNo={}, toggle={}", displayNo, displaySplitNo, toggle);

		// 画面分割(面)情報 取得
		DisplaySplit displaySplit = extConf.getDisplaySplit(extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_MULTI_MONITOR), displaySplitNo);
		if (displaySplit != null) {
			// タイルマトリクス 実行
			String ip = displaySplit.getIpAddress();
			int port = displaySplit.getPort();
			String opCodePage = "02";
			String opCode = "D3";
			int setValue = (toggle) ? 2 : 1; // 2:実行する, 1:実行しない

			SetParamRetCode result = setMonitorParam(displaySplitNo, ip, port, opCodePage, opCode, setValue);
			if (result == SetParamRetCode.NG || result == SetParamRetCode.PARAM_ERROR) {
				log.error("Monitor parameter setting error. ip={}, port={}", ip, port);
				return false;
			}
		} else {
			// 対象のモニタが見つからない場合はログ出力して正常終了
			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
		}
		return true;
	}

	/**
	 * 簡単タイルマトリクス設定
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @param hMonitorNum 水平モニター数
	 * @param vMonitorNum 垂直モニター数
	 * @param inputSw 入力切替
	 * @param saveTileSetting タイルマトリクス設定保存
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean setAutoTileMatrix(int displayNo, int displaySplitNo, int hMonitorNum, int vMonitorNum, int inputSw, SaveTileSetting saveTileSetting) {
		log.info("setAutoTileMatrix(): displayNo={}, displaySplitNo={}," +
				" hMonitorNum={}, vMonitorNum={}, inputSw={}, saveTileSetting={}",
				displayNo, displaySplitNo,
				hMonitorNum, vMonitorNum, inputSw, saveTileSetting);
		boolean ret;

		// 画面分割(面)情報 取得
		DisplaySplit displaySplit = extConf.getDisplaySplit(
				extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_MULTI_MONITOR), displaySplitNo);

		if (displaySplit != null) {
			byte[] message;
			InputPort inputPort = null;
			int monitorId = displaySplitNo + 1; // モニターIDは 面番号 + 1 (運用仕様)

			// 入力切替先が不明な場合はマトリクススイッチャーとする
			HdmiInput hdmiInput = HdmiInput.valueOf(inputSw);
			String inputSwName = (hdmiInput != null) ? hdmiInput.toString() : HdmiInput.MATRIX_SW.toString();

			// 入力切替先をモニタ情報から取得
			if (Objects.equals(displaySplit.getHdmiInput1(), inputSwName)) {
				inputPort = InputPort.DVI;
			} else if (Objects.equals(displaySplit.getHdmiInput2(), inputSwName)) {
				inputPort = InputPort.HDMI1;
			}

			if (inputPort != null) {
				AtomicBoolean rcevResult = new AtomicBoolean(false);

				// 対象ディスプレイのソケットを用意
				SocketClient socket = SocketManager.getSocketClient(
						displaySplit.getIpAddress(),
						displaySplit.getPort());
				if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
					// Auto Tile Matrix Execute command
					message = new byte[] { MSG_STX,
							'C', 'A', '0', '3', // “Auto Tile Matrix” コマンド
							'0', '1', // Execution
							0x00, 0x00, // 水平モニター数
							0x00, 0x00, // 垂直モニター数
							'0', '1', // パターンID ('01'固定)
							0x00, 0x00, // 現在の入力端子
							0x00, 0x00, // タイルマトリクス設定保存
							MSG_ETX };
					// 水平モニター数
					byte[] hMonNumBytes = String.format("%02X", hMonitorNum).getBytes();
					System.arraycopy(hMonNumBytes, 0, message, 7, hMonNumBytes.length);
					// 垂直モニター数
					byte[] vMonNumBytes = String.format("%02X", vMonitorNum).getBytes();
					System.arraycopy(vMonNumBytes, 0, message, 9, vMonNumBytes.length);
					// 現在の入力端子
					byte[] inputPortBytes = String.format("%02X", inputPort.getId()).getBytes();
					System.arraycopy(inputPortBytes, 0, message, 13, inputPortBytes.length);
					// タイルマトリクス設定保存
					byte[] saveTileSettingBytes = String.format("%02X", saveTileSetting.getId()).getBytes();
					System.arraycopy(saveTileSettingBytes, 0, message, 15, saveTileSettingBytes.length);

					// 受信処理
					Consumer<byte[]> callback = (msg) -> {
						// Message の簡易チェック
						if (msg[0] == MSG_STX) {
							// Message の Result を取得
							String resultHex = new String(
									Arrays.copyOfRange(msg, 7, 9));
							int result = Integer.parseInt(resultHex, 16);
							if (result == 0) {
								// 正常
								rcevResult.set(true);

							} else {
								log.error("The message result code is an error. result={}", result);
								rcevResult.set(false);
							}
						} else {
							log.error("Failed to receive message.");
							rcevResult.set(false);
						}
					};
					boolean sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, callback);
					ret = (sendResult && rcevResult.get());
					socket.close();
				} else {
					log.error("Socket connection error.");
					ret = false;
				}
			} else {
				// 入力切替先が見つからない
				log.warn("Input switch destination not found. displayNo={}, displaySplitNo={}, inputSw={}",
						displayNo, displaySplitNo, inputSw);
				ret = true;
			}
		} else {
			// 対象のモニタが見つからない場合はログ出力して正常終了
			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
			ret = true;
		}
		return ret;
	}

	/**
	 * 簡単タイルマトリクス解除
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public boolean resetAutoTileMatrix(int displayNo, int displaySplitNo) {
		log.info("resetAutoTileMatrix(): displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
		boolean ret;

		// 画面分割(面)情報 取得
		DisplaySplit displaySplit = extConf.getDisplaySplit(
				extConf.getDisplay(displayNo, extConf.DISPLAY_TYPE_MULTI_MONITOR), displaySplitNo);

		if (displaySplit != null) {
			byte[] message;
			int monitorId = displaySplitNo + 1;
			AtomicBoolean rcevResult = new AtomicBoolean(false);

			// 対象ディスプレイのソケットを用意
			SocketClient socket = SocketManager.getSocketClient(
					displaySplit.getIpAddress(),
					displaySplit.getPort());
			if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
				// Auto Tile Matrix Reset command
				message = new byte[] { MSG_STX,
						'C', 'A', '0', '3', // Auto Tile Matrix
						'0', '6', // Off
						MSG_ETX };

				// 受信処理
				Consumer<byte[]> callback = (msg) -> {
					// Message の簡易チェック
					if (msg[0] == MSG_STX) {
						// Message の Result を取得
						String resultHex = new String(
								Arrays.copyOfRange(msg, 7, 9));
						int result = Integer.parseInt(resultHex, 16);
						if (result == 0) {
							// 正常
							rcevResult.set(true);

						} else {
							log.error("The message result code is an error. result={}", result);
							rcevResult.set(false);
						}
					} else {
						log.error("Failed to receive message.");
						rcevResult.set(false);
					}
				};
				boolean sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, callback);
				ret = (sendResult && rcevResult.get());
				socket.close();
			} else {
				log.error("Socket connection error.");
				ret = false;
			}
		} else {
			// 対象のモニタが見つからない場合はログ出力して正常終了
			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
			ret = true;
		}
		return ret;
	}

	/**
	 * モニター設定値変更
	 * @param displaySplitNo 画面分割番号
	 * @param ip 接続先IP
	 * @param port 接続先ポート番号
	 * @param opCodePage オペレーションコードのページ (モニター設定値の場所)
	 * @param opCode オペレーションコード (モニター調整値の場所)
	 * @param setValue 設定値
	 * @return 処理結果 true:成功, false:失敗
	 */
	private SetParamRetCode setMonitorParam(int displaySplitNo, String ip, int port, String opCodePage, String opCode, int setValue) {
		byte[] message;
		int monitorId = displaySplitNo + 1; // モニターIDは 面番号 + 1 (運用仕様)
		AtomicBoolean rcevResult = new AtomicBoolean(false);
		AtomicInteger rcevValue = new AtomicInteger(-1);

		if (opCodePage.length() != 2) {
			log.error("Parameter error. opCodePage={}", opCodePage);
			return SetParamRetCode.PARAM_ERROR;
		}
		if (opCode.length() != 2) {
			log.error("Parameter error. opCode={}", opCode);
			return SetParamRetCode.PARAM_ERROR;
		}
		if (setValue < 0 || 9999 < setValue) {
			log.error("Parameter error. setValue={}", setValue);
			return SetParamRetCode.PARAM_ERROR;
		}

		// "Get parameter" reply
		// "Set parameter" reply
		Consumer<byte[]> callback = (msg) -> {
			// Message の簡易チェック
			if (msg[0] == MSG_STX) {
				// Message の Result を取得
				String resultHex = new String(
						Arrays.copyOfRange(msg, 1, 3));
				int result = Integer.parseInt(resultHex, 16);
				if (result == 0) {
					// 正常
					rcevResult.set(true);

					// Message の Value を取得
					String valueHex = new String(
							Arrays.copyOfRange(msg, 13, 17));
					int value = Integer.parseInt(valueHex, 16);
					rcevValue.set(value);

				} else {
					log.error("The message result code is an error. result={}", result);
					rcevResult.set(false);
				}
			} else {
				log.error("Failed to receive message.");
				rcevResult.set(false);
			}
		};

		// 対象ディスプレイのソケットを用意
		SocketClient socket = SocketManager.getSocketClient(ip, port);
		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
			// Get current parameter
			message = new byte[] { MSG_STX,
					opCodePage.getBytes()[0], opCodePage.getBytes()[1], // OP code page
					opCode.getBytes()[0], opCode.getBytes()[1], // OP code
					MSG_ETX };
			boolean sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_GET_PARAM, message, callback);
			if (!sendResult || !rcevResult.get()) {
				socket.close();
				return SetParamRetCode.NG;
			}
			if (setValue == rcevValue.get()) {
				// 設定値が既にモニタに設定済み
				log.info("Parameter setting is skipped because the setting value has already been set." +
						" opCodePage={}, opCode={}, setValue={}",
						opCodePage, opCode, setValue);
				socket.close();
				return SetParamRetCode.SKIP;
			}

			// Set parameter
			message = new byte[] { MSG_STX,
					opCodePage.getBytes()[0], opCodePage.getBytes()[1], // OP code page
					opCode.getBytes()[0], opCode.getBytes()[1], // OP code
					0x00, 0x00, 0x00, 0x00, // Set value
					MSG_ETX };
			// Set value
			byte[] inputBytes = String.format("%04X", setValue).getBytes();
			System.arraycopy(inputBytes, 0, message, 5, inputBytes.length);
			sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_SET_PARAM, message, callback);
			if (!sendResult || !rcevResult.get()) {
				socket.close();
				return SetParamRetCode.NG;
			}
			log.info("Parameter setting completed." +
					" opCodePage={}, opCode={}, setValue={}, rcevValue={}",
					opCodePage, opCode, setValue, rcevValue.get());

			// Save Current Setting command
			message = new byte[] { MSG_STX,
					'0', 'C', // Command code
					MSG_ETX };
			sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, null);
			if (!sendResult) {
				socket.close();
				return SetParamRetCode.NG;
			}

			socket.close();

		} else {
			log.error("Socket connection error.");
			return SetParamRetCode.NG;
		}

		return SetParamRetCode.OK;
	}

	//	/**
	//	 * 電源状態取得 (モニタ通信処理)
	//	 * @param displaySplitNo 画面分割番号
	//	 * @param ip 接続先IP
	//	 * @param port 接続先ポート番号
	//	 * @return PowerStatus 電源状態
	//	 */
	//	private PowerStatus powerStatusRead(int displaySplitNo, String ip, int port) {
	//		byte[] message;
	//		int monitorId = displaySplitNo + 1;
	//
	//		// power status (受信コールバックから更新するためオブジェクト参照型としている)
	//		AtomicReference<PowerStatus> pSts = new AtomicReference<>(PowerStatus.UNKNOWN);
	//
	//		// 対象ディスプレイのソケットを用意
	//		SocketClient socket = SocketManager.getSocket(ip, port);
	//		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
	//			// Power status read command
	//			message = new byte[] { MSG_STX,
	//					'0', '1', 'D', '6', // “Get power status” コマンド
	//					MSG_ETX };
	//
	//			// 受信処理
	//			Consumer<byte[]> callback = (msg) -> {
	//				// Message の簡易チェック
	//				if (msg[0] == MSG_STX) {
	//					// Message の Result を取得
	//					String resultHex = new String(
	//							Arrays.copyOfRange(msg, 3, 5));
	//					int result = Integer.parseInt(resultHex, 16);
	//					if (result == 0) {
	//						// 正常
	//						// Message から Power status (4桁) を取得
	//						String powerStatusHex = new String(
	//								Arrays.copyOfRange(msg, 13, 17)); // power status 4桁 取得
	//						int powerStatus = Integer.parseInt(powerStatusHex, 16);
	//						pSts.set(PowerStatus.valueOf(powerStatus));
	//
	//					} else {
	//						log.error("The message result code is an error. result={}", result);
	//					}
	//				} else {
	//					log.error("Failed to receive message.");
	//				}
	//			};
	//			sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, callback);
	//			socket.close();
	//		} else {
	//			log.error("Socket connection error.");
	//		}
	//		return pSts.get();
	//	}
	//
	//	/**
	//	 * 電源設定 (モニタ通信処理)
	//	 * @param displaySplitNo 画面分割番号
	//	 * @param ip 接続先IP
	//	 * @param port 接続先ポート番号
	//	 * @param setPowerStatus 設定する電源状態
	//	 * @return 処理結果 true:成功, false:失敗
	//	 */
	//	private boolean powerControl(int displaySplitNo, String ip, int port, PowerStatus setPowerStatus) {
	//		boolean ret;
	//		byte[] message;
	//		int monitorId = displaySplitNo + 1;
	//		AtomicBoolean rcevResult = new AtomicBoolean(false);
	//
	//		// 対象ディスプレイのソケットを用意
	//		SocketClient socket = SocketManager.getSocket(ip, port);
	//		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
	//
	//			// Power control command
	//			message = new byte[] { MSG_STX,
	//					'C', '2', '0', '3', 'D', '6', // “Power control” コマンド
	//					0x00, 0x00, 0x00, 0x00, // Power status
	//					MSG_ETX };
	//			// Power status
	//			byte[] powerStatusBytes = String.format("%04X", setPowerStatus.getId()).getBytes();
	//			System.arraycopy(powerStatusBytes, 0, message, 7, powerStatusBytes.length);
	//
	//			// 受信処理
	//			Consumer<byte[]> powerControlCallback = (msg) -> {
	//				// Message の簡易チェック
	//				if (msg[0] == MSG_STX) {
	//					// Message の Result を取得
	//					String resultHex = new String(Arrays.copyOfRange(msg, 3, 5));
	//					int result = Integer.parseInt(resultHex, 16);
	//					if (result == 0) {
	//						// 正常
	//						rcevResult.set(true);
	//
	//					} else {
	//						log.error("The message result code is an error. result={}", result);
	//						rcevResult.set(false);
	//					}
	//				} else {
	//					log.error("Failed to receive message.");
	//					rcevResult.set(false);
	//				}
	//			};
	//
	//			boolean sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, powerControlCallback);
	//			ret = (sendResult && rcevResult.get());
	//			socket.close();
	//
	//		} else {
	//			log.error("Socket connection error.");
	//			ret = false;
	//		}
	//		return ret;
	//	}

	/**
	 * メッセージ送信
	 * @param socket Open済みのSocket
	 * @param monitorId Header用モニターID
	 * @param messageType Header用メッセージタイプ
	 * @param message 送信メッセージ
	 * @param callback 受信コールバック
	 * @return 処理結果 true:成功, false:失敗
	 */
	private synchronized boolean sendMessage(
			SocketClient socket, int monitorId, byte messageType, byte[] message, Consumer<byte[]> callback) {

		// コマンド送信禁止期間中は残り時間待機してから送信処理を実行
		String key = socket.getIp() + ":" + socket.getPort(); // keyの組立 (例)127.0.0.1:13000
		Long msec = this.noSendingPeriodMsecMap.get(key);
		if (msec != null) {
			long waitTime = msec - System.currentTimeMillis();
			waitTime = (waitTime < 0) ? 0 : waitTime;
			if (waitTime > 0) {
				log.info("Wait {} milliseconds before sending. Attempted to send while transmission is inhibited.",
						waitTime);
				try {
					Thread.sleep(waitTime);
				} catch (InterruptedException e) {
					log.error("An interrupt occurred while waiting for a message to be sent.");
					log.error(e.getMessage());
					return false;
				}
			}
			this.noSendingPeriodMsecMap.remove(key);
		}

		// Header の作成
		byte destination = (byte) (monitorId + 0x40);
		byte[] header = {
				HDR_SOH, // SOH
				HDR_RESERVED, // Reserved ('0' 固定)
				destination, // Destination
				'0', // Source (コントローラは '0' 固定)
				messageType, // Message Type
				0x00, 0x00 }; // Message Length
		// Message Length
		byte[] lengthBytes = String.format("%02X", message.length).getBytes();
		System.arraycopy(lengthBytes, 0, header, HDR_IDX_MSG_LEN, lengthBytes.length);

		// Check code の作成
		byte cc = 0x00;
		// header
		byte[] ccHeader = new byte[HDR_SIZE_ALL - 1];
		System.arraycopy(header, 1, ccHeader, 0, ccHeader.length);
		for (byte b : ccHeader) {
			cc = (byte) (cc ^ b);
		}
		// Message
		for (byte b : message) {
			cc = (byte) (cc ^ b);
		}

		// Delimiter の作成
		byte delimiter = 0x0D;

		// 送信データの組立
		int dataLength = header.length + message.length; // Check code, Delimiter を抜いた送信データ長
		byte[] sendData = new byte[dataLength + 2]; // Check code, Delimiter を含めた長さを確保
		System.arraycopy(header, 0, sendData, 0, header.length);
		System.arraycopy(message, 0, sendData, header.length, message.length);
		sendData[dataLength] = cc;
		sendData[dataLength + 1] = delimiter;

		// 受信処理
		Consumer<byte[]> recvProc = (recvBytes) -> {
			log.info("recvData: " + DatatypeConverter.printHexBinary(recvBytes));

			// ヘッダーの簡易チェック
			if ((recvBytes.length >= HDR_SIZE_ALL) && (recvBytes[HDR_IDX_SOH] == HDR_SOH)) {
				// Header から Message length を取得
				String msgLenHex = new String(
						Arrays.copyOfRange(recvBytes, HDR_IDX_MSG_LEN, HDR_IDX_MSG_LEN + HDR_SIZE_MSG_LEN));
				int msgLen = Integer.parseInt(msgLenHex, 16);

				if (msgLen > 0) {
					// message部のみを取得
					byte[] recvMessage = new byte[msgLen];
					System.arraycopy(recvBytes, HDR_SIZE_ALL, recvMessage, 0, msgLen);

					// 受信コールバックの呼び出し
					if (callback != null) {
						callback.accept(recvMessage);
					}

				} else {
					log.error("Message is empty.");
				}
			} else {
				log.error("Failed to receive headers.");
			}
		};

		// 送信
		log.info("★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★★");
		log.info(DatatypeConverter.printHexBinary(sendData));
		return socket.send(sendData, recvProc);
	}
}
