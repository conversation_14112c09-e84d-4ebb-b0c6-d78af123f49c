# VehicleLargeQuarter 集成测试计划

## 测试目标
验证 VehicleLargeQuarter.js 组件在四等分显示模式下的完整功能，包括：
- SourceNo=18 的正确识别和路由
- sourceDispPattern=1 的条件判断
- 50行数据的12行分组显示
- 服务端数据转换的正确性

## 测试环境设置

### 1. 服务端测试数据
```json
{
  "source_no": 18,
  "source_split_no": 1,
  "source_disp_pat": 1,
  "source_data": {
    "is_deployment": 1,
    "title_name": [
      {
        "display_text": "消防署",
        "text_color": "#FFFFFF",
        "background_color": "#FF0000",
        "car_name": [
          {
            "display_text": "救急車1",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          },
          {
            "display_text": "救急車2", 
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          }
        ],
        "town_name": [
          {
            "display_text": "中央区",
            "text_color": "#000000", 
            "background_color": "#FFFFFF"
          },
          {
            "display_text": "西区",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          }
        ],
        "disaster_type": [
          {
            "display_text": "火災",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          },
          {
            "display_text": "救急",
            "text_color": "#000000", 
            "background_color": "#FFFFFF"
          }
        ],
        "avm_dynamic_state": [
          {
            "display_text": "出動中",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          },
          {
            "display_text": "待機中",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          }
        ],
        "deployment": [
          {
            "display_text": "配備",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          },
          {
            "display_text": "非配備",
            "text_color": "#000000",
            "background_color": "#FFFFFF"
          }
        ],
        "lighting_setting": [
          {
            "lighting_status": 1,
            "blink_interval": 500
          },
          {
            "lighting_status": 3,
            "blink_interval": 1000
          }
        ]
      }
    ]
  }
}
```

## 测试用例

### TC001: SourceNo=18 路由测试
**目的**: 验证 SplitScreen.js 正确识别 SourceNo=18
**步骤**:
1. 发送 SourceNo=18, sourceDispPattern=1 的数据
2. 验证 VehicleLargeQuarter 组件被调用
3. 发送 SourceNo=18, sourceDispPattern=0 的数据  
4. 验证 VehicleLarge 组件被调用

**期待结果**: 
- sourceDispPattern=1 时渲染 VehicleLargeQuarter
- sourceDispPattern≠1 时渲染 VehicleLarge

### TC002: 服务端数据转换测试
**目的**: 验证 SystemService.convertVehicleSourceData() 正确处理 SourceNo=18
**步骤**:
1. 发送标准 VehicleContent 结构数据 (SourceNo=18)
2. 验证服务端转换为 VehicleLargeQuarterContent 结构
3. 检查 groupId 和 columnPosition 设置
4. 验证 12行分组逻辑

**期待结果**:
- title_name 结构转换为 items 扁平结构
- 每组包含最多12行数据
- groupId 和 columnPosition 正确设置

### TC003: 四等分显示布局测试
**目的**: 验证组件在四等分模式下的布局
**步骤**:
1. 设置 sourceDispPattern=1, column_position='left'
2. 验证网格类: grid-cols-1 grid-rows-12
3. 设置 column_position='right'
4. 验证右侧列布局
5. 验证字体大小: text-[48px]

**期待结果**:
- 左右列正确应用不同的 padding
- 12行网格布局正确显示
- 字体大小适合四等分显示

### TC004: Tailwind CSS 配置测试
**目的**: 验证新增的 quarter-extended-vehicle 配置
**步骤**:
1. 检查 tailwind.config.js 包含新配置
2. 验证 quarter-extended-vehicle-deploy 网格
3. 验证 quarter-extended-vehicle-nodeploy 网格
4. 测试配备/非配备模式切换

**期待结果**:
- 48px 列宽正确应用
- 配备/非配备模式布局差异正确

### TC005: 数据完整性测试
**目的**: 验证50行数据的完整处理
**步骤**:
1. 发送包含50行车辆数据的测试数据
2. 验证数据分组为5组（每组12行，最后一组2行）
3. 测试每组的独立显示
4. 验证数据不丢失

**期待结果**:
- 50行数据完整处理
- 分组逻辑正确
- 最后一组处理正确（不足12行）

### TC006: 点滅功能测试
**目的**: 验证点滅功能在新组件中正常工作
**步骤**:
1. 设置 lighting_status=3 的测试数据
2. 验证 BlinkBlock 组件正确调用
3. 测试背景色点滅效果
4. 验证点滅间隔设置

**期待结果**:
- 点滅功能正常工作
- 背景色正确应用
- 点滅间隔符合设置

## 回归测试

### RT001: 既存功能影响确认
**目的**: 确认新功能不影响既存系统
**步骤**:
1. 测试 SourceNo=1 (Vehicle.js) 正常工作
2. 测试 SourceNo=1 + sourceDispPattern=1 (CustomVehicle.js) 正常工作
3. 测试 SourceNo=18 + sourceDispPattern≠1 (VehicleLarge.js) 正常工作
4. 测试其他 SourceNo 的内容正常工作

**期待结果**:
- 所有既存功能无影响
- 性能无明显劣化

## 测试执行方法

### 单元测试
```bash
cd Client
npm test VehicleLargeQuarter.test.js
```

### 集成测试
```bash
# 启动服务端
cd Server
./gradlew bootRun

# 启动客户端
cd Client  
npm start

# 发送测试数据
curl -X POST http://localhost:8080/content \
  -H "Content-Type: application/json" \
  -d @test-data.json
```

### 验证方法
1. 浏览器开发者工具检查DOM结构
2. 网络标签页确认数据传输
3. 控制台检查错误信息
4. 视觉确认显示效果

## 成功判定基准
- [ ] 所有测试用例通过
- [ ] 回归测试无问题
- [ ] 性能测试满足要求
- [ ] 视觉测试符合设计规格
- [ ] 代码审查通过
