openapi: 3.0.3
info:
  title: コンテンツAPのAPI
  description: SwaggerのYaml形式で記述
  termsOfService: http://swagger.io/terms/
  version: 0.0.7
externalDocs:
  description: Find out more about Swagger
  url: http://swagger.io
servers:
  - url: https://localhost:8088/contentserver
paths:
  /content:
    post:
      tags:
        - 汎用的なコンテンツAPI
      summary: コンテンツ更新
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    display_content_data:
                      type: array
                      items:
                        type: object
                        properties:
                          source_no:
                            type: integer
                            format: int32
                            description: 表示盤に表示するソース番号
                            minimum: 0
                          source_name:
                            type: string
                            description: ソース番号に紐づく名称
                          source_split_no:
                            type: integer
                            format: int32
                            description: '同一のソース番号(コンテンツ)を分割した番号 例：事案や気象情報など複数コンテンツ表示可能な場合に設定'
                            minimum: 0
                          source_data:
                            anyOf:
                              - $ref: '#/components/schemas/VehicleContent'
                              - $ref: '#/components/schemas/DeploymentContent'
                              - $ref: '#/components/schemas/CaseContent'
                              - $ref: '#/components/schemas/CaseHalfContent'
                              - $ref: '#/components/schemas/CaseQuarterContent'
                              - $ref: '#/components/schemas/IncomingCallAContent'
                              - $ref: '#/components/schemas/IncomingCallBContent'
                              - $ref: '#/components/schemas/WeatherContent'
                              - $ref: '#/components/schemas/TotalFrequencyContent'
                              - $ref: '#/components/schemas/AlarmContent'
                              - $ref: '#/components/schemas/AttendanceContent'
                              - $ref: '#/components/schemas/DoctorOnDutyContent'
                              - $ref: '#/components/schemas/ScheduleContent'
                              - $ref: '#/components/schemas/HandoverContent'
                              - $ref: '#/components/schemas/DigitalRadioContent'
                              - $ref: '#/components/schemas/AmbulanceRateContent'                              
            examples:
              VehicleDeploy:
                $ref: '#/components/examples/VehicleExampleDeploy'
              VehicleExampleDeployMoreDetail:
                $ref: '#/components/examples/VehicleExampleDeployMoreDetail'
              VehicleNoDeploy:
                $ref: '#/components/examples/VehicleExampleNoDeploy'
              DeploymentSample1:
                $ref: '#/components/examples/DeploymentSample1'
              CaseSample1:
                $ref: '#/components/examples/CaseSample1'
              CaseSampleNoHospital:
                $ref: '#/components/examples/CaseSampleNoHospital'
              CaseHalfSample1:
                $ref: '#/components/examples/CaseHalfSample1'
              CaseQuarterSample1:
                $ref: '#/components/examples/CaseQuarterSample1'
              IncomingCallASample1:
                $ref: '#/components/examples/IncomingCallASample1'
              IncomingCallBSample1:
                $ref: '#/components/examples/IncomingCallBSample1'
              WeatherSample1:
                $ref: '#/components/examples/WeatherSample1'
              TotalFrequenceSample1:
                $ref: '#/components/examples/TotalFrequenceSample1'
              AlarmSample1:
                $ref: '#/components/examples/AlarmSample1'
              AttendanceSample1:
                $ref: '#/components/examples/AttendanceSample1'
              ScheduleSample1:
                $ref: '#/components/examples/ScheduleSample1'
              DoctorOnDutySample1:
                $ref: '#/components/examples/DoctorOnDutySample1'
              HandoverFull:
                $ref: '#/components/examples/HandoverFull'
              HandoverMin:
                $ref: '#/components/examples/HandoverMin'
              DigitRadioSample1:
                $ref: '#/components/examples/DigitRadioSample1'
              AmbulanceRateRed:
                $ref: '#/components/examples/AmbulanceRateRed'
              AmbulanceRateYellow:
                $ref: '#/components/examples/AmbulanceRateYellow'
              AmbulanceRateGreen:
                $ref: '#/components/examples/AmbulanceRateGreen'
              AmbulanceRateBlack:
                $ref: '#/components/examples/AmbulanceRateBlack'

                
        required: true
      responses:
        '200':
          $ref: '#/components/responses/OK'
  /control_monitor:
    post:
      tags:
        - 制御系API
      summary: 表示盤制御
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ControlMonitor'
            examples:
              ControlMonitorTotal:
                $ref: '#/components/examples/ControlMonitorTotal'
              ControlMonitorAlarm:
                $ref: '#/components/examples/ControlMonitorAlarm'
              ControlMonitorCase:
                $ref: '#/components/examples/ControlMonitorCase'
              ControlMonitorCaseHalf:
                $ref: '#/components/examples/ControlMonitorCaseHalf'
              ControlMonitorCaseQuarter:
                $ref: '#/components/examples/ControlMonitorCaseQuarter'
              ControlMonitor1Content4Test:
                $ref: '#/components/examples/ControlMonitor1Content4Test'
                
                
        required: true
      responses:
        '200':
          $ref: '#/components/responses/OK'
          
  /control_sound:
    post:
      tags:
        - 制御系API
      summary: 表示盤喚起音吹鳴
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ControlSound'
            examples:
              Sample1:
                value:
                  display_sound_ring_data:
                    display_type: 0
                    display_no: 1
                    sound_no: 2
                    repeat_count: 10
        required: true
      responses:
        '200':
          $ref: '#/components/responses/OK'

  /sync_time:
    post:
      tags:
        - 制御系API
      summary: 指令系APと映像系APの時刻を同期させるため、指令系APから映像系APへ通知を行う
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncTime'
            examples:
              Sample1:
                value:
                  time: 
                    display_text: '2022/10/08 22:30:41'
                    background_color: '#ff00ff'
                    text_color: '#eaeaea'
        required: true
      responses:
        '200':
          $ref: '#/components/responses/OK'
components:
  responses:
    OK:
      description: APIデータをJson形式で正しく受信できた
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResult'
          examples:
            OK:
              value: 'result: 0'
            NG:
              value: 'result: -1'
  schemas:
    Color:
      type: string
      pattern: '^[#0-9a-fA-F]{7}$'
    ChangeSetting:
      type: object
      properties:
        change_time: 
          type: integer
          format: int32
          description: 切替時間.同階層の配列を切り替える間隔. 秒数を設定する. 0の場合は切り替えを行わない.0以上の整数を設定する
        change_size:
          type: integer
          format: int32
          description: 切替サイズ
    BlinkSetting: 
      type: object
      properties:
        blink_time: 
          type: integer
          format: int32
          minimum: 0
          description: |-
            点滅表示を行う時間
            秒数を設定する
            0の場合は点滅しない
            0以上の整数を設定する
        lighting_text_color:
          pattern: '^[#0-9a-fA-F]{7}$'
          type: string
          description: |-
            画面に表示するテキストの文字色
            16進数のRGB値を設定する  '#00ff00'
        lighting_background_color: 
          type: string
          pattern: '^[#0-9a-fA-F]{7}$'
          description: |-
            画面に表示するテキストの背景色
            16進数のRGB値を設定する '#0F0F0F'
        lighting_status: 
          type: string
          pattern: '^[0-3]{1}$'
          description: |-
            該当箇所が点灯などをするかの設定
            0~3を設定する
        blink_speed: 
          type: integer
          format: int32
          minimum: 0
          description: |-
            点滅の切り替えを行う速度
            ミリ秒を設定する
            0の場合は点滅しない
            0以上の整数を設定する
    ControlMonitor:
      type: object
      properties:
        display_control_data:
          type: array
          items:
            type: object
            properties:
              display_type:
                type: integer
                description: |-
                  表示盤の種類毎に付与される識別番号
                  0：４面マルチ表示盤、1：単面表示盤、２：指令台ディスプレイ
                minimum: 0
                maximum: 2
              display_no:
                format: int32
                type: integer
                description: 表示盤毎に付与される表示盤の識別番号
              display_name:
                type: string
                description: 表示盤表示盤番号に紐づく名称
              display_split_data:
                type: array
                items:
                  type: object
                  properties:
                    display_split_no:
                      format: int32
                      type: integer
                      description: 表示盤の画面毎に付与される番号
                      minimum: 0
                      maximum: 3
                    display_details_split_data:
                      type: array
                      items:
                        type: object
                        properties:
                          display_details_split_no:
                            format: int32
                            type: integer
                            description: |- 
                              表示盤に表示可能なコンテンツの最小サイズ毎に付与される番号
                              指令センター表示盤の場合に分割単位に番号を設定
                              （4面マルチ表示盤：0~15、単面表示盤：0~3）
                              署所表示盤の場合には固定で0を設定（分割無し）
                            minimum: 0
                            maximum: 15
                          display_content_data:
                            type: object
                            properties:
                              input_switch:
                                format: int32
                                type: integer
                                description: |- 
                                  モニター入力ポート選択
                                  （0：コンテンツ系　1：マトリクススイッチャー系）
                                minimum: 0
                                maximum: 1
                              source_no:
                                type: integer
                                format: int32
                                description: 表示盤に表示するソース番号
                                minimum: 0
                              source_name:
                                type: string
                                description: ソース番号に紐づく名称
                              source_split_no:
                                type: integer
                                format: int32
                                description: '同一のソース番号(コンテンツ)を分割した番号 例：事案や気象情報など複数コンテンツ表示可能な場合に設定'
                                minimum: 0
                              source_disp_pat:
                                type: integer
                                format: int32
                                description: ソースを表示するサイズのパターン
                                minimum: 0
                                maximum: 3
                              source_vol_control_type:
                                type: integer
                                format: int32
                                description: '0~100,音量'
                                minimum: 0
                                maximum: 100
                              source_vol_mute_control:
                                type: integer
                                format: int32
                                description: 0：Mute有効　1：Mute解除
                                minimum: 0
                                maximum: 1
                              is_vol_control:
                                format: int32
                                type: integer
                                description: |- 
                                  音を出すソースを指定する項目。音を出すソースにのみ1を設定する。
                                  ※初回開発においては、1表示盤につき最大1画面分割番号のみ指定可能
                                  0：ソースの音量を制御しない（音を出さない）
                                  1：ソースの音量を制御する（音を出す）
                                minimum: 0
                                maximum: 1

    ControlSound:
      type: object
      properties:
        display_sound_ring_data:
            type: object
            properties:
              display_type:
                type: integer
                description: |-
                  表示盤の種類毎に付与される識別番号
                  0：指令センター表示盤(4面マルチ)、1：署所表示盤(単面)、
                  2：指令台ディスプレイ
                minimum: 0
                maximum: 2
              display_no:
                format: int32
                type: integer
                description: 表示盤種別毎に付与される表示盤の識別番号
              sound_no:
                format: int32
                type: integer
                description: 吹鳴したい喚起音のファイルの番号
              repeat_count:
                format: int32
                type: integer
                description: ファイルの再生を繰り返す回数

    SyncTime:
      type: object
      properties:
        time:
          type: object
          properties:
            display_text:
              type: string
              pattern: ^[1-9]\d{3}\/(0[1-9]|1[0-2])\/(0[1-9]|[1-2][0-9]|3[0-1])\s+(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$
              description: |-
                指令系APにおける時刻。
                フォーマット：yyyy/MM/dd HH:mm:ss
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'

    DeploymentContent:
      type: object
      properties:
        deployment:
          type: array
          maxItems: 7
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: 配備状況
                maxLength: 2
        car_name:
          type: array
          maxItems: 7
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: 車両名称
                maxLength: 4
        arrow:
          type: array
          maxItems: 7
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: →
                maxLength: 1
        move_car_name:
          type: array
          maxItems: 7
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: 移動待機先署所名／被代替車両名
                maxLength: 4
        car_type:
          type: array
          maxItems: 7
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: 車両種別
                maxLength: 4
    VehicleContent:
      type: object
      properties:
        is_deployment:
          type: integer
          format: int32
          minimum: 1
          maximum: 2
          description: '配備状況の表示を行うか否か 1：表示する 2：表示しない'
        title_name:
          type: array
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                description: '署所(部隊)名または車両種別'
                type: string
                maxLength: 15
              deployment:
                type: array
                items: 
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 1
                      description: 配備状況
              car_name: 
                type: array
                items: 
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 4
                      description: 車両名称
              town_name:
                type: array
                items: 
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 6
                      description: 現在位置町丁名
              disaster_type:
                type: array
                items: 
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 2
                      description: 災害種別
              avm_dynamic_state:
                type: array
                items: 
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 2
                      description: AVM動態
              lighting_setting:
                type: array
                items: 
                  $ref: '#/components/schemas/BlinkSetting'

    AmbulanceRateContent:
      type: object
      properties:
        title:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 10
              description: タイトル
        dispatch_num:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 4
              description: 出動台数
    DigitalRadioContent:
      type: object
      properties:
        wireless_channel_name:
          type: array
          maxItems: 4
          items:
            type: object
            properties:
              display_text:
                type: string
                maxLength: 14
                description: 無線チャネル名
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              outgoing_call_move_station_name:  
                type: array
                maxItems: 2
                items:
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 4
                      description: 発信移動局名
              incoming_call_move_station_name:
                type: array
                maxItems: 2
                items:
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 3
                      description: 着信基地局名
              incoming_call_ts:
                type: array
                maxItems: 2
                items:
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 5
                      description: 着信時刻
              blink_setting: 
                type: array
                maxItems: 2
                items:
                  $ref: '#/components/schemas/BlinkSetting'
    ApiResult:
        type: object
        properties:
          result:
            type: integer
            format: int32
            description: '処理結果:0：成功、-1：失敗'
            minimum: -1
            maximum: 0
          msg:
            type: string
            description: 'Result=-1の場合の補足メッセージ'
    HandoverContent:
      type: object
      properties:
        handover_listing:
          type: array
          maxItems: 6
          items:
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: 引継事項.表示文字数を超えた場合スクロール

    DoctorOnDutyContent:
      type: object
      properties:
        medical_subject: 
          type: array
          maxItems: 6
          items:
            type: object
            properties:
              display_text:
                type: string
                maxLength: 2
                description: 診療科目
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'         
              medical_institution_name: 
                type: array
                items:
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      description: 医療機関名
              medical_institution_telephone_no: 
                type: array
                items:
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 9
                      description: 医療機関電話番号

    AttendanceContent:
      type: object
      properties:
        official_position_name: 
          type: array
          maxItems: 14
          items:
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                maxLength: 6
                description: 役職名
        attendance_dynamic_state_name: 
          type: array
          maxItems: 14
          items:
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                maxLength: 2
                description: 出退動態名
    AlarmContent:
      type: object
      properties:
        issued_ts: 
          type: array
          maxItems: 5
          items:
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                maxLength: 12
                description: 発令日時. 仕様上制限文字数が9ですが、半角数字を含め、12にする
        combined_forecast_warning: 
          type: array
          maxItems: 5
          items:
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                description: 警報・注意報内容
            
    TotalFrequencyContent:
      type: object
      properties:
        agg_unit:
          type: array
          maxItems: 3
          items:
            type: object
            properties:
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              display_text:
                type: string
                maxLength: 3
                description: 集計単位
        agg_info:
          type: array
          maxItems: 5
          items:
            type: object
            properties:
              display_text:
                type: string
                maxLength: 5
                description: 集計対象
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'                          
              number_list:
                type: array
                maxItems: 3
                items:
                  type: object
                  properties:
                    text_color:
                      $ref: '#/components/schemas/Color'
                    background_color:
                      $ref: '#/components/schemas/Color'
                    display_text:
                      type: string
                      maxLength: 7
                      description: 災害区分毎件数. 仕様上の制限文字数が6ですが、999,999の[,]を対応できるように7とする

    WeatherContent:
      type: object
      properties:
        wind_speed:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 平均風速
        wind_direction:
          type: object
          properties:
            display_text:
              type: string
              description: 風向※絵で表現
            display_color:
              $ref: '#/components/schemas/Color'
        wind_speed_max:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 最大風速
        temperature:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 気温
        rainfall:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 雨量
        effective_humidity:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 実効湿度
        relative_humidity:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 相対湿度
        atmospheric_pressure:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              pattern: ^[\d,.]*$
              description: 気圧
        observation_time:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              description: 観測時刻
        weather:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              description: 天候

    CaseQuarterContent:
      type: object
      properties:
        case:
          type: array
          maxItems: 4
          items:
            type: object
            properties:
              disaster_class:
                type: object
                properties:
                  disaster_type:
                    type: object
                    properties:
                      display_text:
                        type: string
                        maxLength: 4
                        description: 災害種別
                      text_color:
                        $ref: '#/components/schemas/Color'
                  case_no:
                    type: object
                    properties:
                      display_text:
                        type: string
                        maxLength: 7
                        description: 指令整番
                      text_color:
                        $ref: '#/components/schemas/Color'
                  fire_station_name:
                    type: object
                    properties:
                      display_text:
                        type: string
                        maxLength: 4
                        description: 署所名
                      text_color:
                        $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
              town_name:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    maxLength: 6
                    description: 災害地点町丁名
              awareness_time:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    maxLength: 8
                    description: 覚知時刻または指令時刻。※仕様の7文字と違って、1個スペースを増やして、8文字
              latest_dynamic_state_time:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    description: 直近動態時刻(廃棄)

    CaseHalfContent:
      type: object
      properties:
        case:
          type: array
          maxItems: 2
          items:
            type: object
            properties:
              disaster_class:
                type: object
                properties:
                  disaster_type:
                    type: object
                    properties:
                      display_text:
                        type: string
                        maxLength: 4
                        description: 災害種別
                      text_color:
                        $ref: '#/components/schemas/Color'
                  case_no:
                    type: object
                    properties:
                      display_text:
                        type: string
                        maxLength: 7
                        description: 指令整番
                      text_color:
                        $ref: '#/components/schemas/Color'
                  fire_station_name:
                    type: object
                    properties:
                      display_text:
                        type: string
                        maxLength: 4
                        description: 署所名
                      text_color:
                        $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
              town_name:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    maxLength: 6
                    description: 災害地点町丁名
              awareness_time:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    maxLength: 8
                    description: 覚知時刻または指令時刻。※仕様の7文字と違って、1個スペースを増やして、8文字
              car_name:
                type: object
                properties:
                  display_data:
                    type: array
                    items:
                      type: object
                      properties:
                        text_color:
                          $ref: '#/components/schemas/Color'
                        background_color:
                          $ref: '#/components/schemas/Color'
                        display_text:
                          type: string
                          maxLength: 4
                          description: 車両名称
                  change_setting:
                    $ref: '#/components/schemas/ChangeSetting'


    CaseContent:
      type: object
      properties:
        disaster_class:
          type: object
          properties:
            disaster_type:
              type: object
              properties:
                display_text:
                  type: string
                  maxLength: 4
                  description: 災害種別
                text_color:
                  $ref: '#/components/schemas/Color'
            case_no:
              type: object
              properties:
                display_text:
                  type: string
                  maxLength: 7
                  description: 指令整番
                text_color:
                  $ref: '#/components/schemas/Color'
            fire_station_name:
              type: object
              properties:
                display_text:
                  type: string
                  maxLength: 4
                  description: 署所名
                text_color:
                  $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
        town_name:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 6
              description: 災害地点町丁名
        target_name:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 9
              description: 目標物名
        awareness_time:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 8
              description: 覚知時刻
        command_time:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 8
              description: 指令時刻
        car_name:
          type: object
          properties:
            display_data:
              type: array
              items:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    maxLength: 4
                    description: 車両名称
            change_setting:
              $ref: '#/components/schemas/ChangeSetting'
        latest_dynamic_state_time:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 15
              description: 直近動態時刻
        disaster_dynamic_state:
          type: object
          properties:
            text_color:
              $ref: '#/components/schemas/Color'
            background_color:
              $ref: '#/components/schemas/Color'
            display_text:
              type: string
              maxLength: 4
              description: 災害状態
        transport_hospital:
          type: object
          properties:
            display_data:
              type: array
              items:
                type: object
                properties:
                  text_color:
                    $ref: '#/components/schemas/Color'
                  background_color:
                    $ref: '#/components/schemas/Color'
                  display_text:
                    type: string
                    maxLength: 15
                    description: 搬送先病院
            change_setting:
              $ref: '#/components/schemas/ChangeSetting'

    IncomingCallBContent:
      type: object
      properties:
        line_name:
          type: array
          maxItems: 32
          items:
            type: object
            properties:
              display_text:
                type: string
                maxLength: 8
                description: '回線名'
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              incoming_call: 
                type: array
                maxItems: 6
                items:
                  type: object
                  properties:
                    display_color: 
                      $ref: '#/components/schemas/Color'
                    blink_setting: 
                      type: object
                      properties:
                        blink_time: # 通常のBlinkSettingと情報が違うので、単独で定義
                          type: integer
                          format: int32
                          minimum: 0
                          description: |-
                            点滅表示を行う時間
                            秒数を設定する
                            0の場合は点滅しない
                            0以上の整数を設定する
                        lighting_display_color: 
                          $ref: '#/components/schemas/Color'
                        lighting_status: 
                          type: string
                          pattern: '^[0-3]{1}$'
                          description: |-
                            該当箇所が点灯などをするかの設定
                            0~3を設定する
                        blink_speed: 
                          type: integer
                          format: int32
                          minimum: 0
                          description: |-
                            点滅の切り替えを行う速度
                            ミリ秒を設定する
                            0の場合は点滅しない
                            0以上の整数を設定する

    IncomingCallAContent:
      type: object
      properties:
        line_name:
          type: array
          maxItems: 28
          items:
            type: object
            properties:
              display_text:
                type: string
                maxLength: 8
                description: 回線名
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
              blink_setting: 
                $ref: '#/components/schemas/BlinkSetting'

    ScheduleContent:
      type: object
      properties:
        schedule_ts: 
          type: array
          maxItems: 6
          items:
            type: object
            properties:
              display_text:
                type: string
                maxLength: 12
                description: 予定日時. 仕様上制限文字数が9ですが、半角数字を含め、12にする
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'
        schedule_content: 
          type: array
          maxItems: 6
          items:
            type: object
            properties:
              display_text:
                type: string
                description: 予定内容
              text_color:
                $ref: '#/components/schemas/Color'
              background_color:
                $ref: '#/components/schemas/Color'

  examples:
    VehicleExampleDeploy:
      value:
        data:
          display_content_data:
            - source_no: 1
              source_name: 車両コンテンツ
              source_split_no: 0
              source_data:
                is_deployment: 1
                title_name:
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'

                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '3'
                      
                      - blink_time: 5
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '3'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

    VehicleExampleDeployMoreDetail:
      value:
        data:
          display_content_data:
            - source_no: 1
              source_name: 車両コンテンツ
              source_split_no: 0
              source_data:
                is_deployment: 1
                title_name:
                  - display_text: あいうえおかきくけこさしすせ
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '00ffff'
                      - display_text: ''
                      - display_text: ''
                      - display_text: '代'
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 最大字数
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 中１
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 中２
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 中3
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 中4
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 中３１
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 中４１
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 機材車
                        text_color: '#ffffff'
                        background_color: '#000000'

                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 最大文字丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'

                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''
                      - display_text: ''

                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'

                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '3'
                      
                      - blink_time: 5
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 2000
                        lighting_status: '3'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'

                    car_name:
                      - display_text: 救急東1
                        text_color: '0f0'
                        background_color: '#000000'
                      - display_text: 東1
                        text_color: 'f00'
                        background_color: '#000000'
                      - display_text: 東２
                        text_color: '#ff0000'
                        background_color: '#000000'
                      - display_text: 東広報車
                        text_color: '#ffffff'
                        background_color: '#000000'

                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'

    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'

                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      - blink_time: 0
                      - blink_time: 0
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 3000
                        lighting_status: '3'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffff00'
                        background_color: '#00ff00'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    deployment:
                      - display_text: 出
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 代
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

    VehicleExampleNoDeploy:
      value:
        data:
          display_content_data:
            - source_no: 1
              source_name: 車両コンテンツ
              source_split_no: 0
              source_data:
                is_deployment: 2
                title_name:
                  - display_text: あいうえおかきくけこさしすせ
                    text_color: '#000000'
                    background_color: '#ffffff'
                    car_name:
                      - display_text: 最大文字
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 最大文字丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'

                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '3'
                      
                      - blink_time: 5
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '3'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
   
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
  
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
                  - display_text: 中消防署
                    text_color: '#000000'
                    background_color: '#ffffff'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'

                  - display_text: 東分署
                    text_color: '#000000'
                    background_color: '#ffffff'
    
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#ff0000'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'


                  - display_text: 南分署
                    text_color: '#000000'
                    background_color: '#ffffff'
   
                    car_name:
                      - display_text: 救急中1
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                  
                    town_name:
                      - display_text: 赤坂2丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 赤坂3丁目
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    disaster_type:
                      - display_text: 急病
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: ''
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    avm_dynamic_state:
                      - display_text: 現着
                        text_color: '#ffffff'
                        background_color: '#000000'
                      
                      - display_text: 待機
                        text_color: '#ffffff'
                        background_color: '#000000'
    
                    lighting_setting:
                      - blink_time: 10
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 1000
                        lighting_status: '1'
                      
                      - blink_time: 100
                        lighting_text_color: '#00ffff'
                        lighting_background_color: '#00ff00'
                        blink_speed: 10
                        lighting_status: '2'
            
    ScheduleSample1:
      value:
        data:
          display_content_data:
            - source_no: 14
              source_name: 予定コンテンツ
              source_split_no: 0
              source_data:
                schedule_ts:  
                  - display_text: 10月10日10時10分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日10時12分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日10時12分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日10時10分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日10時12分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日10時12分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日10時10分
                    text_color: '#ffffff'
                    background_color: '#000000'

                    
                schedule_content:  
                  - display_text: '救急北１車検'
                    text_color: '#ffff00'
                    background_color: '#000000'
                  - display_text: 'わらび小学校見学いく'
                    text_color: '#00e2e2'
                    background_color: '#000000'
                  - display_text: 'あいうえおかきくけこあいうえおかきくけ'
                    text_color: '#00e2e2'
                    background_color: '#000000'
                  - display_text: '救急北1車検'
                    text_color: '#ffff00'
                    background_color: '#000000'
                  - display_text: 'わらび小学校見学'
                    text_color: '#00e2e2'
                    background_color: '#000000'
                  - display_text: '小学校'
                    text_color: '#00e2e2'
                    background_color: '#000000'
                  - display_text: '救急北'
                    text_color: '#ffff00'
                    background_color: '#000000'

                    
                    
    DeploymentSample1:
      value:
        data:
          display_content_data:
            - source_no: 2
              source_name: 配備状況コンテンツ
              source_split_no: 0
              source_data:
                deployment:
                  - display_text: 出向
                    text_color: '#00e400'
                    background_color: '#000000'
                  - display_text: 移動
                    text_color: '#fcfc00'
                    background_color: '#000000'
                  - display_text: 代替
                    text_color: '#f70000'
                    background_color: '#000000'
                  - display_text: 出向
                    text_color: '#00e400'
                    background_color: '#000000'
                  - display_text: 移動
                    text_color: '#fcfc00'
                    background_color: '#000000'
                  - display_text: 代替
                    text_color: '#f70000'
                    background_color: '#000000'
                  - display_text: 代替
                    text_color: '#f70000'
                    background_color: '#000000'
                car_name:
                  - display_text: 最大文字
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 本署ＰＳ
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 栄署栄署
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 本署P2
                    text_color: '#00ff00'
                    background_color: '#000000'  
                  - display_text: 本署PS
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 栄署
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 栄署T1
                    text_color: '#00ff00'
                    background_color: '#000000'
                    
                arrow:
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                  - display_text: →
                    text_color: '#f9f9f9'
                    background_color: '#000000'
                
                move_car_name:
                  - display_text: 栄町P2
                    text_color: '#00f9f9'
                    background_color: '#000000'
                  - display_text: 最大文字
                    text_color: '#00f9f9'
                    background_color: '#000000'
                  - display_text: 山本山
                    text_color: '#00f9f9'
                    background_color: '#000000'
                  - display_text: 栄町栄町
                    text_color: '#00f9f9'
                    background_color: '#000000'
                  - display_text: 本署
                    text_color: '#00f9f9'
                    background_color: '#000000'
                  - display_text: 山本
                    text_color: '#00f9f9'
                    background_color: '#000000'
                  - display_text: 山本
                    text_color: '#00f9f9'
                    background_color: '#000000'
                
                car_type:
                  - display_text: ポンプ車
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: はしご車
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: 指揮者
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: ポンプ車
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: はしご車
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: 指揮者
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: 指揮者
                    text_color: '#ff0000'
                    background_color: '#000000'

    ControlMonitorTotal:
      value:
        display_control_data:
          - display_type: 0
            display_no: 0
            display_name: 指令センター表示盤
            display_split_data:
              - display_split_no: 0
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 7
                      source_name: 着信状況コンテンツA
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                      input_switch: 0
                      is_vol_control: 1
                  - display_details_split_no: 1
                    display_content_data:
                      source_no: 7
                      source_name: 着信状況コンテンツA
                      source_split_no: 1
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 4
                    display_content_data:
                      source_no: 10
                      source_name: 総合度数コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 5
                    display_content_data:
                      source_no: 9
                      source_name: 気象コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
              - display_split_no: 1
                display_details_split_data:
                  - display_details_split_no: 3
                    display_content_data:
                      source_no: 7
                      source_name: 着信状況コンテンツA
                      source_split_no: 2
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 6
                    display_content_data:
                      source_no: 11
                      source_name: 予警報コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 7
                    display_content_data:
                      source_no: 12
                      source_name: 出退コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
              - display_split_no: 2
                display_details_split_data:
                  - display_details_split_no: 8
                    display_content_data:
                      source_no: 6
                      source_name: 現在時刻コンテンツ
                      source_split_no: 0
                      source_disp_pat: 2
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
              - display_split_no: 3
                display_details_split_data:
                  - display_details_split_no: 10
                    display_content_data:
                      source_no: 13
                      source_name: 当番医コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                  - display_details_split_no: 11
                    display_content_data:
                      source_no: 15
                      source_name: 引継事項コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 14
                    display_content_data:
                      source_no: 14
                      source_name: 予定コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                  - display_details_split_no: 15
                    display_content_data:
                      source_no: 16
                      source_name: 無線デジタルコンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                      
                    
                    
                    
          - display_type: 0
            display_no: 1
            display_name: 指令センター表示盤2
            display_split_data:
              - display_split_no: 0
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 3
                      source_name: 事案コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                  - display_details_split_no: 1
                    display_content_data:
                      source_no: 4
                      source_name: 事案コンテンツ1/2
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 4
                    display_content_data:
                      source_no: 5
                      source_name: 事案コンテンツ1/4
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                  - display_details_split_no: 5
                    display_content_data:
                      source_no: 17
                      source_name: 救急車稼働率コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
              - display_split_no: 1
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 8
                      source_name: 着信状況コンテンツB
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                  - display_details_split_no: 1
                    display_content_data:
                      source_no: 2
                      source_name: 配備状況コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
              - display_split_no: 2
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 1
                      source_name: 車両コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0
                  
              - display_split_no: 3
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 16
                      source_name: コンテンツテスト専用分割面
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 0

          - display_type: 0
            display_no: 2
            display_name: 指令センター表示盤3
            display_split_data:
              - display_split_no: 0
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 11
                      source_name: 予警報コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
                      
    ControlMonitorAlarm:
      value:
        display_control_data:
          - display_type: 0
            display_no: 2
            display_name: 指令センター表示盤3
            display_split_data:
              - display_split_no: 0
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 11
                      source_name: 予警報コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1
    ControlMonitorCase:
      value:
        display_control_data:
          - display_type: 0
            display_no: 2
            display_name: 指令センター表示盤3
            display_split_data:
              - display_split_no: 1
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 3
                      source_name: 事案コンテンツ
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1      

    ControlMonitor1Content4Test:
      value:
        display_control_data:
          - display_no: 2
            display_split_data:
              - display_split_no: 1
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 2
                      source_split_no: 0

    ControlMonitorCaseHalf:
      value:
        display_control_data:
          - display_type: 0
            display_no: 2
            display_name: 指令センター表示盤3
            display_split_data:
              - display_split_no: 2
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 4
                      source_name: 事案コンテンツ1/2
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1      
                      
                      
    ControlMonitorCaseQuarter:
      value:
        display_control_data:
          - display_type: 0
            display_no: 2
            display_name: 指令センター表示盤3
            display_split_data:
              - display_split_no: 3
                display_details_split_data:
                  - display_details_split_no: 0
                    display_content_data:
                      source_no: 5
                      source_name: 事案コンテンツ1/4
                      source_split_no: 0
                      source_disp_pat: 1
                      source_vol_control_type: 2
                      source_vol_mute_control: 1      
                      
    CaseSampleNoHospital:
      value:
        data:
          display_content_data:
            - source_no: 3
              source_name: 事案コンテンツ
              source_split_no: 0
              source_data:
                disaster_class:
                  disaster_type:
                    display_text: 急病救急
                    text_color: '#000000'
                  case_no:
                    display_text: 指令整番最大字
                    text_color: '#000000'
                  fire_station_name:
                    display_text: 中消防署
                    text_color: '#000000'
                  background_color: '#40ff40'
                town_name:
                  display_text: 坂下丁目番地
                  text_color: '#ffffff'
                  background_color: '#000000'
                target_name:
                  display_text: あいうえおかきくけ
                  text_color: '#ffffff'
                  background_color: '#000000'
                awareness_time:
                  display_text: '15:08　覚知'
                  text_color: '#ffffff'
                  background_color: '#000000'
                command_time:
                  display_text: '15:08　指令'
                  text_color: '#ffffff'
                  background_color: '#008000'
                car_name:
                  display_data:
                    - display_text: 救急中１
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中中
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はし中中
                      text_color: '#030000'
                      background_color: '#ff0000'


                    - display_text: 救急中
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ中
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご中
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご
                      text_color: '#030000'
                      background_color: '#ff0000'
                  change_setting:
                   change_time: 1
                   change_size: 4
                latest_dynamic_state_time:
                  display_text: '救急中1　　15:14　　現場到着'
                  text_color: '#3ffdfd'
                  background_color: '#000000'
                disaster_dynamic_state:
                  display_text: 現場到着
                  text_color: '#000000'
                  background_color: '#ff0000'
                
                    
    CaseSample1:
      value:
        data:
          display_content_data:
            - source_no: 3
              source_name: 事案コンテンツ
              source_split_no: 0
              source_data:
                disaster_class:
                  disaster_type:
                    display_text: 急病救急
                    text_color: '#000000'
                  case_no:
                    display_text: 指令整番字
                    text_color: '#000000'
                  fire_station_name:
                    display_text: 中消防署東
                    text_color: '#000000'
                  background_color: '#40ff40'
                town_name:
                  display_text: 坂下丁目番地字
                  text_color: '#ffffff'
                  background_color: '#000000'
                target_name:
                  display_text: 山本修一文字
                  text_color: '#ffffff'
                  background_color: '#000000'
                awareness_time:
                  display_text: '15:08　覚知'
                  text_color: '#ffffff'
                  background_color: '#000000'
                command_time:
                  display_text: '15:08　指令'
                  text_color: '#ffffff'
                  background_color: '#008000'
                car_name:
                  display_data:
                    - display_text: 救急中１２３
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中２３４
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中３４５
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ１２３
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ最大文
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ345
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご最大文
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご234
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご345
                      text_color: '#030000'
                      background_color: '#ff0000'


                    - display_text: 救急中456
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中567
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: 救急中678
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ456
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ567
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: ポンプ678
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご456
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご567
                      text_color: '#030000'
                      background_color: '#ff0000'
                    - display_text: はしご678
                      text_color: '#030000'
                      background_color: '#ff0000'
                  change_setting:
                   change_time: 2
                   change_size: 4
                latest_dynamic_state_time:
                  display_text: '救急中1　　15:14　　現場到着'
                  text_color: '#3ffdfd'
                  background_color: '#000000'
                disaster_dynamic_state:
                  display_text: 現場到着
                  text_color: '#000000'
                  background_color: '#ff0000'
                transport_hospital:
                  display_data:
                  - display_text: 病院病院病院病院病院病院病院１
                    text_color: '#00fefe'
                    background_color: '#ff0000'
                  - display_text: 病院病院病院病院病院２
                    text_color: '#00fefe'
                    background_color: '#000000'
                  - display_text: 病院病院病院３
                    text_color: '#00fefe'
                    background_color: '#00ff00'
                  - display_text: 病院病院病院4
                    text_color: '#00fefe'
                    background_color: '#00ff00'
                  change_setting:
                   change_time: 2
                   change_size: 1
                    
                    
    CaseHalfSample1:
      value:
        data:
          display_content_data:
            - source_no: 4
              source_name: 簡易事案コンテンツ(1-2サイズ)
              source_split_no: 0
              source_data:
                case:
                  - disaster_class:
                      disaster_type:
                        display_text: 交通救助
                        text_color: '#000000'
                      case_no:
                        display_text: 指令整番最大字
                        text_color: '#000000'
                      fire_station_name:
                        display_text: 中消防署
                        text_color: '#000000'
                      background_color: '#ffff40'
                    awareness_time:
                      display_text: '指令 14:22'
                      text_color: '#ffffff'
                      background_color: '#000000'                    
                    town_name:
                      display_text: 中頭群嘉手納
                      text_color: '#45ffff'
                      background_color: '#000000'

                    car_name:
                      display_data:
                        - display_text: 救急中
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: 救急中
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: 救急中
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ポンプ最
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ポンプ
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ポンプ
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: はしご最
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: はしご
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: はしご
                          text_color: '#030000'
                          background_color: '#ff0000'


                        - display_text: 救急中
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: 救急中
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: 救急中
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ポンプ
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ポンプ
                          text_color: '#030000'
                          background_color: '#ff0000'

                      change_setting:
                       change_time: 2
                       change_size: 4
                  - disaster_class:
                      disaster_type:
                        display_text: 救急
                        text_color: '#000000'
                      case_no:
                        display_text: あいうえおかき
                        text_color: '#000000'
                      fire_station_name:
                        display_text: 西消防署
                        text_color: '#000000'
                      background_color: '#ffff40'
                    awareness_time:
                      display_text: '指令 14:22'
                      text_color: '#ffffff'
                      background_color: '#000000'
                    town_name:
                      display_text: 中頭群嘉手納
                      text_color: '#45ffff'
                      background_color: '#0af'

                    car_name:
                      display_data:
                        - display_text: １
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ２２
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ３
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ４４
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ５
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ６
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ７
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ８
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: ９
                          text_color: '#030000'
                          background_color: '#ff0000'
                        - display_text: １０
                          text_color: '#030000'
                          background_color: '#ff0000'
                      change_setting:
                       change_time: 1
                       change_size: 4



    CaseQuarterSample1:
      value:
        data:
          display_content_data:
            - source_no: 5
              source_name: 簡易事案コンテンツ(1-4サイズ)
              source_split_no: 0
              source_data:
                case:
                  - disaster_class:
                      disaster_type:
                        display_text: 交通救助
                        text_color: '#000000'
                      case_no:
                        display_text: あいうえおかき
                        text_color: '#000000'
                      fire_station_name:
                        display_text: 中消防署
                        text_color: '#000000'
                      background_color: '#40ff40'
                    town_name:
                      display_text: 中頭群嘉手納
                      text_color: '#45ffff'
                      background_color: '#000000'
                    awareness_time:
                      display_text: '指令１４：２２'
                      text_color: '#ffffff'
                      background_color: '#ff1100'
                    latest_dynamic_state_time:
                      display_text: '１５：３７救急中１病院到着現場到着'
                      text_color: '#3ffdfd'
                      background_color: '#000000'
                  - disaster_class:
                      disaster_type:
                        display_text: 急病
                        text_color: '#000000'
                      case_no:
                        display_text: '#112789'
                        text_color: '#000000'
                      fire_station_name:
                        display_text: 西消防署
                        text_color: '#000000'
                      background_color: '#40ff40'
                    town_name:
                      display_text: 中頭群嘉手納
                      text_color: '#45ffff'
                      background_color: '#000000'
                    awareness_time:
                      display_text: '指令 14:23'
                      text_color: '#ffffff'
                      background_color: '#000000'
                    latest_dynamic_state_time:
                      display_text: '救急中1    15:15     現場到着'
                      text_color: '#3ffdfd'
                      background_color: '#000000'
                  - disaster_class:
                      disaster_type:
                        display_text: 交通救助
                        text_color: '#000000'
                      case_no:
                        display_text: '#112790'
                        text_color: '#000000'
                      fire_station_name:
                        display_text: 西消防署
                        text_color: '#000000'
                      background_color: '#ffff40'
                    town_name:
                      display_text: 中頭群嘉手納
                      text_color: '#45ffff'
                      background_color: '#000000'
                    awareness_time:
                      display_text: '指令 14:24'
                      text_color: '#ffffff'
                      background_color: '#000000'
                    latest_dynamic_state_time:
                      display_text: '救急中1    15:16     現場到着'
                      text_color: '#3ffdfd'
                      background_color: '#ff0000'
                  - disaster_class:
                      disaster_type:
                        display_text: 交通救助
                        text_color: '#000000'
                      case_no:
                        display_text: １２３４５６７
                        text_color: '#000000'
                      fire_station_name:
                        display_text: 西消防署
                        text_color: '#000000'
                      background_color: '#40ff40'
                    town_name:
                      display_text: 中頭群嘉手納
                      text_color: '#45ffff'
                      background_color: '#000000'
                    awareness_time:
                      display_text: '指令 14:25'
                      text_color: '#ffffff'
                      background_color: '#000000'
                    latest_dynamic_state_time:
                      display_text: '救急中1    15:17     現場到着'
                      text_color: '#3ffdfd'
                      background_color: '#ff0000'
                      
                      
    IncomingCallASample1:
      value:
        data:
          display_content_data:
            - source_no: 7
              source_name: 着信状況コンテンツA
              source_split_no: 0
              source_data:
                line_name:
                  - display_text: 津川2
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 100
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: '3'
                      blink_speed: 3000
                  - display_text: 坂本坂本
                    text_color: '#000100'
                    background_color: '#40ff40'
                    blink_setting: 
                      blink_time: 2
                      lighting_text_color: '#ffff00' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1
                  - display_text: 川崎
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 30
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: '3'
                      blink_speed: 1000
                  - display_text: あいうえ
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 阿木
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 山口
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 加坂
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 相川
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 福岡
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 坂下
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 川上
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 神坂
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: 愛坂
                  - display_text: ''
                  - display_text: ''
                  - display_text: ''
                  - display_text: SoftBank
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: docomo
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: au
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: eAccess
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: Softbank
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: NTT
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: KDDI
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: Softbank
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000
                  - display_text: Softbank
                    text_color: '#ebebeb'
                    background_color: '#000000'
                    blink_setting: 
                      blink_time: 1
                      lighting_text_color: '#ffff40' 
                      lighting_background_color: '#45ffff'
                      lighting_status: 1
                      blink_speed: 1000

    IncomingCallBSample1:
      value:
        data:
          display_content_data:
            - source_no: 8
              source_name: 着信状況コンテンツB
              source_split_no: 0
              source_data:
                line_name:
                  - display_text: 国頭地区
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#0000ff'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                      - display_color: '#0000ff'
                        blink_setting: 
                          blink_time: 3
                          lighting_display_color: '#ff0000'
                          lighting_status: 3
                          blink_speed: 500
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 4
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1000
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 5
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 1

                  - display_text: 国頭地区
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                     
                  - display_text: 金武地区
                    text_color: '#e5e539'
                    background_color: '#000000'
                   
                  - display_text: 金武地区
                    text_color: '#e5e539'
                    background_color: '#000000'

                      
                  - display_text: ニライ
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                  - display_text: ニライ
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 3
                          lighting_display_color: '#ffff40'
                          lighting_status: 3
                          blink_speed: 100

                  - display_text: 伊野湾
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10

                  - display_text: 伊野湾
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 3
                          lighting_display_color: '#ffff40'
                          lighting_status: 3
                          blink_speed: 100
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 4
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1000

                  - display_text: 豊見城
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                  - display_text: 豊見城
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      
                  - display_text: 糸満
                   
                  - display_text: 糸満
                    text_color: '#3ef7f7'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10

                  - display_text: 宮古島
                    text_color: '#3ef7f7'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                     
                  - display_text: 宮古島
                    text_color: '#3ef7f7'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10

                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 7
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                  - display_text: 伊江村
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 3
                          lighting_display_color: '#ffff40'
                          lighting_status: 3
                          blink_speed: 100
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 4
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1000

                  - display_text: 伊江村
                    text_color: '#e5e539'
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 3
                          lighting_display_color: '#ffff40'
                          lighting_status: 3
                          blink_speed: 100
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 4
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1000
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 5
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 6
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 2


                  - display_text: 伊江村
                    text_color: 41ffff
                    background_color: '#000000'
                    incoming_call: 
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 1
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 2
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 10
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 3
                          lighting_display_color: '#ffff40'
                          lighting_status: 3
                          blink_speed: 100
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 4
                          lighting_display_color: '#ffff40'
                          lighting_status: 1
                          blink_speed: 1000
                      - display_color: '#40ff40'
                        blink_setting: 
                          blink_time: 5
                          lighting_display_color: '#ffff40'
                          lighting_status: 2
                          blink_speed: 1


    WeatherSample1:
      value:
        data:
          display_content_data:
            - source_no: 9
              source_name: 気象コンテンツ
              source_split_no: 0
              source_data:
                wind_direction:
                  display_text: '南西'
                  text_color: '#000000'
                wind_speed:
                  display_text: 6.4
                  text_color: '#ffffff'
                  background_color: '#000000'
                wind_speed_max: 
                  display_text: 6.8
                  text_color: '#ffffff'
                  background_color: '#000000'
                temperature: 
                  display_text: 25
                  text_color: '#ffffff'
                  background_color: '#000000'
                rainfall: 
                  display_text: 0.0
                  text_color: '#ffffff'
                  background_color: '#000000'
                effective_humidity: 
                  display_text: 35.2
                  text_color: '#ffffff'
                  background_color: '#000000'
                relative_humidity: 
                  display_text: 65.1
                  text_color: '#ffffff'
                  background_color: '#000000'
                atmospheric_pressure: 
                  display_text: 1,019.9
                  text_color: '#ffffff'
                  background_color: '#000000'
                observation_time: 
                  display_text: 10:10
                  text_color: '#ffffff'
                  background_color: '#000000'
                weather: 
                  display_text: 晴
                  text_color: '#ffffff'
                  background_color: '#000000'

    TotalFrequenceSample1:
      value:
        data:
          display_content_data:
            - source_no: 10
              source_name: 総合度数コンテンツ
              source_split_no: 0
              source_data:
                agg_unit: 
                  - display_text: 本日
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 本月
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 累計
                    text_color: '#ffffff'
                    background_color: '#000000'
                agg_info: 
                  - display_text: 119着信
                    text_color: '#ffffff'
                    background_color: '#000000'
                    number_list: 
                      - display_text: 3,456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 4,456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 6
                        text_color: '#ffffff'
                        background_color: '#000000'                                  
                  - display_text: 火災
                    text_color: '#ff0000'
                    background_color: '#000000'
                    number_list: 
                      - display_text: 3456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 56
                        text_color: '#ffffff'
                        background_color: '#000000'                                  
                  - display_text: 救急
                    text_color: '#00ff00'
                    background_color: '#000000'
                    number_list: 
                      - display_text: 6
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 4,456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 5,456
                        text_color: '#ffffff'
                        background_color: '#000000'                                  
                  - display_text: 救助
                    text_color: '#e8e800'
                    background_color: '#000000'
                    number_list: 
                      - display_text: 9,999
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 56
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 23,456
                        text_color: '#ffffff'
                        background_color: '#000000'                                  
                  - display_text: その他
                    text_color: '#00cdcd'
                    background_color: '#000000'
                    number_list: 
                      - display_text: 3,456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 4,456
                        text_color: '#ffffff'
                        background_color: '#000000'
                      - display_text: 345,456
                        text_color: '#ffffff'
                        background_color: '#000000'                                  


    AlarmSample1:
      value:
        data:
          display_content_data:
            - source_no: 11
              source_name: 予警報コンテンツ
              source_split_no: 0
              source_data:
                issued_ts:  
                  - display_text: 10月10日17時35分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日17時35分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日17時35分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日17時45分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日17時35分
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 10月10日17時35分
                    text_color: '#ffffff'
                    background_color: '#000000'
                combined_forecast_warning:  
                  - display_text: あいうえおかきくけこさしすせ
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: 大雨警報大雨警報大雨
                    text_color: '#ff0000'
                    background_color: '#000000'
                  - display_text: 雷注意報
                    text_color: '#ffff00'
                    background_color: '#000000'
                  - display_text: 雷注意報
                    text_color: '#ffff00'
                    background_color: '#000000'
                  - display_text: 雷注意報
                    text_color: '#ffff00'
                    background_color: '#000000'
                  - display_text: 雷注意報
                    text_color: '#ffff00'
                    background_color: '#000000'

    AttendanceSample1:
      value:
        data:
          display_content_data:
            - source_no: 12
              source_name: 出退コンテンツ
              source_split_no: 0
              source_data:
                official_position_name:  
                  - display_text: 消防長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 消防長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 総務課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 総務課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 通信指令課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 通信指令課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 予防課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 予防課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 総務課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 総務課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 通信指令課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 通信指令課長
                    text_color: '#ffffff'
                    background_color: '#000000'
                  - display_text: 予防課長
                    text_color: '#ffffff'
                    background_color: '#000000'


                attendance_dynamic_state_name:  
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 在籍
                    text_color: '#00ff00'
                    background_color: '#000000'
               

    DoctorOnDutySample1:
      value:
        data:
          display_content_data:
            - source_no: 13
              source_name: 当番医コンテンツ
              source_split_no: 0
              source_data:
                medical_subject:  
                  - display_text: 内科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: 'ヤトウ病院'
                      text_color: '#00ff00'
                      background_color: '#000000'
                    - display_text: '綾瀬病院'
                      text_color: '#00ff00'
                      background_color: '#000000'

                    medical_institution_telephone_no:  
                    - display_text: '1234-5678'
                      text_color: '#00dddd'
                      background_color: '#000000'
                    - display_text: '1234-2222'
                      text_color: '#00dddd'
                      background_color: '#000000'
                      
                  - display_text: 外科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: 中央病院
                      text_color: '#00ff00'
                      background_color: '#000000'
                    medical_institution_telephone_no:  
                    - display_text: 'あいうえおかき'
                      text_color: '#00dddd'
                      background_color: '#000000'

                  - display_text: 外科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: 横浜中央病院３町名8丁目
                      text_color: '#00ff00'
                      background_color: '#000000'
                    medical_institution_telephone_no:  
                    - display_text: '1234-5678'
                      text_color: '#00dddd'
                      background_color: '#000000'

                  - display_text: 外科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: ヤトウ病院
                      text_color: '#00ff00'
                      background_color: '#000000'
                    medical_institution_telephone_no:  
                    - display_text: '1234-5678'
                      text_color: '#00dddd'
                      background_color: '#000000'

                  - display_text: 外科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: あいう病院
                      text_color: '#00ff00'
                      background_color: '#000000'
                    medical_institution_telephone_no:  
                    - display_text: '1234-5678'
                      text_color: '#00dddd'
                      background_color: '#000000'

                  - display_text: 外科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: 病院
                      text_color: '#00ff00'
                      background_color: '#000000'
                    medical_institution_telephone_no:  
                    - display_text: '1234-5678'
                      text_color: '#00dddd'
                      background_color: '#000000'

                  - display_text: 外科
                    text_color: '#ffffff'
                    background_color: '#000000'
                    medical_institution_name:  
                    - display_text: あいう病院
                      text_color: '#00ff00'
                      background_color: '#000000'
                    medical_institution_telephone_no:  
                    - display_text: '1234-5678'
                      text_color: '#00dddd'
                      background_color: '#000000'
    HandoverFull:
      value:
        data:
          display_content_data:
            - source_no: 15
              source_name: 引継事項コンテンツ
              source_split_no: 0
              source_data:
                handover_listing:
                  - display_text: 水利点検よあいうえおかきくけこ水利点検
                    text_color: '#3BFEff'
                    background_color: '#0F0F0F'
                  - display_text: １２３４５６７８９０１２３４５６７８９
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 水利点検 ３０ページまで完了
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 水利点検よあいうえおかきくけこ
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: 水利点検 ３０ページまで完了
                    text_color: '#00ff00'
                    background_color: '#000000'
                  - display_text: あいうえおかきくけこさしすせそたち
                    text_color: '#00ff00'
                    background_color: '#000000'
    HandoverMin:
      value:
        data:
          display_content_data:
            - source_no: 15
              source_name: 引継事項コンテンツ
              source_split_no: 0
              source_data:
                handover_listing:
                  - display_text: 水利点検 ３０ページまで完了
                    text_color: '#00ff00'
                    background_color: '#000000'
                    
                    
    DigitRadioSample1:
      value:
        data:
          display_content_data:
            - source_no: 16
              source_name: デジタル無線コンテンツ
              source_split_no: 0
              source_data:
                wireless_channel_name:
                  - display_text: あいうえおかきくけこさしすせ
                    text_color: '#ffffff'
                    background_color: '#000000'
                    outgoing_call_move_station_name:
                      - display_text: 救急最大
                        text_color: '#000000'
                        background_color: '#00ff00'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                    incoming_call_move_station_name:
                      - display_text: 基地１
                        text_color: '#000000'
                        background_color: '#00ff00'
                      - display_text: 基地
                        text_color: '#ffffff'
                        background_color: '#000000'
                    incoming_call_ts:
                      - display_text: '15:32'
                        text_color: '#000000'
                        background_color: '#00ff00'
                      - display_text: '15:30'
                        text_color: '#ffffff'
                        background_color: '#000000'
                    blink_setting: 
                      - blink_time: 10
                        lighting_text_color: '#ffff40' 
                        lighting_background_color: '#45ffff'
                        lighting_status: 1
                        blink_speed: 10
                      - blink_time: 3
                        lighting_text_color: '#ffffff' 
                        lighting_background_color: '#0000ff'
                        lighting_status: 2
                        blink_speed: 1000
                  - display_text: 活動波2
                    text_color: '#ffffff'
                    background_color: '#000000'
                    outgoing_call_move_station_name:
                      - display_text: 救急中1
                        text_color: '#000000'
                        background_color: '#00ff00'

                    incoming_call_move_station_name:
                      - display_text: 基地1
                        text_color: '#000000'
                        background_color: '#00ff00'

                    incoming_call_ts:
                      - display_text: '15:32'
                        text_color: '#000000'
                        background_color: '#00ff00'

                    blink_setting: 
                      - blink_time: 9
                        lighting_text_color: '#ffff40' 
                        lighting_background_color: '#45ffff'
                        lighting_status: 3
                        blink_speed: 3000

                  - display_text: 活動波3
                    text_color: '#ffffff'
                    background_color: '#000000'
                    
                  - display_text: 活動波4
                    text_color: '#ffffff'
                    background_color: '#000000'
                    outgoing_call_move_station_name:
                      - display_text: 救急中1
                        text_color: '#000000'
                        background_color: '#00ff00'
                      - display_text: 救急中2
                        text_color: '#ffffff'
                        background_color: '#000000'
                    incoming_call_move_station_name:
                      - display_text: 基地1
                        text_color: '#000000'
                        background_color: '#00ff00'
                      - display_text: 基地2
                        text_color: '#ffffff'
                        background_color: '#000000'
                    incoming_call_ts:
                      - display_text: '15:32'
                        text_color: '#000000'
                        background_color: '#00ff00'
                      - display_text: '15:30'
                        text_color: '#ffffff'
                        background_color: '#000000'
                    blink_setting: 
                      - blink_time: 1
                        lighting_text_color: '#ffff40' 
                        lighting_background_color: '#45ffff'
                        lighting_status: 1
                        blink_speed: 10
                      - blink_time: 3
                        lighting_text_color: '#ffff40' 
                        lighting_background_color: '#45ffff'
                        lighting_status: 2
                        blink_speed: 1000


    AmbulanceRateRed:
      value:
        data:
          display_content_data:
            - source_no: 17
              source_name: 救急車稼働率コンテンツ
              source_split_no: 0
              source_data:
                title:
                  display_text: 出動救急車
                  text_color: '#ffffff'
                  background_color: '#ff0000'
                dispatch_num:
                  display_text: 999台
                  text_color: '#ffffff'
                  background_color: '#ff0000'
    AmbulanceRateYellow:
      value:
        data:
          display_content_data:
            - source_no: 17
              source_name: 救急車稼働率コンテンツ
              source_split_no: 0
              source_data:
                title:
                  display_text: 出動救急車
                  text_color: '#ffffff'
                  background_color: 'ffc000'
                dispatch_num:
                  display_text: '15台'
                  text_color: '#ffffff'
                  background_color: 'ffc000'
    AmbulanceRateGreen:
      value:
        data:
          display_content_data:
            - source_no: 17
              source_name: 救急車稼働率コンテンツ
              source_split_no: 0
              source_data:
                title:
                  display_text: 出動救急車
                  text_color: '#ffffff'
                  background_color: '#0adc0a'
                dispatch_num:
                  display_text: '13台'
                  text_color: '#ffffff'
                  background_color: '#0adc0a'
    AmbulanceRateBlack:
      value:
        data:
          display_content_data:
            - source_no: 17
              source_name: 救急車稼働率コンテンツ
              source_split_no: 0
              source_data:
                title:
                  display_text: 出動救急車
                  text_color: '#ffffff'
                  background_color: '#000000'
                dispatch_num:
                  display_text: '9台'
                  text_color: '#ffffff'
                  background_color: '#000000'
                  
                  
