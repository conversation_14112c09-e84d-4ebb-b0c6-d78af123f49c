package com.contents.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.contents.common.db.Control;
import com.contents.composition.MachineComposition;
import com.contents.manager.CompositionManager;
import com.contents.model.SyncTime;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 機器制御する処理をここで纏める
 */
@Service
@Data
@Slf4j
public class MachineControlService {

    private final int DISPLAY_TYPE_MULTI_MONITOR = 0;
    private final int DISPLAY_TYPE_SINGLE_MONITOR = 1;
    private final int IS_VOL_CONTROL_ENABLE = 1;
    
    private final MachineComposition machineComposition;
    
    @Autowired
    public MachineControlService() {
    	this.machineComposition =  CompositionManager.loadComposition();
    }


    public List<String> machineControl(List<Control> controlList) {
    	
    	return this.machineComposition.machineControl(controlList);
    }
    
    
    /**
     * ClientのMonitorをセットアップ時点で、既に受け取ったAPI情報を使って、機器制御を実施
     * @param controlList 制御パラメータリスト
     */
    public void machineControlWhenMonitorSetup(List<Control> controlList) {
    	this.machineComposition.machineControl(controlList);
    }

    /**
     * [3.28時刻同期]API用の関数。<br>
     * ※2022/11/12 時点で、時刻同期の制御を実施する必要がない(指令系APと映像系APが物理的に同じサーバーなので)
     * @param syncTime 制御パラメータ
     * @return 処理成功するかどうか。true:成功, false:失敗
     */
    public boolean timeSync(SyncTime syncTime) {
        // TODO: 時刻同期の制御を実施 (次期開発)
        return true;
    }
}
