import React from 'react';
import Cell from './elements/Cell';
import { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';

/**
 * 簡易事案コンテンツ(1-4サイズ)<br>
 * propsは、「3.7簡易事案コンテンツ(1-4サイズ)情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module CaseQuarter
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const CaseQuarter = (props) => {
  const MAX_ROW = 4;
  return (
    <>
      {isValidSource(props) &&
        props.case.map((item, index) => {
          //・1コンテンツあたり最大4件の情報を表示可能。
          if (index >= MAX_ROW) return undefined;

          return <CaseQuarterRow key={index} item={item} index={index} />;
        })}
    </>
  );
};

// 簡易事案コンテンツ(1-4サイズ)の一事案
const CaseQuarterRow = (props) => {
  let row1Cell1Props, row1Cell2Props, row1Cell3Props, row1Props;
  let row2Cell1Props, row2Cell2Props;

  const data = props.item;
  let obj = data.disaster_class;
  row1Props = { backgroundColor: getHexColor(obj.background_color) };
  row1Cell1Props = getCellFace(obj.disaster_type, 'col-span-4');
  row1Cell2Props = getCellFace(obj.case_no, 'col-span-7 col-start-6');
  row1Cell3Props = getCellFace(
    obj.fire_station_name,
    'col-span-4 col-start-14'
  );

  row2Cell1Props = getCellFace(
    data.awareness_time,
    'col-span-8 col-start-2 w-fit'
  );
  row2Cell2Props = getCellFace(
    data?.town_name,
    'col-span-10 col-start-11 w-fit'
  );
  
  return (
    <div className="text-6xl leading-[1]">
      <div
        style={row1Props}
        className="border-transparent border-x-[1rem] grid grid-cols-19 leading-none auto-cols-fr"
      >
        <Cell {...row1Cell1Props} />
        <Cell {...row1Cell2Props} />
        <Cell {...row1Cell3Props} />
      </div>
      <div className="grid grid-cols-21 text-6xl auto-cols-fr my-[2.35rem]">
        <Cell {...row2Cell1Props} />
        <Cell {...row2Cell2Props} />
      </div>
    </div>
  );
};

export default CaseQuarter;
