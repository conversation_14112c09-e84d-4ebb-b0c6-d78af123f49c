package com.contents.devicemodel.monitor;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

import com.contents.common.MessageFormatUtil;
import com.contents.common.db.Control;
import com.contents.common.external.cross.DeviceLinkItem;
import com.contents.common.external.cross.DeviceRoutingInfo;
import com.contents.common.external.cross.DisplayDevice;
import com.contents.common.external.cross.DisplayGroupInfo;
import com.contents.common.external.cross.DisplayRoutingInfo;
import com.contents.common.external.cross.DisplaySplitInfo;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.devicemodel.DeviceModel;
import com.contents.devicemodel.ExecutionPlan;
import com.contents.devicemodel.madiareceiver.MediaReceiverModel;
import com.contents.devicemodel.videosource.VideoSourceDeviceModel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class MonitorDeviceModel extends DeviceModel {

	protected DisplayDevice displayDevice;

	protected DisplayGroupInfo displayGroupInfo;

	protected DisplaySplitInfo displaySplitInfo;

	protected DisplayRoutingInfo displayRoutingInfo;

	/** 表示盤種別 */
	protected Integer displayType;

	/** 表示盤番号 */
	protected Integer displayNumber;

	/** 表示盤分割面番号 */
	protected Integer displaySplitNumber;

	

	/**
	 * ディスプレイデバイスをバインドします。
	 * @param displayDevice ディスプレイデバイス
	 */
	public void bind(DisplayDevice displayDevice) {
		this.displayDevice = displayDevice;
		this.deviceNumber = displayDevice.getDeviceNumber();
		this.productName = displayDevice.getProductName();
		this.networkInfo = displayDevice.getNetworkInfo();

	}

	public boolean match(Integer displayType, Integer displayNumber, Integer displaySplitNumber) {

		if (!Objects.equals(this.displayType, displayType))
			return false;

		if (!Objects.equals(this.displayNumber, displayNumber))
			return false;

		if (!Objects.equals(this.displaySplitNumber, displaySplitNumber))
			return false;

		return true;
	}

	public void expand(RoutingInformation routingInformation) {

		for (Iterator<DisplayGroupInfo> ite = routingInformation.getDisplayGroupManageList().iterator(); ite.hasNext();) {
			DisplayGroupInfo group = ite.next();

			java.util.Optional<DisplaySplitInfo> optional = group.getSplitList().stream().filter((split) -> Objects.equals(split.getDeviceNumber(), this.deviceNumber)).findFirst();

			if (!optional.isEmpty()) {

				this.displayGroupInfo = group;
				this.displaySplitInfo = optional.get();

				this.displayType = this.displayGroupInfo.getDisplayType();
				this.displayNumber = this.displayGroupInfo.getDisplayNumber();
				this.displaySplitNumber = this.displaySplitInfo.getDisplaySplitNumber();
			}
		}

		{
			java.util.Optional<DisplayRoutingInfo> optional = routingInformation.getDisplayRoutingList().stream().filter((link) -> Objects.equals(link.getDeviceNumber(), this.deviceNumber)).findFirst();
			if (!optional.isEmpty()) {
				this.displayRoutingInfo = optional.get();
			}
		}
	}
	
	private DeviceLinkItem pickupInputLinkl(Control control) {
		
		if (this.displayRoutingInfo == null)
			return null;
		
		Integer displayType = control.getDisplay_type();
		Integer displayNo = control.getDisplay_no();
		Integer displaySplitNo = control.getDisplay_split_no();
		Integer sourceNo = control.getSource_no();
		Integer inputSwitch = control.getInput_switch();
		
		if (this.displayType != displayType)
			return null;
		
		if (this.displayNumber != displayNo)
			return null;
		
		if (this.displaySplitNumber != displaySplitNo)
			return null;
		
		if (inputSwitch == null)
			return null;
		
		List<VideoSourceDeviceModel> videoSourceDeviceModelList = deviceModelInfo.getVideoSourceDeviceModelList();

		List<MediaReceiverModel> mediaReceiverModelList = deviceModelInfo.getMediaReceiverModelList();
		List<MonitorDeviceModel> monitorDeviceModelList = deviceModelInfo.getMonitorDeviceModelList();
		
		if (inputSwitch == 0) {
			// 「0」はメディアレシーバー
			MediaReceiverModel mediaReceiverModel = mediaReceiverModelList.stream().filter((model) -> model.match(displayType, displayNo, displaySplitNo)).findFirst().orElse(null);

			if (mediaReceiverModel == null)
				return null;

			MonitorDeviceModel monitorDeviceModel = monitorDeviceModelList.stream().filter((monitor) -> monitor.match(displayType, displayNo, displaySplitNo)).findFirst().orElse(null);

			if (monitorDeviceModel == null)
				return null;

			DeviceLinkItem inputLink = this.displayRoutingInfo.getInputList().stream().filter((input) -> Objects.equals(input.getDeviceNumber(), mediaReceiverModel.getDeviceNumber())).findFirst().orElse(null);

			return inputLink;
			
		} else if (inputSwitch == 1) {
			
			// 「1」はマトリックススイッチャー
			try {
				
				VideoSourceDeviceModel videoSourceDeviceModel = videoSourceDeviceModelList.stream().filter((model) -> Objects.equals(model.getVideoSourceNumber(), sourceNo)).findFirst().orElse(null);

				if (videoSourceDeviceModel == null) {
					log.info("non sourceNo {}", sourceNo);
					return null;
				}

				DeviceLinkItem inputLink = getDeviceLinkItem(videoSourceDeviceModel.getDeviceNumber());

				return inputLink;
				
			} catch (Exception e) {
				
				e.printStackTrace();
			}
			
		}
		
		
		return null;
	}
	
	private DeviceLinkItem getDeviceLinkItem(Integer videoSourceDeviceNumber) {
		
		Integer deviceNumber = videoSourceDeviceNumber;
		
		Iterator<DeviceRoutingInfo> ite = routingInformation.iteratorDeviceRoutingInfo();
		
		while(ite.hasNext()) {
			
			DeviceRoutingInfo deviceRoutingInfo = ite.next();
			
			if (deviceRoutingInfo.getDeviceNumber() == deviceNumber) {
				ite.remove();
				continue;
			}
			
			List<DeviceLinkItem> inputList = deviceRoutingInfo.getInputList();
			
			if (inputList == null) {
				ite.remove();
				continue;
			}
			
			DeviceLinkItem inpuLink = searchDeviceLinkItem(inputList, deviceNumber);
			
			if (inpuLink == null) {
				ite.remove();
				continue;
			}
			
			
			
			DeviceLinkItem displayInpuLink = this.displayRoutingInfo.getInputList().stream().filter((input) -> Objects.equals(input.getDeviceNumber(), inpuLink.getDeviceNumber())).findFirst().orElse(null);
			
			if (displayInpuLink != null) {
				
				return displayInpuLink;
			}
			
			
			
			deviceNumber = deviceRoutingInfo.getDeviceNumber();
			ite = routingInformation.iteratorDeviceRoutingInfo();
		}
		
		return null;
	}
	
	private DeviceLinkItem searchDeviceLinkItem(List<DeviceLinkItem> inputList, final Integer deviceNumber) {
		
		DeviceLinkItem inpuLink = inputList.stream().filter((input) -> Objects.equals(deviceNumber, input.getDeviceNumber())).findFirst().orElse(null);
		return inpuLink;
	}

	

	protected List<ExecutionPlan> createExecutionPlan(Control control) throws Exception {

		List<ExecutionPlan> executionPlanList = new ArrayList<ExecutionPlan>();

		Integer displayType = control.getDisplay_type();
		Integer displayNo = control.getDisplay_no();
		Integer displaySplitNo = control.getDisplay_split_no();
		Integer sourceNo = control.getSource_no();
		Integer inputSwitch = control.getInput_switch();
		Integer sourceDispPattern = control.getSource_disp_pat();

		try {
			
			DeviceLinkItem inputLink = pickupInputLinkl(control);
			
			if (inputLink == null)
				return null;
			
			ExecutionPlan plan = new ExecutionPlan() {

				@Override
				public List<String> transmit() {

					synchronized (TRANSMIT_LOCK_OBJECT) {

						List<String> errors = new ArrayList<String>();

						if (sourceDispPattern != null) {
							
							String format = "ディスプレイの切り替えに失敗しました。displayType={},displayNo={},displaySplitNo ={},sourceNo={},sourceDispPattern={}";
							Object[] params = new Object[] { displayType, displayNo, displaySplitNo, sourceNo, sourceDispPattern };
							
							try {
								boolean result = setMultiMonitor(sourceDispPattern);
								if (!result) {
									errors.add(MessageFormatUtil.format(format, params));
								}
							} catch (Exception e) {
								log.error(format, params);
								log.error("MonitorDeviceModel transmit setMultiMonitor", e);
								log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
								errors.add(MessageFormatUtil.format(format, params));
							}
						}

						if (inputSwitch != null || inputLink != null) {

							String format = "ディスプレイの切り替えに失敗しました。displayType={},displayNo={},displaySplitNo ={},sourceNo={},inputSwitch={}";
							Object[] params = new Object[] { displayType, displayNo, displaySplitNo, sourceNo, inputSwitch };
							
							try {
								boolean result = selectInput(inputLink.getConnectorNumber(), inputSwitch);
								if (!result) {
									errors.add(MessageFormatUtil.format(format, params));
								}
							} catch (Exception e) {
								log.error(format, params);
								log.error("MonitorDeviceModel transmit selectInput", e);
								log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
								errors.add(MessageFormatUtil.format(format, params));
							}
						}

						return errors;
					}
				}
			};
			
			executionPlanList.add(plan);
			
		} catch (Exception e) {
			log.error("createExecutionPlan control error.", e);
			log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
			String format = "ディスプレイの切り替えに失敗しました。model={}, ip={}, displayType={}, displayNo={}, displaySplitNo={}, sourceNo={}, inputSwitch={}, sourceDispPattern={}";
			Object[] params = new Object[] { this.productName, this.networkInfo.getIpAddress(), displayType, displayNo, displaySplitNo, sourceNo, inputSwitch, sourceDispPattern };
			String message = MessageFormatUtil.format(format, params);
			log.error(message);
			
			//throw new Exception(message, e);
		}

		return executionPlanList;
	}
	
//	protected boolean checkTransferringCommands() {
//		
//		// ネットワーク情報オブジェクトが存在しなければモニターに命令を転送しない
//		if (networkInfo == null)
//			return false;
//
//		// IPアドレスが存在しなければモニターに命令を転送しない
//		String ipAddress = networkInfo.getIpAddress();
//		if (StringUtils.isEmpty(ipAddress))
//			return false;
//		
//		// ポートまでチェックするかは、モニター依存
//		// ネットワーク情報以外の要素がある場合は、モニターごとに実装
//		
//		return true;
//	}

	public abstract boolean selectInput(Integer connectorNumber, Integer inputSwitch);

	public abstract boolean setMultiMonitor(Integer sourceDispPattern);

	/**
	 * displayGroupInfoを取得します。
	 * @return displayGroupInfo
	 */
	public DisplayGroupInfo getDisplayGroupInfo() {
		return displayGroupInfo;
	}

	/**
	 * displayLinkInfoを取得します。
	 * @return displayLinkInfo
	 */
	public DisplayRoutingInfo getDisplayRoutingInfo() {
		return displayRoutingInfo;
	}

	/**
	 * 表示盤種別を取得します。
	 * @return 表示盤種別
	 */
	public Integer getDisplayType() {
		return displayType;
	}

	/**
	 * 表示盤番号を取得します。
	 * @return 表示盤番号
	 */
	public Integer getDisplayNumber() {
		return displayNumber;
	}

	/**
	 * 表示盤分割面番号を取得します。
	 * @return 表示盤分割面番号
	 */
	public Integer getDisplaySplitNumber() {
		return displaySplitNumber;
	}

}
