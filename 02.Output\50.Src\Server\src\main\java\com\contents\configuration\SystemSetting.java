package com.contents.configuration;


public class SystemSetting {

	/** historyテーブル保持期間(単位:月) */
	private Integer apiHistoryMaxKeepMonth = 60;
	
	/** APIタイムアウト時間(単位:ミリ秒) */
	private Long apiTimeout = 10000L;
	
	/** 映像機器制御ON/OFF */
	private Boolean machineControlEnable = false;

	/**
	 * historyテーブル保持期間(単位:月)を取得します。
	 * @return historyテーブル保持期間(単位:月)
	 */
	public Integer getApiHistoryMaxKeepMonth() {
	    return apiHistoryMaxKeepMonth;
	}

	/**
	 * historyテーブル保持期間(単位:月)を設定します。
	 * @param apiHistoryMaxKeepMonth historyテーブル保持期間(単位:月)
	 */
	public void setApiHistoryMaxKeepMonth(Integer apiHistoryMaxKeepMonth) {
	    this.apiHistoryMaxKeepMonth = apiHistoryMaxKeepMonth;
	}

	/**
	 * APIタイムアウト時間(単位:ミリ秒)を取得します。
	 * @return APIタイムアウト時間(単位:ミリ秒)
	 */
	public Long getApiTimeout() {
	    return apiTimeout;
	}

	/**
	 * APIタイムアウト時間(単位:ミリ秒)を設定します。
	 * @param apiTimeout APIタイムアウト時間(単位:ミリ秒)
	 */
	public void setApiTimeout(Long apiTimeout) {
	    this.apiTimeout = apiTimeout;
	}

	/**
	 * 映像機器制御ON/OFFを取得します。
	 * @return 映像機器制御ON/OFF
	 */
	public Boolean getMachineControlEnable() {
	    return machineControlEnable;
	}

	/**
	 * 映像機器制御ON/OFFを設定します。
	 * @param machineControlEnable 映像機器制御ON/OFF
	 */
	public void setMachineControlEnable(Boolean machineControlEnable) {
	    this.machineControlEnable = machineControlEnable;
	}
}
