package com.contents.common.db;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * OptimisticLockを失敗する時のリトライ回数を指定するAnnotation
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RetryJpaOptimisticLock {

    /**
     * リトライ処理回数。デフォルト3回
     * @return リトライ処理回数
     */
    int times() default 3;
}
