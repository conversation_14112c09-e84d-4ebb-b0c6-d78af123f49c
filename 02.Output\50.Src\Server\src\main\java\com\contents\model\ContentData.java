package com.contents.model;

import java.util.Objects;
import com.contents.model.ContentDataDisplayContentData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ContentData
 */
@Validated


public class ContentData   {
  @JsonProperty("display_content_data")
  @Valid
  private List<ContentDataDisplayContentData> displayContentData = null;

  public ContentData displayContentData(List<ContentDataDisplayContentData> displayContentData) {
    this.displayContentData = displayContentData;
    return this;
  }

  public ContentData addDisplayContentDataItem(ContentDataDisplayContentData displayContentDataItem) {
    if (this.displayContentData == null) {
      this.displayContentData = new ArrayList<ContentDataDisplayContentData>();
    }
    this.displayContentData.add(displayContentDataItem);
    return this;
  }

  /**
   * Get displayContentData
   * @return displayContentData
   **/
  @Schema(description = "")
      @Valid
    public List<ContentDataDisplayContentData> getDisplayContentData() {
    return displayContentData;
  }

  public void setDisplayContentData(List<ContentDataDisplayContentData> displayContentData) {
    this.displayContentData = displayContentData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ContentData contentData = (ContentData) o;
    return Objects.equals(this.displayContentData, contentData.displayContentData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayContentData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ContentData {\n");
    
    sb.append("    displayContentData: ").append(toIndentedString(displayContentData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
