import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { StompSessionProvider } from 'react-stomp-hooks';
import { ErrorBoundary } from 'react-error-boundary';
import { <PERSON>rowserRouter, useLocation } from 'react-router-dom';
import axios from 'axios';

const root = ReactDOM.createRoot(document.getElementById('root'));

let context = process.env.REACT_APP_SERVER_CONTEXT;
if (context && !context.startsWith('/')) {
  context = `/${context}`;
}
let ws = process.env.REACT_APP_WEBSOCKET;
if (ws && !ws.startsWith('/')) {
  ws = `/${ws}`;
}

const reconnectDelay = 5000; // 5秒後に再接続を試みる

const handleStompConfig = () => ({
  // STOMP configurations
  reconnectDelay, // 再接続までの遅延時間
  onConnect: () => {
    console.log('Connected to WebSocket');
  },
  onDisconnect: () => {
    console.log('Disconnected from WebSocket, will attempt to reconnect');
  },
  onWebSocketClose: () => {
    console.log('WebSocket closed, will attempt to reconnect');
  },
  // その他の設定オプションを追加可能
});

const RootComponent = () => {

  const location = useLocation();
  const params = new URLSearchParams(location.search);
  let server_url = params.get('server_url');

  if (server_url == null) {
    const host = window.location.hostname;
    const port = window.location.port;
    const protocol = window.location.protocol;
    server_url = `${protocol}//${host}:${port}`;
  }


  const contentErrorHandler = (error, info) => {
    // Do something with the error
    let data = {
      name: error?.name,
      message: error?.messsage,
      stack: error?.stack,
      componentStack: info?.componentStack,
    };

    // エラー情報をサーバーに送る
    axios({
      headers: {
        'Content-Type': 'application/json',
      },
      method: 'post',
      url: `${server_url}${context}/saveFrontError`,
      data: data,
    }).then((res) => {
      if (res && res.status === 200) {
        console.log('Upload error success.');
      } else {
        console.log('Upload error fail.');
      }
    });
  };

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={contentErrorHandler}
      onReset={() => {
        // reset the state of your app so the error doesn't happen again
      }}
    >
      <StompSessionProvider
        url={`${server_url}${context}${ws}`}
        config={handleStompConfig}
      >
        <App />
      </StompSessionProvider>
    </ErrorBoundary>
  );
};

/**
 * ErrorBoundaryでエラーをCatchした時に、ユーザーに表示する画面
 *
 * @param {*} { error, resetErrorBoundary }
 * @return {*} エラー画面
 */
function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div className="bg-black grid min-h-screen grid-rows-1 grid-cols-1 place-content-center text-3xl">
      <div className="flex flex-col items-center text-green-400">
        <p>エラー発生: </p>
        <pre>{error.message}</pre>
        <br />
        <br />
        <button className="text-red-800" onClick={resetErrorBoundary}>
          ここをクリックして、もう一度試してください
        </button>
      </div>
    </div>
  );
}

root.render(
  <BrowserRouter basename={process.env.REACT_APP_APP_URL}>
    <RootComponent />
  </BrowserRouter>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
