package com.contents.common.db;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;

/**
 * 表示盤喚起音吹鳴テーブルのDAO
 */
@Repository
@RepositoryRestResource(path = "sound")
public interface SoundDao extends JpaRepository<Sound, Integer>, Serializable {
    /**
     * 最新の受信時間で、指定したCount行を返す
     *
     * @param count　返す必要な行数
     * @return 行のリスト
     */
    @Query(value = "select * from Sound t order by receive_time desc limit :count", nativeQuery = true)
    List<Sound> findRowByLastReceiveTime(@Param("count") Integer count);
}
