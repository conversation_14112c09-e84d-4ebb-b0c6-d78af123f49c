package com.contents.configuration.websocket;

import com.contents.common.bean.ScreenInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;

import java.util.ArrayList;

/**
 * WebSocketのChannel Interceptor　<br>
 * MonitorのBrowserから最初Registryした時に、DBにMonitorステータス保存
 */
@Component
public class CustomChannelInterceptor implements ChannelInterceptor {

    private final MonitorHandler monitorHandler;


    public CustomChannelInterceptor(MonitorHandler monitorHandler) {
        this.monitorHandler = monitorHandler;
    }

    /**
     * STOMPのSubscribe処理で、表示盤番号/面番号をMonitorServiceに保存(紐づくKey：SessionID)。
     * 該当MonitorのステータスをOnlineに設定。※browserがサーバーより、
     * 先に起動する場合もWebSocketがReconnectの仕組みがあり、Subscribe処理を再実施
     * @param message
     * @param channel
     * @return message
     */
    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = StompHeaderAccessor.wrap(message);
        StompCommand command = accessor.getCommand();
        if (StompCommand.SUBSCRIBE == command) {
            monitorRegister(accessor);
        }
        return message;
    }

    /**
     * クライアントのMonitorをDBに登録する
     *
     * @param accessor
     */
    private void monitorRegister(StompHeaderAccessor accessor) {
        ScreenInfo screenInfo = monitorHandler.getMonitor(accessor.getSessionId());
        if (screenInfo != null) {
            return;
        }
        LinkedMultiValueMap map = (LinkedMultiValueMap) accessor.getHeader("nativeHeaders");
        if (map == null) {
            return;
        }

        ArrayList displayNoList = (ArrayList) map.get("displayNo");
        if (displayNoList == null) {
            return;
        }
        Object displayNoObj = displayNoList.get(0);

        if (displayNoObj == null || StringUtils.equalsIgnoreCase(displayNoObj.toString(), "null")) {
            return;
        }
        Integer displayNo = Integer.valueOf((String) displayNoList.get(0));

        ArrayList splitNoList = (ArrayList) map.get("splitNo");
        if (splitNoList == null) {
            return;
        }
        Object splitNoObj = splitNoList.get(0);

        if (splitNoObj == null || StringUtils.equalsIgnoreCase(splitNoObj.toString(), "null")) {
            return;
        }
        Integer splitNo = Integer.valueOf((String) splitNoList.get(0));

        screenInfo = new ScreenInfo();
        screenInfo.setDisplayNo(displayNo);
        screenInfo.setSplitNo(splitNo);

        monitorHandler.register(accessor.getSessionId(), screenInfo);
    }
}
