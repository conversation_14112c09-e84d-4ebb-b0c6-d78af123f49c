package com.contents.common;

import com.contents.common.db.MonitorDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * SpringBoot起動する時で発生するイベント。<br>
 * システム起動で初期化する必要な処理をここで実施
 */
@Component
@Slf4j
public class ContextRefreshedListener implements ApplicationListener<ContextRefreshedEvent> {
    private final MonitorDao monitorDao;

    public ContextRefreshedListener(MonitorDao monitorDao) {
        this.monitorDao = monitorDao;
    }

    /**
     * MonitorをすべてOfflineにする<br>
     * この後WebSocketで、ブラウザからサーバーに接続すれば、該当MonitorをOnlineに設定
     * @param event the event to respond to
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        //root application context
        if (event.getApplicationContext().getParent() == null) {
            log.info("Spring boot started and update all monitor to offline");
            monitorDao.update2Offline(new Date());
        }
    }
}