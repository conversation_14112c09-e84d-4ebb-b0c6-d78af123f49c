package com.contents.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 
 */
@Data
public class DisplayItem {

	/**
	 * コンストラクタ
	 */
	public DisplayItem() {
		
	}
	
	/**
	 * コンストラクタ
	 * @param displayText 表示テキスト
	 * @param textColor 文字色
	 * @param backgroundColor 背景色
	 */
	public DisplayItem(String displayText, String textColor, String backgroundColor) {
		this.displayText = displayText;
		this.textColor = textColor;
		this.backgroundColor = backgroundColor;
	}
	
	/** 表示テキスト */
	@JsonProperty("display_text")
	private String displayText;
	
	/** 文字色 */
	@JsonProperty("text_color")
	private String textColor;
	
	/** 背景色 */
	@JsonProperty("background_color")
	private String backgroundColor;
}
