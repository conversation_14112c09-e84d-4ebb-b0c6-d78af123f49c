import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import { replaceWithHtmlSpace } from '../../utils/Util.js';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const propTypes = {
  display_text: PropTypes.string,
};

const ScaleBlock = (props) => {
  const defaultState = {
    scale: 1,
    translate: 0,
  };

  const [state, setState] = useState(defaultState);
  const [width, height] = useWindowSize();

  let ref = useRef(null);
  useEffect(() => {
    const { offsetWidth, parentElement } = ref.current;
    let contentWidth = offsetWidth;
    let boxWidth = parentElement?.offsetWidth;

    let tempScale = 1;
    if (contentWidth && boxWidth) {
      tempScale = boxWidth / contentWidth;

      if (tempScale < 1) {
        setState({
          scale: tempScale,
          translate: ((1 - tempScale) / 2) * 100,
        });
      }
    }
  }, [props.display_text, width, height]);

  let refinedText = replaceWithHtmlSpace(props?.display_text || props?.text);

  return (
    <StyledSpan
      ref={ref}
      dangerouslySetInnerHTML={{ __html: refinedText }}
      scale={state.scale}
      translate={state.translate}
    ></StyledSpan>
  );
};

const StyledSpan = styled.span`
  white-space: nowrap;
  display: inline-block;
  transform: scaleX(${(props) => props.scale});
  translate: -${(props) => props.translate}%;
`;

function useWindowSize() {
  const [size, setSize] = useState([0, 0]);
  useLayoutEffect(() => {
    function updateSize() {
      setSize([window.innerWidth, window.innerHeight]);
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);
  return size;
}

ScaleBlock.propTypes = propTypes;
export default ScaleBlock;
