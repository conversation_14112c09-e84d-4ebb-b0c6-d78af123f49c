D:\99.DevSoft\10.JDK\bin\java.exe -Dmaven.multiModuleProjectDirectory=D:\11.Working\20.AutoControl\02.Output\50.Src\Server "-Dmaven.home=E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\plugins\maven\lib\maven3" "-Dclassworlds.conf=E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\plugins\maven\lib\maven3\bin\m2.conf" "-Dmaven.ext.class.path=E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\plugins\maven\lib\maven-event-listener.jar" "-javaagent:E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\lib\idea_rt.jar=50587:E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\bin" -Dfile.encoding=UTF-8 -classpath "E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\plugins\maven\lib\maven3\boot\plexus-classworlds-2.6.0.jar;E:\Program Files\JetBrains\IntelliJ IDEA 2022.1.3\plugins\maven\lib\maven3\boot\plexus-classworlds.license" org.codehaus.classworlds.Launcher -Didea.version=2022.1.3 clean package

pause