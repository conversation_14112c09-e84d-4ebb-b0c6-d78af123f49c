package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseQuarterContentDisasterClassDisasterType
 */
@Validated


public class CaseQuarterContentDisasterClassDisasterType   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  public CaseQuarterContentDisasterClassDisasterType displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 災害種別
   * @return displayText
   **/
  @Schema(description = "災害種別")
  
  @Size(max=4)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public CaseQuarterContentDisasterClassDisasterType textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseQuarterContentDisasterClassDisasterType caseQuarterContentDisasterClassDisasterType = (CaseQuarterContentDisasterClassDisasterType) o;
    return Objects.equals(this.displayText, caseQuarterContentDisasterClassDisasterType.displayText) &&
        Objects.equals(this.textColor, caseQuarterContentDisasterClassDisasterType.textColor);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseQuarterContentDisasterClassDisasterType {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
