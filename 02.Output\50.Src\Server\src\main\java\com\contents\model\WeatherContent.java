package com.contents.model;

import java.util.Objects;
import com.contents.model.WeatherContentAtmosphericPressure;
import com.contents.model.WeatherContentEffectiveHumidity;
import com.contents.model.WeatherContentObservationTime;
import com.contents.model.WeatherContentRainfall;
import com.contents.model.WeatherContentRelativeHumidity;
import com.contents.model.WeatherContentTemperature;
import com.contents.model.WeatherContentWeather;
import com.contents.model.WeatherContentWindDirection;
import com.contents.model.WeatherContentWindSpeed;
import com.contents.model.WeatherContentWindSpeedMax;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * WeatherContent
 */
@Validated


public class WeatherContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("wind_speed")
  private WeatherContentWindSpeed windSpeed = null;

  @JsonProperty("wind_direction")
  private WeatherContentWindDirection windDirection = null;

  @JsonProperty("wind_speed_max")
  private WeatherContentWindSpeedMax windSpeedMax = null;

  @JsonProperty("temperature")
  private WeatherContentTemperature temperature = null;

  @JsonProperty("rainfall")
  private WeatherContentRainfall rainfall = null;

  @JsonProperty("effective_humidity")
  private WeatherContentEffectiveHumidity effectiveHumidity = null;

  @JsonProperty("relative_humidity")
  private WeatherContentRelativeHumidity relativeHumidity = null;

  @JsonProperty("atmospheric_pressure")
  private WeatherContentAtmosphericPressure atmosphericPressure = null;

  @JsonProperty("observation_time")
  private WeatherContentObservationTime observationTime = null;

  @JsonProperty("weather")
  private WeatherContentWeather weather = null;

  public WeatherContent windSpeed(WeatherContentWindSpeed windSpeed) {
    this.windSpeed = windSpeed;
    return this;
  }

  /**
   * Get windSpeed
   * @return windSpeed
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentWindSpeed getWindSpeed() {
    return windSpeed;
  }

  public void setWindSpeed(WeatherContentWindSpeed windSpeed) {
    this.windSpeed = windSpeed;
  }

  public WeatherContent windDirection(WeatherContentWindDirection windDirection) {
    this.windDirection = windDirection;
    return this;
  }

  /**
   * Get windDirection
   * @return windDirection
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentWindDirection getWindDirection() {
    return windDirection;
  }

  public void setWindDirection(WeatherContentWindDirection windDirection) {
    this.windDirection = windDirection;
  }

  public WeatherContent windSpeedMax(WeatherContentWindSpeedMax windSpeedMax) {
    this.windSpeedMax = windSpeedMax;
    return this;
  }

  /**
   * Get windSpeedMax
   * @return windSpeedMax
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentWindSpeedMax getWindSpeedMax() {
    return windSpeedMax;
  }

  public void setWindSpeedMax(WeatherContentWindSpeedMax windSpeedMax) {
    this.windSpeedMax = windSpeedMax;
  }

  public WeatherContent temperature(WeatherContentTemperature temperature) {
    this.temperature = temperature;
    return this;
  }

  /**
   * Get temperature
   * @return temperature
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentTemperature getTemperature() {
    return temperature;
  }

  public void setTemperature(WeatherContentTemperature temperature) {
    this.temperature = temperature;
  }

  public WeatherContent rainfall(WeatherContentRainfall rainfall) {
    this.rainfall = rainfall;
    return this;
  }

  /**
   * Get rainfall
   * @return rainfall
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentRainfall getRainfall() {
    return rainfall;
  }

  public void setRainfall(WeatherContentRainfall rainfall) {
    this.rainfall = rainfall;
  }

  public WeatherContent effectiveHumidity(WeatherContentEffectiveHumidity effectiveHumidity) {
    this.effectiveHumidity = effectiveHumidity;
    return this;
  }

  /**
   * Get effectiveHumidity
   * @return effectiveHumidity
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentEffectiveHumidity getEffectiveHumidity() {
    return effectiveHumidity;
  }

  public void setEffectiveHumidity(WeatherContentEffectiveHumidity effectiveHumidity) {
    this.effectiveHumidity = effectiveHumidity;
  }

  public WeatherContent relativeHumidity(WeatherContentRelativeHumidity relativeHumidity) {
    this.relativeHumidity = relativeHumidity;
    return this;
  }

  /**
   * Get relativeHumidity
   * @return relativeHumidity
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentRelativeHumidity getRelativeHumidity() {
    return relativeHumidity;
  }

  public void setRelativeHumidity(WeatherContentRelativeHumidity relativeHumidity) {
    this.relativeHumidity = relativeHumidity;
  }

  public WeatherContent atmosphericPressure(WeatherContentAtmosphericPressure atmosphericPressure) {
    this.atmosphericPressure = atmosphericPressure;
    return this;
  }

  /**
   * Get atmosphericPressure
   * @return atmosphericPressure
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentAtmosphericPressure getAtmosphericPressure() {
    return atmosphericPressure;
  }

  public void setAtmosphericPressure(WeatherContentAtmosphericPressure atmosphericPressure) {
    this.atmosphericPressure = atmosphericPressure;
  }

  public WeatherContent observationTime(WeatherContentObservationTime observationTime) {
    this.observationTime = observationTime;
    return this;
  }

  /**
   * Get observationTime
   * @return observationTime
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentObservationTime getObservationTime() {
    return observationTime;
  }

  public void setObservationTime(WeatherContentObservationTime observationTime) {
    this.observationTime = observationTime;
  }

  public WeatherContent weather(WeatherContentWeather weather) {
    this.weather = weather;
    return this;
  }

  /**
   * Get weather
   * @return weather
   **/
  @Schema(description = "")
  
    @Valid
    public WeatherContentWeather getWeather() {
    return weather;
  }

  public void setWeather(WeatherContentWeather weather) {
    this.weather = weather;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WeatherContent weatherContent = (WeatherContent) o;
    return Objects.equals(this.windSpeed, weatherContent.windSpeed) &&
        Objects.equals(this.windDirection, weatherContent.windDirection) &&
        Objects.equals(this.windSpeedMax, weatherContent.windSpeedMax) &&
        Objects.equals(this.temperature, weatherContent.temperature) &&
        Objects.equals(this.rainfall, weatherContent.rainfall) &&
        Objects.equals(this.effectiveHumidity, weatherContent.effectiveHumidity) &&
        Objects.equals(this.relativeHumidity, weatherContent.relativeHumidity) &&
        Objects.equals(this.atmosphericPressure, weatherContent.atmosphericPressure) &&
        Objects.equals(this.observationTime, weatherContent.observationTime) &&
        Objects.equals(this.weather, weatherContent.weather);
  }

  @Override
  public int hashCode() {
    return Objects.hash(windSpeed, windDirection, windSpeedMax, temperature, rainfall, effectiveHumidity, relativeHumidity, atmosphericPressure, observationTime, weather);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WeatherContent {\n");
    
    sb.append("    windSpeed: ").append(toIndentedString(windSpeed)).append("\n");
    sb.append("    windDirection: ").append(toIndentedString(windDirection)).append("\n");
    sb.append("    windSpeedMax: ").append(toIndentedString(windSpeedMax)).append("\n");
    sb.append("    temperature: ").append(toIndentedString(temperature)).append("\n");
    sb.append("    rainfall: ").append(toIndentedString(rainfall)).append("\n");
    sb.append("    effectiveHumidity: ").append(toIndentedString(effectiveHumidity)).append("\n");
    sb.append("    relativeHumidity: ").append(toIndentedString(relativeHumidity)).append("\n");
    sb.append("    atmosphericPressure: ").append(toIndentedString(atmosphericPressure)).append("\n");
    sb.append("    observationTime: ").append(toIndentedString(observationTime)).append("\n");
    sb.append("    weather: ").append(toIndentedString(weather)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
