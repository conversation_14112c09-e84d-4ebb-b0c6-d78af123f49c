package com.contents.common.bean;

import com.contents.common.CommonUtil;
import com.contents.common.db.Content;
import com.contents.common.db.Control;
import com.contents.common.db.Monitor;
import com.contents.pojo.MatrixIndexInfo;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class TemporaryContentData {

	private Integer displayType;

	private Integer displayNo;

	private Integer displaySplitNo;

	private Integer displayDetailsSplitNo;

	private MatrixIndexInfo matrixIndexInfo;

	private Integer sourceDisplayPattern;

	private Integer sourceNo;

	private Integer sourceSplitNo;

	private String sourceName;

	private Content dbContent;

	private Control dbControl;

	private Content convertContent;

	private Monitor monitor;

	public void bind(Control control) {

		this.dbControl = control;

		this.displayType = control.getDisplay_type();
		this.displayNo = control.getDisplay_no();
		this.displaySplitNo = control.getDisplay_split_no();
		this.displayDetailsSplitNo = control.getDisplay_details_split_no();

		this.sourceDisplayPattern = control.getSource_disp_pat();
		this.sourceNo = control.getSource_no();
		this.sourceSplitNo = control.getSource_split_no();
		this.sourceName = control.getSource_name();
	}

	public boolean isMonitorReady() {
		
		if (monitor == null)
			return false;
		
		Integer monitorStatus = monitor.getStatus();

		if (monitorStatus == null) {
			log.info("monitor table is empty. displayNo: {}, displaySplitNo: ", displayNo, displaySplitNo);
			return false;
		}

		if (monitorStatus != CommonUtil.MONITOR_STATUS_CONNECTED) {
			log.info("monitor is not ready. displayNo: {}, displaySplitNo: ", displayNo, displaySplitNo);
			return false;
		}

		return true;
	}

	public Content adoptContent() {

		// 変換したコンテンツデータがある場合は変換したデータを採用
		if (convertContent != null)
			return convertContent;

		return dbContent;
	}

	public String createControlWebSocketEndpoint() {

		return CommonUtil.getWebSocketId(displayNo, displaySplitNo);
	}

	public String createContentWebSocketEndpoint() {

		Integer afterDetailSplitNo = displayDetailsSplitNo;
		if (displayType == 1) {

			if (afterDetailSplitNo == 2)
				afterDetailSplitNo = 4;

			if (afterDetailSplitNo == 3)
				afterDetailSplitNo = 5;
		}

		return CommonUtil.getWebSocketId(displayNo, displayDetailsSplitNo, afterDetailSplitNo);
	}
	
	public ScreenInfo createClockScreenInfo() {

		Integer afterDetailSplitNo = displayDetailsSplitNo;
		
		if (displayType == 1) {

			if (afterDetailSplitNo == 2)
				afterDetailSplitNo = 4;

			if (afterDetailSplitNo == 3)
				afterDetailSplitNo = 5;
		}

		ScreenInfo screenInfo = new ScreenInfo();
		screenInfo.setDisplayNo(displayNo);
		screenInfo.setSplitNo(displaySplitNo);
		screenInfo.setDetailSplitNo(afterDetailSplitNo);
		
		return screenInfo;
	}
	
	public ContentInfo createClockContentInfo() {

		Integer afterDetailSplitNo = displayDetailsSplitNo;
		
		if (displayType == 1) {

			if (afterDetailSplitNo == 2)
				afterDetailSplitNo = 4;

			if (afterDetailSplitNo == 3)
				afterDetailSplitNo = 5;
		}

		return new ContentInfo(sourceNo, sourceSplitNo, afterDetailSplitNo);
	}
}
