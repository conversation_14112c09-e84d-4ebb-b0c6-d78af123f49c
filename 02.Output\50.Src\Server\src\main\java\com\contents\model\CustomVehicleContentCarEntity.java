package com.contents.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class CustomVehicleContentCarEntity extends CustomVehicleContentEntity {

	/** 配備 */
	@JsonProperty("deployment")
	private DisplayItem deployment;
	
	/** 車両名称 */
	@JsonProperty("car_name")
	private DisplayItem carName;
	
	/** 現地名称 */
	@JsonProperty("town_name")
	private DisplayItem townName;
	
	/** 災害種別 */
	@JsonProperty("disaster_type")
	private DisplayItem disasterType;
	
	/** AVM動態 */
	@JsonProperty("avm_dynamic_state")
	private DisplayItem avmDynamicState;
	
	/** 照明設定 */
	@JsonProperty("lighting_setting")
	private DisplayEffect lightingSetting;
}
