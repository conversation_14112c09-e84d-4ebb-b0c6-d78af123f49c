import React from 'react';
import Cell from './elements/Cell';
import CellBox from './elements/CellBox';
import { getCellFace, isValidSource, getHexColor } from '../utils/Util.js';

/**
 * 救急車稼働率コンテンツ<br>
 * propsは、「3.18救急車稼働率コンテンツ情報更新」のsource_data部分のAPI仕様に従う
 *
 * @module AmbulanceRate
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const AmbulanceRate = (props) => {
  if (!isValidSource(props)) {
    return;
  }

  const title = props.title;
  const dispatch_num = props.dispatch_num;

  let totalStyle = { backgroundColor: getHexColor(title?.background_color) };

  let row1Cell1Props = getCellFace(title, 'mt-[2.3rem] text-10xl col-start-2 col-span-5  justify-self-center');
  let row2Cell1Props = getCellFace(
    dispatch_num,
    'mt-[1.5rem] col-start-2 col-span-4 justify-self-end'
  );
  row2Cell1Props.text = detachUnit(row2Cell1Props.text);

  return (
    <div style={totalStyle} className="h-full w-full">
      <div className="grid grid-cols-1 grid-rows-2 place-items-center leading-[1] gap-y-[3rem]">
		<div className="grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(5,10rem)_minmax(0.25rem,2fr)]">
		  <Cell {...row1Cell1Props} />
		</div>
        <div className="grid grid grid-cols-[minmax(0.25rem,2fr)_repeat(4,13rem)_minmax(0.25rem,2fr)] text-[23rem]">
          <CellBox {...row2Cell1Props} >
            {row2Cell1Props.text}
            <span className="text-12xl">台</span>
          </CellBox>
        </div>
      </div>
    </div>
  );
};

/**
 * 単位の「台」なくして返す
 *
 * @export
 * @param {*} num
 * @return {*}　「台」なくす文字列
 */
function detachUnit(num) {
  if (num == null) {
    return num;
  }

  if (num.endsWith('台')) {
    return num.substring(0, num.length - 1);
  } else {
    return num;
  }
}

export default AmbulanceRate;
