package com.contents.manager;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.contents.composition.MachineComposition;

import lombok.extern.slf4j.Slf4j;

/**
 * 外部機器管理
 */
@Component
@EnableScheduling
@EnableAsync
@Slf4j
public class ExternalTaskManager {

	public ExternalTaskManager() {
		
	}
	
	/**
     * 周期処理
     */
    @Async("externalTask")
    //@Scheduled(initialDelay = 60000, fixedDelay = 300000)
    @Scheduled(initialDelay = 15000, fixedDelay = (300 * 1000))
    public void manageExec() {
    	
    	log.info("ExternalTask start.");
    	
    	try {
    		
    		boolean machineControlEnable = SystemSettingManager.getSystemSetting().getMachineControlEnable();
    		
    		if (!machineControlEnable)
    			return;
    		
    		MachineComposition machineComposition = CompositionManager.loadComposition();
        	
    		machineComposition.manageExec();
			
		} catch (Exception e) {
			
			log.error("error manageExec.", e);
			
		} finally {
			
			log.info("ExternalTask end.");
		}
    	
    }
    
    /**
     * 接続状態取得
     * @return 処理結果 true:正常, false:異常
     */
    public boolean checkHealth() {
    	
    	MachineComposition machineComposition = CompositionManager.loadComposition();
    	return machineComposition.checkHealth();
    }
}
