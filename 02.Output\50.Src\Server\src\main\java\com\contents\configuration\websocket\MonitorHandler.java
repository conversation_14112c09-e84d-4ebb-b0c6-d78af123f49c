package com.contents.configuration.websocket;

import com.contents.common.SpringContextUtil;
import com.contents.common.bean.ScreenInfo;
import com.contents.service.SystemService;

import lombok.Data;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Monitor情報をここで纏めてハンドルする
 */
@Component
@Data
@DependsOn("springContextUtil")
public class MonitorHandler {
    private final Map<String, ScreenInfo> sessionMap = new ConcurrentHashMap<>();

    public void register(String sessionId, ScreenInfo screenInfo) {
        sessionMap.put(sessionId, screenInfo);

        Thread thread = new Thread(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            monitorSetup(screenInfo.getDisplayNo(), screenInfo.getSplitNo());
        });
        thread.start();
    }

    public void remove(String sessionId) {
        ScreenInfo screenInfo = sessionMap.remove(sessionId);

        monitorDown(screenInfo.getDisplayNo(), screenInfo.getSplitNo());
    }

    public ScreenInfo getMonitor(String sessionId) {
        return sessionMap.get(sessionId);
    }

    private void monitorSetup(Integer displayNo, Integer splitNo) {
        var systemService = SpringContextUtil.getBean("systemService", SystemService.class);
        systemService.monitorSetup(displayNo, splitNo);
    }

    private void monitorDown(Integer displayNo, Integer splitNo) {
        var systemService = SpringContextUtil.getBean("systemService", SystemService.class);
        systemService.monitorDown(displayNo, splitNo);
    }
}