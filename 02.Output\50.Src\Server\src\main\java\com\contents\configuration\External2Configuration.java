package com.contents.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.contents.common.external.cross.DeviceInfomation;

/**
 * 外部設定情報
 */
@Component
@ConfigurationProperties(prefix = "app.configuration.external2")
public class External2Configuration {
    
	/** デバイス情報 */
	private DeviceInfomation deviceInfomation;

	/**
	 * デバイス情報を取得します。
	 * @return デバイス情報
	 */
	public DeviceInfomation getDeviceInfomation() {
	    return deviceInfomation;
	}

	/**
	 * デバイス情報を設定します。
	 * @param deviceInfomation デバイス情報
	 */
	public void setDeviceInfomation(DeviceInfomation deviceInfomation) {
	    this.deviceInfomation = deviceInfomation;
	}
	

}