package com.contents.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * WeatherContentWindDirection
 */
@Validated


public class WeatherContentWindDirection   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("display_color")
  private String displayColor = null;

  public WeatherContentWindDirection displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 風向※絵で表現
   * @return displayText
   **/
  @Schema(description = "風向※絵で表現")
  
    public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public WeatherContentWindDirection displayColor(String displayColor) {
    this.displayColor = displayColor;
    return this;
  }

  /**
   * Get displayColor
   * @return displayColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getDisplayColor() {
    return displayColor;
  }

  public void setDisplayColor(String displayColor) {
    this.displayColor = displayColor;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WeatherContentWindDirection weatherContentWindDirection = (WeatherContentWindDirection) o;
    return Objects.equals(this.displayText, weatherContentWindDirection.displayText) &&
        Objects.equals(this.displayColor, weatherContentWindDirection.displayColor);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, displayColor);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WeatherContentWindDirection {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    displayColor: ").append(toIndentedString(displayColor)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
