package com.contents.api;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

public interface UtilityApi {

	@Operation(summary = "ユーティリティ", description = "", tags = { "ユーティリティAPI" })
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "正しく受信できた", content = @Content(mediaType = "text/plain")) })
	@RequestMapping(value = "/server_health", method = RequestMethod.GET)
	String health();
	
	@Operation(summary = "ユーティリティ", description = "", tags = { "ユーティリティAPI" })
	@ApiResponses(value = {
			@ApiResponse(responseCode = "200", description = "正しく受信できた", content = @Content(mediaType = "text/plain")) })
	@RequestMapping(value = "/version", method = RequestMethod.GET)
	String version();
}
