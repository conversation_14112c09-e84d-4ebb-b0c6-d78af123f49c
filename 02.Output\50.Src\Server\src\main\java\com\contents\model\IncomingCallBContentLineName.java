package com.contents.model;

import java.util.Objects;
import com.contents.model.IncomingCallBContentIncomingCall;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * IncomingCallBContentLineName
 */
@Validated


public class IncomingCallBContentLineName   {
  @JsonProperty("display_text")
  private String displayText = null;

  @JsonProperty("text_color")
  private String textColor = null;

  @JsonProperty("background_color")
  private String backgroundColor = null;

  @JsonProperty("incoming_call")
  @Valid
  private List<IncomingCallBContentIncomingCall> incomingCall = null;

  public IncomingCallBContentLineName displayText(String displayText) {
    this.displayText = displayText;
    return this;
  }

  /**
   * 回線名
   * @return displayText
   **/
  @Schema(description = "回線名")
  
  @Size(max=8)   public String getDisplayText() {
    return displayText;
  }

  public void setDisplayText(String displayText) {
    this.displayText = displayText;
  }

  public IncomingCallBContentLineName textColor(String textColor) {
    this.textColor = textColor;
    return this;
  }

  /**
   * Get textColor
   * @return textColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getTextColor() {
    return textColor;
  }

  public void setTextColor(String textColor) {
    this.textColor = textColor;
  }

  public IncomingCallBContentLineName backgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
    return this;
  }

  /**
   * Get backgroundColor
   * @return backgroundColor
   **/
  @Schema(description = "")
  
  @Pattern(regexp="^[#0-9a-fA-F]{7}$")   public String getBackgroundColor() {
    return backgroundColor;
  }

  public void setBackgroundColor(String backgroundColor) {
    this.backgroundColor = backgroundColor;
  }

  public IncomingCallBContentLineName incomingCall(List<IncomingCallBContentIncomingCall> incomingCall) {
    this.incomingCall = incomingCall;
    return this;
  }

  public IncomingCallBContentLineName addIncomingCallItem(IncomingCallBContentIncomingCall incomingCallItem) {
    if (this.incomingCall == null) {
      this.incomingCall = new ArrayList<IncomingCallBContentIncomingCall>();
    }
    this.incomingCall.add(incomingCallItem);
    return this;
  }

  /**
   * Get incomingCall
   * @return incomingCall
   **/
  @Schema(description = "")
      @Valid
  @Size(max=6)   public List<IncomingCallBContentIncomingCall> getIncomingCall() {
    return incomingCall;
  }

  public void setIncomingCall(List<IncomingCallBContentIncomingCall> incomingCall) {
    this.incomingCall = incomingCall;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IncomingCallBContentLineName incomingCallBContentLineName = (IncomingCallBContentLineName) o;
    return Objects.equals(this.displayText, incomingCallBContentLineName.displayText) &&
        Objects.equals(this.textColor, incomingCallBContentLineName.textColor) &&
        Objects.equals(this.backgroundColor, incomingCallBContentLineName.backgroundColor) &&
        Objects.equals(this.incomingCall, incomingCallBContentLineName.incomingCall);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayText, textColor, backgroundColor, incomingCall);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IncomingCallBContentLineName {\n");
    
    sb.append("    displayText: ").append(toIndentedString(displayText)).append("\n");
    sb.append("    textColor: ").append(toIndentedString(textColor)).append("\n");
    sb.append("    backgroundColor: ").append(toIndentedString(backgroundColor)).append("\n");
    sb.append("    incomingCall: ").append(toIndentedString(incomingCall)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
