package com.contents.model;

import java.util.Objects;
import com.contents.model.ScheduleContentScheduleContent;
import com.contents.model.ScheduleContentScheduleTs;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ScheduleContent
 */
@Validated


public class ScheduleContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("schedule_ts")
  @Valid
  private List<ScheduleContentScheduleTs> scheduleTs = null;

  @JsonProperty("schedule_content")
  @Valid
  private List<ScheduleContentScheduleContent> scheduleContent = null;

  public ScheduleContent scheduleTs(List<ScheduleContentScheduleTs> scheduleTs) {
    this.scheduleTs = scheduleTs;
    return this;
  }

  public ScheduleContent addScheduleTsItem(ScheduleContentScheduleTs scheduleTsItem) {
    if (this.scheduleTs == null) {
      this.scheduleTs = new ArrayList<ScheduleContentScheduleTs>();
    }
    this.scheduleTs.add(scheduleTsItem);
    return this;
  }

  /**
   * Get scheduleTs
   * @return scheduleTs
   **/
  @Schema(description = "")
      @Valid
  @Size(max=6)   public List<ScheduleContentScheduleTs> getScheduleTs() {
    return scheduleTs;
  }

  public void setScheduleTs(List<ScheduleContentScheduleTs> scheduleTs) {
    this.scheduleTs = scheduleTs;
  }

  public ScheduleContent scheduleContent(List<ScheduleContentScheduleContent> scheduleContent) {
    this.scheduleContent = scheduleContent;
    return this;
  }

  public ScheduleContent addScheduleContentItem(ScheduleContentScheduleContent scheduleContentItem) {
    if (this.scheduleContent == null) {
      this.scheduleContent = new ArrayList<ScheduleContentScheduleContent>();
    }
    this.scheduleContent.add(scheduleContentItem);
    return this;
  }

  /**
   * Get scheduleContent
   * @return scheduleContent
   **/
  @Schema(description = "")
      @Valid
  @Size(max=6)   public List<ScheduleContentScheduleContent> getScheduleContent() {
    return scheduleContent;
  }

  public void setScheduleContent(List<ScheduleContentScheduleContent> scheduleContent) {
    this.scheduleContent = scheduleContent;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ScheduleContent scheduleContent = (ScheduleContent) o;
    return Objects.equals(this.scheduleTs, scheduleContent.scheduleTs) &&
        Objects.equals(this.scheduleContent, scheduleContent.scheduleContent);
  }

  @Override
  public int hashCode() {
    return Objects.hash(scheduleTs, scheduleContent);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ScheduleContent {\n");
    
    sb.append("    scheduleTs: ").append(toIndentedString(scheduleTs)).append("\n");
    sb.append("    scheduleContent: ").append(toIndentedString(scheduleContent)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
