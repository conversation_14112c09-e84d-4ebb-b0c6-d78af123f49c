package com.contents.model;

import java.util.Objects;
import com.contents.model.ControlMonitorDisplayDetailsSplitData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * ControlMonitorDisplaySplitData
 */
@Validated


public class ControlMonitorDisplaySplitData   {
  @JsonProperty("display_split_no")
  private Integer displaySplitNo = null;

  @JsonProperty("display_details_split_data")
  @Valid
  private List<ControlMonitorDisplayDetailsSplitData> displayDetailsSplitData = null;

  public ControlMonitorDisplaySplitData displaySplitNo(Integer displaySplitNo) {
    this.displaySplitNo = displaySplitNo;
    return this;
  }

  /**
   * 表示盤の画面毎に付与される番号
   * minimum: 0
   * maximum: 3
   * @return displaySplitNo
   **/
  @Schema(description = "表示盤の画面毎に付与される番号")
  
  @Min(0) @Max(3)   public Integer getDisplaySplitNo() {
    return displaySplitNo;
  }

  public void setDisplaySplitNo(Integer displaySplitNo) {
    this.displaySplitNo = displaySplitNo;
  }

  public ControlMonitorDisplaySplitData displayDetailsSplitData(List<ControlMonitorDisplayDetailsSplitData> displayDetailsSplitData) {
    this.displayDetailsSplitData = displayDetailsSplitData;
    return this;
  }

  public ControlMonitorDisplaySplitData addDisplayDetailsSplitDataItem(ControlMonitorDisplayDetailsSplitData displayDetailsSplitDataItem) {
    if (this.displayDetailsSplitData == null) {
      this.displayDetailsSplitData = new ArrayList<ControlMonitorDisplayDetailsSplitData>();
    }
    this.displayDetailsSplitData.add(displayDetailsSplitDataItem);
    return this;
  }

  /**
   * Get displayDetailsSplitData
   * @return displayDetailsSplitData
   **/
  @Schema(description = "")
      @Valid
    public List<ControlMonitorDisplayDetailsSplitData> getDisplayDetailsSplitData() {
    return displayDetailsSplitData;
  }

  public void setDisplayDetailsSplitData(List<ControlMonitorDisplayDetailsSplitData> displayDetailsSplitData) {
    this.displayDetailsSplitData = displayDetailsSplitData;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ControlMonitorDisplaySplitData controlMonitorDisplaySplitData = (ControlMonitorDisplaySplitData) o;
    return Objects.equals(this.displaySplitNo, controlMonitorDisplaySplitData.displaySplitNo) &&
        Objects.equals(this.displayDetailsSplitData, controlMonitorDisplaySplitData.displayDetailsSplitData);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displaySplitNo, displayDetailsSplitData);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ControlMonitorDisplaySplitData {\n");
    
    sb.append("    displaySplitNo: ").append(toIndentedString(displaySplitNo)).append("\n");
    sb.append("    displayDetailsSplitData: ").append(toIndentedString(displayDetailsSplitData)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
