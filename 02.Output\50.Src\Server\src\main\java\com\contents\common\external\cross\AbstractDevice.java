package com.contents.common.external.cross;

public abstract class AbstractDevice {

	/** デバイス番号 */
	protected Integer deviceNumber;
	
	/** 製品名称(モデル名称)/仮想デバイス名称 */
	protected String productName;

	/**
	 * デバイス番号を取得します。
	 * @return デバイス番号
	 */
	public Integer getDeviceNumber() {
	    return deviceNumber;
	}

	/**
	 * デバイス番号を設定します。
	 * @param deviceNumber デバイス番号
	 */
	public void setDeviceNumber(Integer deviceNumber) {
	    this.deviceNumber = deviceNumber;
	}

	/**
	 * 製品名称(モデル名称)を取得します。
	 * @return 製品名称(モデル名称)
	 */
	public String getProductName() {
	    return productName;
	}

	/**
	 * 製品名称(モデル名称)を設定します。
	 * @param productName 製品名称(モデル名称)
	 */
	public void setProductName(String productName) {
	    this.productName = productName;
	}
}
