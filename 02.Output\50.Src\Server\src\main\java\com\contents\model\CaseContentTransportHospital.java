package com.contents.model;

import java.util.Objects;
import com.contents.model.CaseContentTransportHospitalDisplayData;
import com.contents.model.ChangeSetting;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * CaseContentTransportHospital
 */
@Validated


public class CaseContentTransportHospital   {
  @JsonProperty("display_data")
  @Valid
  private List<CaseContentTransportHospitalDisplayData> displayData = null;

  @JsonProperty("change_setting")
  private ChangeSetting changeSetting = null;

  public CaseContentTransportHospital displayData(List<CaseContentTransportHospitalDisplayData> displayData) {
    this.displayData = displayData;
    return this;
  }

  public CaseContentTransportHospital addDisplayDataItem(CaseContentTransportHospitalDisplayData displayDataItem) {
    if (this.displayData == null) {
      this.displayData = new ArrayList<CaseContentTransportHospitalDisplayData>();
    }
    this.displayData.add(displayDataItem);
    return this;
  }

  /**
   * Get displayData
   * @return displayData
   **/
  @Schema(description = "")
      @Valid
    public List<CaseContentTransportHospitalDisplayData> getDisplayData() {
    return displayData;
  }

  public void setDisplayData(List<CaseContentTransportHospitalDisplayData> displayData) {
    this.displayData = displayData;
  }

  public CaseContentTransportHospital changeSetting(ChangeSetting changeSetting) {
    this.changeSetting = changeSetting;
    return this;
  }

  /**
   * Get changeSetting
   * @return changeSetting
   **/
  @Schema(description = "")
  
    @Valid
    public ChangeSetting getChangeSetting() {
    return changeSetting;
  }

  public void setChangeSetting(ChangeSetting changeSetting) {
    this.changeSetting = changeSetting;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CaseContentTransportHospital caseContentTransportHospital = (CaseContentTransportHospital) o;
    return Objects.equals(this.displayData, caseContentTransportHospital.displayData) &&
        Objects.equals(this.changeSetting, caseContentTransportHospital.changeSetting);
  }

  @Override
  public int hashCode() {
    return Objects.hash(displayData, changeSetting);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CaseContentTransportHospital {\n");
    
    sb.append("    displayData: ").append(toIndentedString(displayData)).append("\n");
    sb.append("    changeSetting: ").append(toIndentedString(changeSetting)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
