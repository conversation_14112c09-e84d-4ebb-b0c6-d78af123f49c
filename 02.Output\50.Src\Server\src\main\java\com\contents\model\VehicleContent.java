package com.contents.model;

import java.util.Objects;
import com.contents.model.VehicleContentTitleName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * VehicleContent
 */
@Validated


public class VehicleContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("is_deployment")
  private Integer isDeployment = null;

  @JsonProperty("title_name")
  @Valid
  private List<VehicleContentTitleName> titleName = null;

  public VehicleContent isDeployment(Integer isDeployment) {
    this.isDeployment = isDeployment;
    return this;
  }

  /**
   * 配備状況の表示を行うか否か 1：表示する 2：表示しない
   * minimum: 1
   * maximum: 2
   * @return isDeployment
   **/
  @Schema(description = "配備状況の表示を行うか否か 1：表示する 2：表示しない")
  
  @Min(1) @Max(2)   public Integer getIsDeployment() {
    return isDeployment;
  }

  public void setIsDeployment(Integer isDeployment) {
    this.isDeployment = isDeployment;
  }

  public VehicleContent titleName(List<VehicleContentTitleName> titleName) {
    this.titleName = titleName;
    return this;
  }

  public VehicleContent addTitleNameItem(VehicleContentTitleName titleNameItem) {
    if (this.titleName == null) {
      this.titleName = new ArrayList<VehicleContentTitleName>();
    }
    this.titleName.add(titleNameItem);
    return this;
  }

  /**
   * Get titleName
   * @return titleName
   **/
  @Schema(description = "")
      @Valid
    public List<VehicleContentTitleName> getTitleName() {
    return titleName;
  }

  public void setTitleName(List<VehicleContentTitleName> titleName) {
    this.titleName = titleName;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    VehicleContent vehicleContent = (VehicleContent) o;
    return Objects.equals(this.isDeployment, vehicleContent.isDeployment) &&
        Objects.equals(this.titleName, vehicleContent.titleName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(isDeployment, titleName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class VehicleContent {\n");
    
    sb.append("    isDeployment: ").append(toIndentedString(isDeployment)).append("\n");
    sb.append("    titleName: ").append(toIndentedString(titleName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
