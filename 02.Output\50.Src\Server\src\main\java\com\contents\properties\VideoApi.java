package com.contents.properties;

public class VideoApi {

	/** 機器コントールON/OFF */
	private Boolean deviceControl;
	
	/** 履歴データ保持期間(月) */
	private Integer historyKeepMonth;
	
	/** APIタイムアウト */
	private Long apiTimeout;

	/**
	 * 機器コントールON/OFFを取得します。
	 * @return 機器コントールON/OFF
	 */
	public Boolean getDeviceControl() {
	    return deviceControl;
	}

	/**
	 * 機器コントールON/OFFを設定します。
	 * @param deviceControl 機器コントールON/OFF
	 */
	public void setDeviceControl(Boolean deviceControl) {
	    this.deviceControl = deviceControl;
	}

	/**
	 * 履歴データ保持期間(月)を取得します。
	 * @return 履歴データ保持期間(月)
	 */
	public Integer getHistoryKeepMonth() {
	    return historyKeepMonth;
	}

	/**
	 * 履歴データ保持期間(月)を設定します。
	 * @param historyKeepMonth 履歴データ保持期間(月)
	 */
	public void setHistoryKeepMonth(Integer historyKeepMonth) {
	    this.historyKeepMonth = historyKeepMonth;
	}

	/**
	 * APIタイムアウトを取得します。
	 * @return APIタイムアウト
	 */
	public Long getApiTimeout() {
	    return apiTimeout;
	}

	/**
	 * APIタイムアウトを設定します。
	 * @param apiTimeout APIタイムアウト
	 */
	public void setApiTimeout(Long apiTimeout) {
	    this.apiTimeout = apiTimeout;
	}
}
