package com.contents.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class DisplayEffect {

	/** コンストラクタ */
	public DisplayEffect() {
	}

	/**
	 * コンストラクタ
	 * @param blinkTime 点滅時間
	 * @param lightingTextColor 点灯時文字色
	 * @param lightingBackgroundColor 点灯時背景色
	 * @param lightingStatus 点灯状態
	 * @param blinkSpeed 点滅速度
	 */
	public DisplayEffect(Integer blinkTime, String lightingTextColor, String lightingBackgroundColor, String lightingStatus, Integer blinkSpeed) {
		this.blinkTime = blinkTime;
		this.lightingTextColor = lightingTextColor;
		this.lightingBackgroundColor = lightingBackgroundColor;
		this.lightingStatus = lightingStatus;
		this.blinkSpeed = blinkSpeed;
	}

	/** 点滅時間 */
	@JsonProperty("blink_time")
	private Integer blinkTime = null;

	/** 点灯時文字色 */
	@JsonProperty("lighting_text_color")
	private String lightingTextColor = null;

	/** 点灯時背景色 */
	@JsonProperty("lighting_background_color")
	private String lightingBackgroundColor = null;

	/** 点灯状態 */
	@JsonProperty("lighting_status")
	private String lightingStatus = null;

	/** 点滅速度 */
	@JsonProperty("blink_speed")
	private Integer blinkSpeed = null;
}
