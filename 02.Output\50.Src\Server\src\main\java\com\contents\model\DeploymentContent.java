package com.contents.model;

import java.util.Objects;
import com.contents.model.DeploymentContentArrow;
import com.contents.model.DeploymentContentCarName;
import com.contents.model.DeploymentContentCarType;
import com.contents.model.DeploymentContentDeployment;
import com.contents.model.DeploymentContentMoveCarName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import javax.validation.Valid;
import javax.validation.constraints.*;

/**
 * DeploymentContent
 */
@Validated


public class DeploymentContent  implements AnyOfcontentDataDisplayContentDataSourceData {
  @JsonProperty("deployment")
  @Valid
  private List<DeploymentContentDeployment> deployment = null;

  @JsonProperty("car_name")
  @Valid
  private List<DeploymentContentCarName> carName = null;

  @JsonProperty("arrow")
  @Valid
  private List<DeploymentContentArrow> arrow = null;

  @JsonProperty("move_car_name")
  @Valid
  private List<DeploymentContentMoveCarName> moveCarName = null;

  @JsonProperty("car_type")
  @Valid
  private List<DeploymentContentCarType> carType = null;

  public DeploymentContent deployment(List<DeploymentContentDeployment> deployment) {
    this.deployment = deployment;
    return this;
  }

  public DeploymentContent addDeploymentItem(DeploymentContentDeployment deploymentItem) {
    if (this.deployment == null) {
      this.deployment = new ArrayList<DeploymentContentDeployment>();
    }
    this.deployment.add(deploymentItem);
    return this;
  }

  /**
   * Get deployment
   * @return deployment
   **/
  @Schema(description = "")
      @Valid
  @Size(max=7)   public List<DeploymentContentDeployment> getDeployment() {
    return deployment;
  }

  public void setDeployment(List<DeploymentContentDeployment> deployment) {
    this.deployment = deployment;
  }

  public DeploymentContent carName(List<DeploymentContentCarName> carName) {
    this.carName = carName;
    return this;
  }

  public DeploymentContent addCarNameItem(DeploymentContentCarName carNameItem) {
    if (this.carName == null) {
      this.carName = new ArrayList<DeploymentContentCarName>();
    }
    this.carName.add(carNameItem);
    return this;
  }

  /**
   * Get carName
   * @return carName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=7)   public List<DeploymentContentCarName> getCarName() {
    return carName;
  }

  public void setCarName(List<DeploymentContentCarName> carName) {
    this.carName = carName;
  }

  public DeploymentContent arrow(List<DeploymentContentArrow> arrow) {
    this.arrow = arrow;
    return this;
  }

  public DeploymentContent addArrowItem(DeploymentContentArrow arrowItem) {
    if (this.arrow == null) {
      this.arrow = new ArrayList<DeploymentContentArrow>();
    }
    this.arrow.add(arrowItem);
    return this;
  }

  /**
   * Get arrow
   * @return arrow
   **/
  @Schema(description = "")
      @Valid
  @Size(max=7)   public List<DeploymentContentArrow> getArrow() {
    return arrow;
  }

  public void setArrow(List<DeploymentContentArrow> arrow) {
    this.arrow = arrow;
  }

  public DeploymentContent moveCarName(List<DeploymentContentMoveCarName> moveCarName) {
    this.moveCarName = moveCarName;
    return this;
  }

  public DeploymentContent addMoveCarNameItem(DeploymentContentMoveCarName moveCarNameItem) {
    if (this.moveCarName == null) {
      this.moveCarName = new ArrayList<DeploymentContentMoveCarName>();
    }
    this.moveCarName.add(moveCarNameItem);
    return this;
  }

  /**
   * Get moveCarName
   * @return moveCarName
   **/
  @Schema(description = "")
      @Valid
  @Size(max=7)   public List<DeploymentContentMoveCarName> getMoveCarName() {
    return moveCarName;
  }

  public void setMoveCarName(List<DeploymentContentMoveCarName> moveCarName) {
    this.moveCarName = moveCarName;
  }

  public DeploymentContent carType(List<DeploymentContentCarType> carType) {
    this.carType = carType;
    return this;
  }

  public DeploymentContent addCarTypeItem(DeploymentContentCarType carTypeItem) {
    if (this.carType == null) {
      this.carType = new ArrayList<DeploymentContentCarType>();
    }
    this.carType.add(carTypeItem);
    return this;
  }

  /**
   * Get carType
   * @return carType
   **/
  @Schema(description = "")
      @Valid
  @Size(max=7)   public List<DeploymentContentCarType> getCarType() {
    return carType;
  }

  public void setCarType(List<DeploymentContentCarType> carType) {
    this.carType = carType;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeploymentContent deploymentContent = (DeploymentContent) o;
    return Objects.equals(this.deployment, deploymentContent.deployment) &&
        Objects.equals(this.carName, deploymentContent.carName) &&
        Objects.equals(this.arrow, deploymentContent.arrow) &&
        Objects.equals(this.moveCarName, deploymentContent.moveCarName) &&
        Objects.equals(this.carType, deploymentContent.carType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(deployment, carName, arrow, moveCarName, carType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DeploymentContent {\n");
    
    sb.append("    deployment: ").append(toIndentedString(deployment)).append("\n");
    sb.append("    carName: ").append(toIndentedString(carName)).append("\n");
    sb.append("    arrow: ").append(toIndentedString(arrow)).append("\n");
    sb.append("    moveCarName: ").append(toIndentedString(moveCarName)).append("\n");
    sb.append("    carType: ").append(toIndentedString(carType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
