package com.contents.manager;

import java.util.HashMap;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Component
public class SocketManager {
	
    /** Socket管理用Map (key(例): 127.0.0.1:13000 value: SocketClientインスタンス) */
    private static HashMap<String, SocketClient> socketMap = new HashMap<String, SocketClient>();

    /**
     * IPとPortの組み合わせから、対象のSocketClient取得する
     * SocketClientが使用中の場合は
     * SocketClientが Close (isUsed=false) されるまで待機する
     * @param ip 接続先IP
     * @param port 接続先Port
     * @return SocketClient
     */
    public static SocketClient getSocketClient(String ip, int port) {
    	
        String key = ip + ":" + port;   // keyの組立 (例)127.0.0.1:13000
        SocketClient socket;

        if (socketMap.containsKey(key)) {
            while (true) {
                socket = socketMap.get(key);
                if (socket == null) {
                    // SocketClientを生成しMapに格納
                    log.warn("Socket is null. Create a new socket. {}", key);
                    socketMap.put(key, new SocketClient(ip, port));
                    socket = socketMap.get(key);
                    break;

                } else if (!socket.isUsing()) {
                    // SocketClientが未使用になったので待機状態から抜ける
                    break;
                }
            }
        } else {
            // SocketClientを生成しMapに格納
            log.info("New socket. {}", key);
            socketMap.put(key, new SocketClient(ip, port));
            socket = socketMap.get(key);
        }

        log.info("Get socket. {}", key);
        socket.use();
        return socket;
    }
}
