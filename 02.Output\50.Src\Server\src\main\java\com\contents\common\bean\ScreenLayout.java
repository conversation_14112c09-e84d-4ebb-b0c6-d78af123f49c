package com.contents.common.bean;

import lombok.Data;

/**
 * ブラウザに送る表示盤制御情報
 */
@Data
public class ScreenLayout {
    /**
     * Rowの数
     */
    private Integer rowNum;
    /**
     * Colの数
     */
    private Integer colNum;
    /**
     * 該当メッセージのID
     */
    private String id;

    public ScreenLayout(Integer rowNum, Integer colNum) {
        this.rowNum = rowNum;
        this.colNum = colNum;
    }
}
