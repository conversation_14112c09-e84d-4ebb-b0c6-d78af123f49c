import React from 'react';
import { getCellFace, isValidSource } from '../utils/Util.js';
import CellBox from './elements/CellBox';
import PropTypes from 'prop-types';

const propTypes = {
  year: PropTypes.string.isRequired,
  month: PropTypes.string.isRequired,
  day: PropTypes.string.isRequired,
  weekday: PropTypes.string.isRequired,
  hour: PropTypes.string.isRequired,
  minute: PropTypes.string.isRequired,
  text_color: PropTypes.string,
  background_color: PropTypes.string,
};

/**
 * 時刻コンテンツB<br>
 * propsは、「3.28時刻同期」のsource_data部分のAPI仕様に従う
 *
 * @module Now
 * @component
 * @param {*} props
 * @returns 表示内容
 */
const Now = (props) => {
  // Propsのtext_colorが優先で、なければ、text-[#40ffff]が効く
  let cell1Props = getCellFace(props, 'ml-[9rem] mt-[2rem] text-10xl text-[#40ffff]');
  let cell2Props = getCellFace(props, 'mt-[1.5rem] text-[23rem] text-[#40ffff]');

  return (
    <>
      {isValidSource(props) && (
        <div className="grid grid-cols-1 grid-rows-2 place-items-center auto-cols-fr leading-[1] gap-y-[3rem]">
		   <div className="grid auto-cols-max">
          <CellBox {...cell1Props}>
            {props.year}
            <span className="text-6xl mx-[1rem]">年</span>
            { Number(props.month) < 10 && (
              <>
                <span className="text-black">0</span>
              </>
            )}
            {props.month}
            <span className="text-6xl mx-[1rem]">月</span>
            { Number(props.day) < 10 && (
              <>
                <span className="text-black">0</span>
              </>
            )}
            {props.day}
            <span className="text-6xl ml-[1rem]">日</span>
            <span>（</span>
            {props.weekday}
            <span>）</span>
          </CellBox>
		  </div> 
          <CellBox {...cell2Props}>
            { Number(props.hour) < 10 && (
              <>
                <span className="text-black">0</span>
              </>
            )}
            {props.hour}
            <span className="text-12xl mx-[1rem]">時</span>
            {props.minute}
            <span className="text-12xl ml-[1rem]">分</span>
          </CellBox>
        </div>
      )}
    </>
  );
};

Now.propTypes = propTypes;
export default Now;
