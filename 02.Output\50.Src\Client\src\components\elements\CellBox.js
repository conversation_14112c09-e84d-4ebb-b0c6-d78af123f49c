import React from 'react';
import { getHexColor } from '../../utils/Util.js';
import PropTypes from 'prop-types';

const propTypes = {
  className: PropTypes.string,
  text_color: PropTypes.string,
  background_color: PropTypes.string,
};

/**
 * 色/背景色/文字列の情報を従い、子供のHtmlTagを表示する
 * @module CellBox
 * @component
 * @param {*} props 
 * @returns div箱
 */
const CellBox = (props) => {
  const textStyle = {};

  if (props.text_color) {
    textStyle.color = getHexColor(props.text_color);
  }

  if (props.background_color) {
    textStyle.backgroundColor = getHexColor(props.background_color);
  }

  return (
    <div className={props?.className?.trim()} style={textStyle}>
      {props.children}
    </div>
  );
};

CellBox.propTypes = propTypes;
export default CellBox;
