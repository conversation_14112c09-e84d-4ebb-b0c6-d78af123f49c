package com.contents.devicemodel.monitor;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import javax.xml.bind.DatatypeConverter;

import com.contents.common.CommonUtil;
import com.contents.common.annotation.ExternalDeviceProduct;
import com.contents.manager.CrossSocketClient;
import com.contents.pojo.Ping;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * モニターデバイスモデルクラス
 * <p>
 * 対応機種: LCD-UN462VA, LCD-X464UN
 */
@Slf4j
@ExternalDeviceProduct(names = { "LCD-UN462VA", "LCD-X464UN" })
public class LcdUn462Va extends MonitorDeviceModel {

	/* コマンドパケット ヘッダー 各要素位置 */
	private final int HDR_IDX_SOH = 0;
	private final int HDR_IDX_RESERVED = 1;
	private final int HDR_IDX_DEST = 2;
	private final int HDR_IDX_SOURCE = 3;
	private final int HDR_IDX_MSG_TYPE = 4;
	private final int HDR_IDX_MSG_LEN = 5;

	/* コマンドパケット ヘッダー サイズ */
	private final int HDR_SIZE_MSG_LEN = 2;
	private final int HDR_SIZE_ALL = 7;

	/* コマンドパケット ヘッダー 定数 */
	private final byte HDR_SOH = 0x01;
	private final byte HDR_RESERVED = 0x30;
	private final byte HDR_MSG_TYPE_CMD = 0x41;
	private final byte HDR_MSG_TYPE_CMD_REPLY = 0x42;
	private final byte HDR_MSG_TYPE_GET_PARAM = 0x43;
	private final byte HDR_MSG_TYPE_GET_PARAM_REPLY = 0x44;
	private final byte HDR_MSG_TYPE_SET_PARAM = 0x45;
	private final byte HDR_MSG_TYPE_SET_PARAM_REPLY = 0x46;
	private final byte MSG_STX = 0x02;
	private final byte MSG_ETX = 0x03;

	/* 簡単タイルマトリクス 設定値 */
	private final int AUTO_TILE_MATRIX_H_MON_NUM = 2;
	private final int AUTO_TILE_MATRIX_V_MON_NUM = 2;

	/* 各種送信禁止期間 設定値 */
	/** 入力切替コマンド実行後 コマンド送信禁止期間 (msec) */
	private final int NO_SENDING_PERIOD_MSEC_INPUT_SW = 10 * 1000;
	/** 電源切替コマンド実行後 コマンド送信禁止期間 (msec) */
	private final int NO_SENDING_PERIOD_MSEC_POWER_SW = 15 * 1000;

	/* PINGタイムアウト設定値 (sec) */
	private final int PING_TIMEOUT = 1;
	/* ソケット 各種タイムアウト設定値 (msec) */
	private final int CONNECT_TIMEOUT = 5000;
	private final int RECEIVE_TIMEOUT = 20 * 1000;

	@Data
	protected class ErrorCode {

		/** エラーコード */
		private String code;
		/** 説明 */
		private String explanation;

		public ErrorCode(String code, String explanation) {
			this.code = code;
			this.explanation = explanation;
		}
	}

	@Data
	protected class ConnectorInfo {

		public ConnectorInfo(Integer connectorNumber, String connectorName, Integer value, boolean selectable) {
			this.connectorNumber = connectorNumber;
			this.connectorName = connectorName;
			this.value = value;
			this.selectable = selectable;
		}

		/** コネクター番号 */
		private Integer connectorNumber;

		/** コネクター名称 */
		private String connectorName;

		/** 値 */
		private Integer value;

		/** 選択可否 */
		private boolean selectable;

	}

	protected final List<ConnectorInfo> CONNECTOR_LIST;

	protected final List<ErrorCode> ERROR_CODE_LIST;

	public LcdUn462Va() {
		this.ERROR_CODE_LIST = createErrorCodeList();
		this.CONNECTOR_LIST = createConnectorList();
	}

	protected List<ErrorCode> createErrorCodeList() {
		List<ErrorCode> list = new ArrayList<>();
		list.add(new ErrorCode("70", "スタンバイ電源+3.3V 異常"));
		list.add(new ErrorCode("71", "スタンバイ電源+5V 異常"));
		list.add(new ErrorCode("72", "パネル電源+12V 異常"));
		list.add(new ErrorCode("78", "インバータ電源/オプション・スロット２電源+24V 異常"));
		list.add(new ErrorCode("80", "冷却ファン-1 異常"));
		list.add(new ErrorCode("81", "冷却ファン-2 異常"));
		list.add(new ErrorCode("82", "冷却ファン-3 異常"));
		list.add(new ErrorCode("83", "COMPUTE MODULE 冷却ファン異常"));
		list.add(new ErrorCode("90", "インバータ異常"));
		list.add(new ErrorCode("91", "LED バックライト異常"));
		list.add(new ErrorCode("A0", "温度異常– シャットダウン"));
		list.add(new ErrorCode("A1", "温度異常– 輝度低下"));
		list.add(new ErrorCode("A2", "Sensor がユーザ指定温度に達した"));
		list.add(new ErrorCode("B0", "NO SIGNAL"));
		list.add(new ErrorCode("D0", "Proof of Play ログメモリ減少"));
		list.add(new ErrorCode("D1", "RTC エラー"));
		list.add(new ErrorCode("E0", "システムエラー"));
		return list;
	}

	protected List<ConnectorInfo> createConnectorList() {

		List<ConnectorInfo> list = new ArrayList<ConnectorInfo>();
		list.add(new ConnectorInfo(1, "VGA", 1, true));
		list.add(new ConnectorInfo(2, "DVI", 3, true));
		list.add(new ConnectorInfo(3, "VIDEO", 5, false));
		list.add(new ConnectorInfo(4, "YGA", 12, false));
		list.add(new ConnectorInfo(5, "OPTION", 13, false));
		list.add(new ConnectorInfo(6, "DISPLAY_PORT1", 15, true));
		list.add(new ConnectorInfo(7, "DISPLAY_PORT2", 16, false));
		list.add(new ConnectorInfo(8, "HDMI1", 17, true));
		list.add(new ConnectorInfo(9, "HDMI2", 18, false));
		list.add(new ConnectorInfo(10, "HDMI3", 130, false));
		list.add(new ConnectorInfo(11, "MP", 135, false));
		return list;
	}

	/**
	 * 入力 (モニターコマンド用)
	 */
	private enum Input {
		NO_MEAN(0), VGA(1), DVI(3), VIDEO(5), YGA(12), OPTION(13), DISPLAY_PORT1(15), DISPLAY_PORT2(16), HDMI1(17), HDMI2(18), HDMI3(130), MP(135), COMPUTE_MODULE(136);

		private final int id;

		Input(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	/**
	 * 電源状態 (モニターコマンド用)
	 */
	private enum PowerStatus {
		OFF(0), ON(1), POWER_SAVE(2), STANDBY(4), UNKNOWN(9);

		private final int id;

		PowerStatus(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}

		public static PowerStatus valueOf(int id) {
			return Arrays.stream(PowerStatus.values())
					.filter(data -> data.getId() == id)
					.findFirst()
					.orElse(null);
		}
	}

	//	/**
	//	 * 入力ポート (モニターコマンド用)
	//	 */
	//	private enum InputPort {
	//		VGA_RGB(1), DVI(3), VIDEO(5), VGA_YPBPR(12), OPTION(13), DISPLAY_PORT1(15), DISPLAY_PORT2(16), HDMI1(17), HDMI2(18), HDMI3(130), MP(135), COMPUTE_MODULE(136);
	//
	//		private final int id;
	//
	//		InputPort(int id) {
	//			this.id = id;
	//		}
	//
	//		public int getId() {
	//			return id;
	//		}
	//	}

	//	/**
	//	 * タイルマトリクス設定保存 (モニターコマンド用)
	//	 */
	//	private enum SaveTileSetting {
	//		COMMON(0), INPUT(1);
	//
	//		private final int id;
	//
	//		SaveTileSetting(int id) {
	//			this.id = id;
	//		}
	//
	//		public int getId() {
	//			return id;
	//		}
	//	}

	/**
	 * モニター設定値変更戻り値
	 */
	private enum SetParamRetCode {
		NG(-1), PARAM_ERROR(-2), OK(1), SKIP(2);

		private final int id;

		SetParamRetCode(int id) {
			this.id = id;
		}

		public int getId() {
			return id;
		}
	}

	private byte[] requestMessage(CrossSocketClient socket, byte[] sendData, Integer sleepTimer) throws Exception {

		try {
			waitSleep(0);
			// 送信
			log.info(DatatypeConverter.printHexBinary(sendData));
			byte[] result = socket.request(sendData);
			return result;

		} catch (Exception e) {

			log.error("socket error.");
			throw e;

		} finally {

			if (sleepTimer != null) {
				this.lastInstant = Instant.now().plusMillis(sleepTimer.longValue());
			} else {
				this.lastInstant = Instant.now();
			}
			long elapsedTime = Duration.between(Instant.now(), this.lastInstant).toMillis();
			log.info("★ {}({}) is next interval {} ms", productName, networkInfo.getIpAddress(), elapsedTime);
		}
	}

	private void sendMessage(CrossSocketClient socket, byte[] sendData, Integer sleepTimer) throws Exception {

		try {
			synchronized (this) {
				waitSleep(sleepTimer);
				// 送信
				log.info(DatatypeConverter.printHexBinary(sendData));
				socket.send(sendData);

			}

		} catch (Exception e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now();
		}
	}

	@Override
	public synchronized boolean selectInput(Integer connectorNumber, Integer inputSwitch) {

		// 入力切替
		ConnectorInfo info = CONNECTOR_LIST.stream().filter((connector) -> connector.selectable && Objects.equals(connector.connectorNumber, connectorNumber)).findFirst().orElse(null);

		if (info == null) {
			log.warn("Set input command null.");
			return false;
		}

		int setValue = info.getValue();

		String opCodePage = "00";
		String opCode = "60";

		boolean result = setMonitorParam(opCodePage, opCode, setValue, NO_SENDING_PERIOD_MSEC_INPUT_SW);
		if (!result) {
			log.error("Monitor parameter setting error. ip={}, port={}", networkInfo.getIpAddress(), networkInfo.getPort());
			return false;
		}

		return true;
	}

	@Override
	protected void healthCheck() {

		log.info("Health check start.");

		try {

			String ipAddress = displayDevice.getNetworkInfo().getIpAddress();

			if (!CommonUtil.checkIpAddressFormat(ipAddress)) {
				updateDeviceConnectStatus(false);
				log.info("Health check Invalid IP address.");
				return;
			}

			// ping送信
			Ping ping = new Ping(ipAddress, PING_TIMEOUT);
			if (ping.ping()) {
				// 自己診断ステータス取得コマンド送信
				final boolean deviceConnect = sendGetSelfDiagStatus();
				updateDeviceConnectStatus(deviceConnect);

			} else {
				log.error("Health check ping error. ip={}", ipAddress);
				updateDeviceConnectStatus(false);
			}

		} finally {
			log.info("Health check end.");
		}
	}

	/**
	 * 自己診断ステータス取得コマンド送信 (Self-diagnosis status read command)
	 * @param displaySplit 画面分割(面)情報
	 * @return コマンド応答結果 true:正常応答, false:送信エラー or エラー応答 or 機器異常
	 */
	private synchronized boolean sendGetSelfDiagStatus() {

		// 受信処理
		ResultMessageValidator<Boolean, byte[]> validator = (msg) -> {
			// Message の簡易チェック
			if (msg[0] == MSG_STX) {
				// Message の セルフテストの結果 を取得
				String result = new String(Arrays.copyOfRange(msg, 3, (msg.length - 1)));

				if (result.length() >= 2) {

					String value = result.substring(0, 2);
					if ("00".equals(value)) {
						return true;
					}
					
					ErrorCode errorCode = ERROR_CODE_LIST.stream().filter((error) -> Objects.equals( error.code,value )).findFirst().orElse(null);
					if (errorCode == null) {
						log.error("Self-diagnosis error. {}:{}", value, "不明なエラー");
					} else {
						log.error("Self-diagnosis error. {}:{}", value, errorCode.explanation);
					}
					return false;
				}

			} else {
				log.error("Failed to receive message.");
				return false;
			}
			return true;
		};

		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {

				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT);

				// Self-diagnosis status read command
				byte[] command = new byte[] { MSG_STX,
						'B', '1', // Command code
						MSG_ETX };

				// コマンド送信
				byte[] recvData = sendMessage(socket, HDR_MSG_TYPE_CMD, command);
				return validator.validate(recvData);
			//}

		} catch (IOException e) {

			log.error("socket connection error.", e);
			return false;
		} catch (Exception e) {

			log.error("socket connection error.", e);
			return false;
		}
	}

	/**
	 * モニター設定値変更
	 * @param displaySplitNo 画面分割番号
	 * @param ip 接続先IP
	 * @param port 接続先ポート番号
	 * @param opCodePage オペレーションコードのページ (モニター設定値の場所)
	 * @param opCode オペレーションコード (モニター調整値の場所)
	 * @param setValue 設定値
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean setMonitorParam(String opCodePage, String opCode, int setValue) {

		return setMonitorParam(opCodePage, opCode, setValue, null);
	}

	/**
	 * モニター設定値変更
	 * @param displaySplitNo 画面分割番号
	 * @param ip 接続先IP
	 * @param port 接続先ポート番号
	 * @param opCodePage オペレーションコードのページ (モニター設定値の場所)
	 * @param opCode オペレーションコード (モニター調整値の場所)
	 * @param setValue 設定値
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean setMonitorParam(String opCodePage, String opCode, int setValue, Integer sleepTimer) {

		if (opCodePage.length() != 2) {
			log.error("Parameter error. opCodePage={}", opCodePage);
			return false;
		}
		if (opCode.length() != 2) {
			log.error("Parameter error. opCode={}", opCode);
			return false;
		}
		if (setValue < 0 || 9999 < setValue) {
			log.error("Parameter error. setValue={}", setValue);
			return false;
		}

		ResultMessageValidator<Integer, byte[]> validator = (msg) -> {
			// Message の簡易チェック
			if (msg[0] == MSG_STX) {
				// Message の Result を取得
				String resultHex = new String(Arrays.copyOfRange(msg, 1, 3));
				int result = Integer.parseInt(resultHex, 16);
				if (result == 0) {
					// Message の Value を取得
					String valueHex = new String(Arrays.copyOfRange(msg, 13, 17));

					int value = Integer.parseInt(valueHex, 16);
					return value;

				} else {
					log.error("The message result code is an error. result={}", result);
					throw new Exception("message result code is an error.");
				}
			} else {
				log.error("Failed to receive message.");
				throw new Exception("Failed to receive message.");
			}
		};

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			synchronized (socket) {

				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT);

				{
					byte[] command = new byte[] { MSG_STX,
							opCodePage.getBytes()[0], opCodePage.getBytes()[1], // OP code page
							opCode.getBytes()[0], opCode.getBytes()[1], // OP code
							MSG_ETX };

					byte[] recvData = sendMessage(socket, HDR_MSG_TYPE_GET_PARAM, command);

					Integer value = validator.validate(recvData);

					if (setValue == value) {
						return true;
					}
				}

				{
					// Set parameter
					byte[] command = new byte[] { MSG_STX,
							opCodePage.getBytes()[0], opCodePage.getBytes()[1], // OP code page
							opCode.getBytes()[0], opCode.getBytes()[1], // OP code
							0x00, 0x00, 0x00, 0x00, // Set value
							MSG_ETX };
					// Set value
					byte[] inputBytes = String.format("%04X", setValue).getBytes();
					System.arraycopy(inputBytes, 0, command, 5, inputBytes.length);
					byte[] recvData = sendMessage(socket, HDR_MSG_TYPE_SET_PARAM, command, sleepTimer);

					Integer value = validator.validate(recvData);

					log.info("Parameter setting completed. opCodePage={}, opCode={}, setValue={}, value={}", opCodePage, opCode, setValue, value);
				}

				//				{
				//					byte[] command = new byte[] { MSG_STX,
				//							'0', 'C', // Command code
				//							MSG_ETX };
				//
				//					sendMessage(socket, HDR_MSG_TYPE_CMD, command);
				//				}

				return true;
			}

		} catch (IOException e) {

			log.error("socket connection error.", e);
			return false;
		} catch (Exception e) {

			log.error("socket connection error.", e);
			return false;
		}

	}

	/**
	 * メッセージ送信
	 * @param socket Open済みのSocket
	 * @param monitorId Header用モニターID
	 * @param messageType Header用メッセージタイプ
	 * @param command 送信メッセージ
	 * @param callback 受信コールバック
	 * @return 処理結果 true:成功, false:失敗
	 * @throws IOException 
	 */
	private byte[] sendMessage(CrossSocketClient socket, byte messageType, byte[] command) throws Exception {

		return sendMessage(socket, messageType, command, null);
	}

	/**
	 * メッセージ送信
	 * @param socket Open済みのSocket
	 * @param monitorId Header用モニターID
	 * @param messageType Header用メッセージタイプ
	 * @param command 送信メッセージ
	 * @param callback 受信コールバック
	 * @return 処理結果 true:成功, false:失敗
	 * @throws IOException 
	 */
	private byte[] sendMessage(CrossSocketClient socket, byte messageType, byte[] command, Integer sleepTimer) throws Exception {

		// Header の作成
		int monitorId = (this.displaySplitNumber + 1);

		byte destination = (byte) (monitorId + 0x40);
		byte[] header = {
				HDR_SOH, // SOH
				HDR_RESERVED, // Reserved ('0' 固定)
				destination, // Destination
				'0', // Source (コントローラは '0' 固定)
				messageType, // Message Type
				0x00, 0x00 }; // Message Length
		// Message Length
		byte[] lengthBytes = String.format("%02X", command.length).getBytes();
		System.arraycopy(lengthBytes, 0, header, HDR_IDX_MSG_LEN, lengthBytes.length);

		// Check code の作成
		byte cc = 0x00;
		// header
		byte[] ccHeader = new byte[HDR_SIZE_ALL - 1];
		System.arraycopy(header, 1, ccHeader, 0, ccHeader.length);
		for (byte b : ccHeader) {
			cc = (byte) (cc ^ b);
		}
		// Message
		for (byte b : command) {
			cc = (byte) (cc ^ b);
		}

		// Delimiter の作成
		byte delimiter = 0x0D;

		// 送信データの組立
		int dataLength = header.length + command.length; // Check code, Delimiter を抜いた送信データ長
		byte[] sendData = new byte[dataLength + 2]; // Check code, Delimiter を含めた長さを確保
		System.arraycopy(header, 0, sendData, 0, header.length);
		System.arraycopy(command, 0, sendData, header.length, command.length);
		sendData[dataLength] = cc;
		sendData[dataLength + 1] = delimiter;

		byte[] recvBytes = requestMessage(socket, sendData, sleepTimer);

		log.info("recvData: " + DatatypeConverter.printHexBinary(recvBytes));

		// ヘッダーの簡易チェック
		if ((recvBytes.length >= HDR_SIZE_ALL) && (recvBytes[HDR_IDX_SOH] == HDR_SOH)) {
			// Header から Message length を取得
			String msgLenHex = new String(
					Arrays.copyOfRange(recvBytes, HDR_IDX_MSG_LEN, HDR_IDX_MSG_LEN + HDR_SIZE_MSG_LEN));
			int msgLen = Integer.parseInt(msgLenHex, 16);

			if (msgLen > 0) {
				// message部のみを取得
				byte[] recvData = new byte[msgLen];
				System.arraycopy(recvBytes, HDR_SIZE_ALL, recvData, 0, msgLen);

				return recvData;

			} else {
				log.error("Message is empty.");
				throw new Exception("Message is empty.");
			}
		} else {
			log.error("Failed to receive headers.");
			throw new Exception("Failed to receive headers.");
		}
	}

	@Override
	public synchronized boolean setMultiMonitor(Integer sourceDispPattern) {

		boolean toggle = (sourceDispPattern == 3);

		return setTileMatrix(toggle);
	}

	/**
	 * タイルマトリクス設定
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @param toggle タイルマトリクス実行 (true:実行, false:解除)
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean setTileMatrix(boolean toggle) {

		log.info("setTileMatrix(): displayNo={}, displaySplitNo={}, toggle={}", displayNumber, displaySplitNumber, toggle);

		// タイルマトリクス 実行
		String opCodePage = "02";
		String opCode = "D3";
		int setValue = (toggle) ? 2 : 1; // 2:実行する, 1:実行しない

		//boolean result = setMonitorParam(opCodePage, opCode, setValue, NO_SENDING_PERIOD_MSEC_INPUT_SW);
		boolean result = setMonitorParam(opCodePage, opCode, setValue, 0);
		if (!result) {
			String format = "Moniter Device setTileMatrix(displayNo={}, displaySplitNo={}, toggle={})";
			log.error(format, displayNumber, displaySplitNumber, toggle);
			return false;
		}

		return true;
	}

	//	/**
	//	 * 電源状態取得 (モニタ通信処理)
	//	 * @param displaySplitNo 画面分割番号
	//	 * @param ip 接続先IP
	//	 * @param port 接続先ポート番号
	//	 * @return PowerStatus 電源状態
	//	 */
	//	private PowerStatus powerStatusRead(int displaySplitNo, String ip, int port) {
	//		byte[] message;
	//		int monitorId = displaySplitNo + 1;
	//
	//		// power status (受信コールバックから更新するためオブジェクト参照型としている)
	//		AtomicReference<PowerStatus> pSts = new AtomicReference<>(PowerStatus.UNKNOWN);
	//
	//		// 対象ディスプレイのソケットを用意
	//		SocketClient socket = SocketManager.getSocketClient(ip, port);
	//		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
	//			// Power status read command
	//			message = new byte[] { MSG_STX,
	//					'0', '1', 'D', '6', // “Get power status” コマンド
	//					MSG_ETX };
	//
	//			// 受信処理
	//			Consumer<byte[]> callback = (msg) -> {
	//				// Message の簡易チェック
	//				if (msg[0] == MSG_STX) {
	//					// Message の Result を取得
	//					String resultHex = new String(
	//							Arrays.copyOfRange(msg, 3, 5));
	//					int result = Integer.parseInt(resultHex, 16);
	//					if (result == 0) {
	//						// 正常
	//						// Message から Power status (4桁) を取得
	//						String powerStatusHex = new String(
	//								Arrays.copyOfRange(msg, 13, 17)); // power status 4桁 取得
	//						int powerStatus = Integer.parseInt(powerStatusHex, 16);
	//						pSts.set(PowerStatus.valueOf(powerStatus));
	//
	//					} else {
	//						log.error("The message result code is an error. result={}", result);
	//					}
	//				} else {
	//					log.error("Failed to receive message.");
	//				}
	//			};
	//			sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, callback);
	//			socket.close();
	//		} else {
	//			log.error("Socket connection error.");
	//		}
	//		return pSts.get();
	//	}

	//	/**
	//	 * 電源設定 (モニタ通信処理)
	//	 * @param displaySplitNo 画面分割番号
	//	 * @param ip 接続先IP
	//	 * @param port 接続先ポート番号
	//	 * @param setPowerStatus 設定する電源状態
	//	 * @return 処理結果 true:成功, false:失敗
	//	 */
	//	private boolean powerControl(int displaySplitNo, String ip, int port, PowerStatus setPowerStatus) {
	//		boolean ret;
	//		byte[] message;
	//		int monitorId = displaySplitNo + 1;
	//		AtomicBoolean rcevResult = new AtomicBoolean(false);
	//
	//		// 対象ディスプレイのソケットを用意
	//		SocketClient socket = SocketManager.getSocketClient(ip, port);
	//		if (socket.open(CONNECT_TIMEOUT, RECEIVE_TIMEOUT)) {
	//
	//			// Power control command
	//			message = new byte[] { MSG_STX,
	//					'C', '2', '0', '3', 'D', '6', // “Power control” コマンド
	//					0x00, 0x00, 0x00, 0x00, // Power status
	//					MSG_ETX };
	//			// Power status
	//			byte[] powerStatusBytes = String.format("%04X", setPowerStatus.getId()).getBytes();
	//			System.arraycopy(powerStatusBytes, 0, message, 7, powerStatusBytes.length);
	//
	//			// 受信処理
	//			Consumer<byte[]> powerControlCallback = (msg) -> {
	//				// Message の簡易チェック
	//				if (msg[0] == MSG_STX) {
	//					// Message の Result を取得
	//					String resultHex = new String(Arrays.copyOfRange(msg, 3, 5));
	//					int result = Integer.parseInt(resultHex, 16);
	//					if (result == 0) {
	//						// 正常
	//						rcevResult.set(true);
	//
	//					} else {
	//						log.error("The message result code is an error. result={}", result);
	//						rcevResult.set(false);
	//					}
	//				} else {
	//					log.error("Failed to receive message.");
	//					rcevResult.set(false);
	//				}
	//			};
	//
	//			boolean sendResult = sendMessage(socket, monitorId, HDR_MSG_TYPE_CMD, message, powerControlCallback);
	//			ret = (sendResult && rcevResult.get());
	//			socket.close();
	//
	//		} else {
	//			log.error("Socket connection error.");
	//			ret = false;
	//		}
	//		return ret;
	//	}

	//	/**
	//	 * 電源状態取得
	//	 * @param displayNo 表示盤番号
	//	 * @param displaySplitNo 画面分割番号
	//	 * @return 処理結果 true:成功, false:失敗
	//	 */
	//	public PowerStatus getPowerStatus(int displayNo, int displaySplitNo) {
	//		log.info("getPowerStatus(): displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
	//
	//		PowerStatus powerStatus = PowerStatus.UNKNOWN;
	//
	//		// 画面分割(面)情報 取得
	//		DisplaySplit displaySplit = getDisplaySplit(display, displaySplitNo);
	//
	//		if (displaySplit != null) {
	//			String ip = displaySplit.getIpAddress();
	//			int port = displaySplit.getPort();
	//
	//			powerStatus = powerStatusRead(displaySplitNo, ip, port);
	//		} else {
	//			// 対象のモニタが見つからない場合はログ出力して正常終了
	//			log.warn("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
	//		}
	//		log.info("Power status: {}({})", powerStatus.toString(), powerStatus.getId());
	//		return powerStatus;
	//	}

	//	/**
	//	 * 電源切替
	//	 * @param displayNo 表示盤番号
	//	 * @param displaySplitNo 画面分割番号
	//	 * @param powerStatus 電源設定
	//	 * @return 処理結果 true:成功, false:失敗
	//	 */
	//	public boolean switchPower(int displayNo, int displaySplitNo, PowerStatus powerStatus) {
	//		log.info("switchPower(): displayNo={}, displaySplitNo={}, powerStatus={}",
	//				displayNo, displaySplitNo, powerStatus.toString());
	//		boolean ret;
	//
	//		// 画面分割(面)情報 取得
	//		DisplaySplit displaySplit = getDisplaySplit(display, displaySplitNo);
	//
	//		if (displaySplit != null) {
	//			String ip = displaySplit.getIpAddress();
	//			int port = displaySplit.getPort();
	//
	//			PowerStatus curtPowerStatus = powerStatusRead(displaySplitNo, ip, port);
	//			if (powerStatus.getId() == curtPowerStatus.getId()) {
	//				// 既に目的の電源設定となっている場合は電源設定せずに正常終了
	//				return true;
	//			}
	//
	//			ret = powerControl(displaySplitNo, ip, port, powerStatus);
	//			if (ret) {
	//				// コマンド送信禁止期間の設定
	//				String key = ip + ":" + port; // keyの組立 (例)127.0.0.1:13000
	//				long msec = System.currentTimeMillis() + NO_SENDING_PERIOD_MSEC_POWER_SW;
	//				this.noSendingPeriodMsecMap.put(key, msec);
	//			}
	//		} else {
	//			// 対象のモニタが見つからない場合はログ出力して正常終了
	//			log.info("Target monitor not found. displayNo={}, displaySplitNo={}", displayNo, displaySplitNo);
	//			ret = true;
	//		}
	//		return ret;
	//	}

}
