package com.contents.devicemodel.monitor;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.BooleanUtils;

import com.contents.common.CommonUtil;
import com.contents.common.annotation.ExternalDeviceProduct;
import com.contents.common.external.cross.NetworkInfo;
import com.contents.manager.CrossSocketClient;
import com.contents.manager.CrossSocketClient.ConnectCallback;
import com.contents.pojo.Ping;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * モニターデバイスモデルクラス
 * <p>
 * 対応機種: PN-HS431
 */
@Slf4j
@ExternalDeviceProduct(names = { "PN-HS431" })
public class PnHs431 extends MonitorDeviceModel {

	private final int PING_TIMEOUT = 1; // 1秒
	private final int CONNECT_TIMEOUT = 1000;
	private final int RECEIVE_TIMEOUT = 10000; // 10秒

	private final String MSG_LOGIN = "Login:";
	private final String MSG_PASSWORD = "Password:";
	private final String MSG_NEW_PASSWORD = "New password:";
	private final String MSG_RETYPE_NEW_PASSWORD = "Retype new password:";
	private final String MSG_OK = "OK";
	private final String MSG_GOODBYE = "goodbye";
	private final String MSG_NEWLINE = "\r\n";
	private final int RES_INPUT_MAX_LEN = (2 + MSG_NEWLINE.length());
	private final int RES_TEMP_MAX_LEN = (3 + MSG_NEWLINE.length());

	private final String CMD_USER = "\r\n";
	private final String CMD_PASS = "\r\n";
	private final String CMD_BYE = "BYE\r\n";
	private final String CMD_NEWLINE = "\r\n";
	private final String CMD_INPUT = "INPS";
	private final String INPUT_PARM_TOGGLE = "   0";
	private final String INPUT_PARM_DSUB = "   2";
	private final String INPUT_PARM_HDMI1 = "  10";
	private final String INPUT_PARM_USB = "  11";
	private final String INPUT_PARM_HDMI2 = "  13";
	private final String INPUT_PARM_HDMI3 = "  18";
	private final String CMD_ENLG = "ENLG";
	private final String ENLG_OFF = "   0";
	private final String ENLG_ON = "   1";
	private final String CMD_TEMP = "ERRT";
	private final String PARM_GET = "????";
	private final String GET_TEMP = CMD_TEMP + PARM_GET + CMD_NEWLINE;
	private final String GET_INPUT = CMD_INPUT + PARM_GET + CMD_NEWLINE;
	private final String GET_ENLG = CMD_ENLG + PARM_GET + CMD_NEWLINE;

	public PnHs431() {

		this.CONNECTOR_LIST = createConnectorList();
	}

	@Data
	private class ConnectorInfo {

		public ConnectorInfo(Integer connectorNumber, String connectorName, Integer value, String command, boolean selectable) {
			this.connectorNumber = connectorNumber;
			this.connectorName = connectorName;
			this.value = value;
			this.command = command;
			this.selectable = selectable;
		}

		/** コネクター番号 */
		private Integer connectorNumber;

		/** コネクター名称 */
		private String connectorName;

		/** 値 */
		private Integer value;

		/** コマンド */
		private String command;

		/** 選択可否 */
		private boolean selectable;

		public String createSetInputCommand() {
			return CMD_INPUT + this.command + CMD_NEWLINE;
		}
	}

	private final ConnectCallback connectCallback = (response) -> {

		if (response != null) {
			String recv = new String(response);

			String login = MSG_LOGIN;
			if (!Objects.equals(login, recv)) {
				log.error("Connect response error. {}", recv);
				throw new Exception("Connect response error.");
			}
		}
	};

	protected List<ConnectorInfo> createConnectorList() {

		List<ConnectorInfo> list = new ArrayList<ConnectorInfo>();
		list.add(new ConnectorInfo(1, "HDMI1", 10, INPUT_PARM_HDMI1, true));
		list.add(new ConnectorInfo(2, "HDMI2", 13, INPUT_PARM_HDMI2, true));
		list.add(new ConnectorInfo(3, "HDMI3", 18, INPUT_PARM_HDMI3, true));
		list.add(new ConnectorInfo(4, "DSUB", 2, INPUT_PARM_DSUB, true));
		list.add(new ConnectorInfo(5, "USB", 11, INPUT_PARM_USB, false));
		return list;
	}

	protected final List<ConnectorInfo> CONNECTOR_LIST;

	private byte[] requestMessage(CrossSocketClient socket, byte[] sendData) throws Exception {

		try {

			waitSleep(0);
			// 送信
			return socket.request(sendData);

		} catch (Exception e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now().plusMillis(100L);
		}
	}

	private void sendMessage(CrossSocketClient socket, byte[] sendData) throws Exception {

		try {

			waitSleep(0);
			// 送信
			socket.send(sendData);

		} catch (Exception e) {

			log.error("socket error.");
			throw e;

		} finally {

			this.lastInstant = Instant.now().plusMillis(100L);
		}
	}

	@Override
	protected void healthCheck() {

		try {
			NetworkInfo networkInfo = displayDevice.getNetworkInfo();
			
			String ipAddress = networkInfo.getIpAddress();
			Integer port = networkInfo.getPort();

			if (!CommonUtil.checkIpAddressFormat(ipAddress)) {
				updateDeviceConnectStatus(false);
				log.info("Health check Invalid IP address.");
				return;
			}

			Ping ping = new Ping(ipAddress, PING_TIMEOUT);
			if (ping.ping()) {
				// ヘルスチェック用コマンド送信
				final boolean deviceConnect = healthExec(ipAddress, port);
				updateDeviceConnectStatus(deviceConnect);

			} else {
				log.warn("Health check ping error. ip={}", ipAddress);
				updateDeviceConnectStatus(false);
			}
		} catch (Exception e) {
			log.error("Health checkInstant error.", e);
			updateDeviceConnectStatus(false);
		}

	}

	/**
	 * ヘルスチェック用コマンド送信
	 * @param ip 接続先IPアドレス
	 * @param port 接続先ポート
	 * @return 処理結果 true:成功, false:失敗
	 */
	private synchronized boolean healthExec(String ip, int port) {

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {

				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT, connectCallback);

				// ログイン
				if (!sendLogin(socket)) {
					log.error("login error.");
					return false;
				}

				// 温度取得コマンド
				if (!sendGetTemp(socket)) {
					log.error("Get temperature error.");
					return false;
				}

				// 切断
				if (!sendLogout(socket)) {
					log.error("logout error.");
					return false;
				}

				return true;
			//}
		
		} catch (IOException e) {

			log.error("socket connection error.", e);

			// 切断
			if (!sendLogout(socket)) {
				log.error("logout error.");
				return false;
			}

			return false;
		} catch (Exception e) {

			log.error("socket connection error.", e);

			// 切断
			if (!sendLogout(socket)) {
				log.error("logout error.");
				return false;
			}

			return false;
		}

	}

	/**
	 * ログインコマンド送信
	 * @param socket Open済みのSocket
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendLogin(CrossSocketClient socket) {

		try {

			String recv = new String(requestMessage(socket, CMD_USER.getBytes()));
			String password = (MSG_NEWLINE + MSG_PASSWORD);
			if (!Objects.equals(password, recv)) {
				log.error("User response error.");
				return false;
			}

		} catch (Exception e) {
			log.error("User response error", e);
			return false;
		}

		try {

			String recv = new String(requestMessage(socket, CMD_PASS.getBytes()));
			String ok = (MSG_OK + MSG_NEWLINE);

			if (Objects.equals(ok, recv)) {
				return true;
			}

			String newPassword = (MSG_NEWLINE + MSG_NEW_PASSWORD);
			if (!Objects.equals(newPassword, recv)) {

				String incorrect = ("Login incorrect" + MSG_NEWLINE);
				if (Objects.equals(incorrect, recv)) {
					log.error("Password response error.");
					return false;
				}
			}

		} catch (Exception e) {
			log.error("Password response error", e);
			return false;
		}

		try {

			{
				String recv = new String(requestMessage(socket, CMD_PASS.getBytes()));
				String retypeNewPassword = (MSG_NEWLINE + MSG_RETYPE_NEW_PASSWORD);
				if (!Objects.equals(retypeNewPassword, recv)) {
					log.error("non return {}", MSG_RETYPE_NEW_PASSWORD);
					return false;
				}
			}

			{
				String recv = new String(requestMessage(socket, CMD_PASS.getBytes()));
				String ok = (MSG_OK + MSG_NEWLINE);

				if (Objects.equals(ok, recv)) {
					return true;
				}
			}

		} catch (Exception e) {
			log.error("User response error", e);
			return false;
		}

		return false;
	}

	/**
	 * 温度取得コマンド送信
	 * @param socket Open済みのSocket
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendGetTemp(CrossSocketClient socket) {

		try {
			byte[] bytes = requestMessage(socket, GET_TEMP.getBytes());
			String message = new String(bytes);

			if (!responseCheckValue(message, RES_TEMP_MAX_LEN)) {
				log.error("Get temperature response value error.");
				return false;
			}

		} catch (Exception e) {
			log.error("Get temperature response value error.", e);
		}

		return true;
	}

	/**
	 * ログアウトコマンド送信
	 * @param socket Open済みのSocket
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendLogout(CrossSocketClient socket) {

		try {
			byte[] bytes = requestMessage(socket, CMD_BYE.getBytes());
			String message = new String(bytes);

			if (!Objects.equals((MSG_GOODBYE + MSG_NEWLINE), message)) {
				log.error("Logout response error.");
				return false;
			}

		} catch (Exception e) {
			log.error("Logout response error.", e);
			return false;
		}

		return true;
	}

	/**
	 * コマンドレスポンス確認
	 * @param str レスポンス文字列
	 * @param lenMax レスポンス文字列最大長
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean responseCheckValue(String str, int lenMax) {
		// コマンド長
		if (str == null || str.length() > lenMax) {
			log.error("Response length error.");
			return false;
		}

		// 改行コード除去
		String chkStr = str.replace(MSG_NEWLINE, "");

		// 数値確認
		if (chkStr.matches("[0-9]+")) {
			int getVal = Integer.parseInt(chkStr);
			log.info("Get val = " + getVal);
		} else {
			log.error("Response value error.");
			return false;
		}
		return true;
	}

	/**
	 * 入力切替設定要求
	 * @param inputSw 入力切替
	 * @param displayNo 表示盤番号
	 * @param displaySplitNo 画面分割番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public synchronized boolean selectInput(Integer connectorNumber, Integer inputSwitch) {

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {

				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT, connectCallback);

				// ログイン
				if (!sendLogin(socket)) {
					log.error("login error.");
					return false;
				}

				// 入力切替コマンド
				if (!sendSetInput(socket, connectorNumber)) {
					log.error("Set input error.");
					return false;
				}

				// 切断
				if (!sendLogout(socket)) {
					log.error("logout error.");
					return false;
				}

				return true;
			//}

		} catch (IOException e) {

			log.error("socket connection error.", e);

			// 切断
			if (!sendLogout(socket)) {
				log.error("logout error.");
				return false;
			}

			return false;
		} catch (Exception e) {

			log.error("socket connection error.", e);

			// 切断
			if (!sendLogout(socket)) {
				log.error("logout error.");
				return false;
			}

			return false;
		}
	}

	/**
	 * 入力切替コマンド送信
	 * @param socket Open済みのSocket
	 * @param input 入力設定
	 * @return 処理結果 true:成功, false:失敗
	 */
	private boolean sendSetInput(CrossSocketClient socket, Integer connectorNumber) {

		try {
			ConnectorInfo info = CONNECTOR_LIST.stream().filter((connector) -> connector.selectable && connector.connectorNumber == connectorNumber).findFirst().orElse(null);
			if (info == null) {
				log.warn("Set input command null.");
				return false;
			}

			try {

				String command = GET_INPUT;

				byte[] bytes = requestMessage(socket, command.getBytes());
				String message = new String(bytes);

				if (message != null) {

					message = message.replace(MSG_NEWLINE, "");
					Integer inps = Integer.valueOf(message);
					if (Objects.equals(info.value, inps))
						return true;
				}

			} catch (Exception e) {
				log.warn("get ispn error ");
			}

			String command = info.createSetInputCommand();

			try {
				byte[] bytes = requestMessage(socket, command.getBytes());
				String message = new String(bytes);

				// 応答チェック
				if (!Objects.equals((MSG_OK + MSG_NEWLINE), message)) {
					log.error("Set input response error.");
					return false;
				}

				return true;
			} catch (Exception e) {

				log.error("Set input response error.");
			}

			return true;

		} catch (Exception e) {

			log.error("input send error.", e);
			return false;
		}
	}

	@Override
	public synchronized boolean setMultiMonitor(Integer sourceDispPattern) {

		log.info("★★タイルマトリクス設定");

		boolean toggle = (sourceDispPattern == 3);

		// ソケット接続
		try (CrossSocketClient socket = loadSocketClient()) {

			//synchronized (socket) {

				socket.connect(CONNECT_TIMEOUT, RECEIVE_TIMEOUT, connectCallback);

				// ログイン
				if (!sendLogin(socket)) {
					log.error("login error.");
					return false;
				}

				// エンラージ設定ON/OFF
				if (!setEnlarge(socket, toggle)) {
					log.error("Set input response error.");
					return false;
				}

				// 切断
				if (!sendLogout(socket)) {
					log.error("logout error.");
					return false;
				}
			//}

		} catch (IOException e) {

			log.error("socket connection error.", e);

			// 切断
			if (!sendLogout(socket)) {
				log.error("logout error.");
				return false;
			}

			return false;

		} catch (Exception e) {

			log.error("socket connection error.", e);

			// 切断
			if (!sendLogout(socket)) {
				log.error("logout error.");
				return false;
			}

			return false;

		}
		return true;

	}

	private boolean setEnlarge(CrossSocketClient socket, boolean toggle) throws IOException {

		try {

			String command = GET_ENLG;

			String recv = new String(requestMessage(socket, command.getBytes()));

			if (recv != null) {

				recv = recv.replace(MSG_NEWLINE, "");
				log.info(recv);
				boolean enlg = BooleanUtils.toBoolean(recv);

				if (enlg == toggle)
					return true;
			}

		} catch (Exception e) {
			log.warn("get enlarge error");
		}

		try {

			// エンラージコマンド送信
			String command = toggle ? (CMD_ENLG + ENLG_ON + MSG_NEWLINE) : (CMD_ENLG + ENLG_OFF + MSG_NEWLINE);
			log.info(command);
			String recv = new String(requestMessage(socket, command.getBytes()));

			String ok = (MSG_OK + MSG_NEWLINE);
			if (!Objects.equals(ok, recv)) {
				log.error("command error input change");
				return false;
			}

			return true;

		} catch (Exception e) {
			log.warn("set enlarge error");
			return false;
		}
	}
}
