package com.contents.devicemodel.switcher;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.contents.common.CommonUtil;
import com.contents.common.MessageFormatUtil;
import com.contents.common.db.Control;
import com.contents.common.external.cross.DeviceLinkItem;
import com.contents.common.external.cross.DisplayGroupInfo;
import com.contents.common.external.cross.MatrixSwitcherDevice;
import com.contents.common.external.cross.RoutingInformation;
import com.contents.common.external.cross.SwitcherAudioRoutingInfo;
import com.contents.common.external.cross.SwitcherVideoRoutingInfo;
import com.contents.common.external.cross.VolumeControllerRoutingInfo;
import com.contents.devicemodel.DeviceModel;
import com.contents.devicemodel.ExecutionPlan;
import com.contents.devicemodel.madiareceiver.MediaReceiverModel;
import com.contents.devicemodel.monitor.MonitorDeviceModel;
import com.contents.devicemodel.sound.SoundVolumeDeviceModel;
import com.contents.devicemodel.videosource.VideoSourceDeviceModel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class MatrixSwitcherDeviceModel extends DeviceModel {

	protected MatrixSwitcherDevice matrixSwitcherDevice;

	protected SwitcherVideoRoutingInfo switcherVideoRoutingInfo;

	protected SwitcherAudioRoutingInfo switcherAudioRoutingInfo;

	protected List<VolumeControllerRoutingInfo> volumeControllerRoutingList;

	public void bind(MatrixSwitcherDevice matrixSwitcherDevice) {
		this.matrixSwitcherDevice = matrixSwitcherDevice;
		this.deviceNumber = matrixSwitcherDevice.getDeviceNumber();
		this.productName = matrixSwitcherDevice.getProductName();
		this.networkInfo = matrixSwitcherDevice.getNetworkInfo();
	}

	public void expand(RoutingInformation linkInformation) {

		{
			java.util.Optional<SwitcherVideoRoutingInfo> optional = linkInformation.getSwitcherVideoRoutingList().stream().filter((link) -> Objects.equals(link.getDeviceNumber(), this.deviceNumber)).findFirst();

			if (!optional.isEmpty()) {
				this.switcherVideoRoutingInfo = optional.get();
			}
		}

		{

			java.util.Optional<SwitcherAudioRoutingInfo> optional = linkInformation.getSwitcherAudioRoutingList().stream().filter((link) -> Objects.equals(link.getDeviceNumber(), this.deviceNumber)).findFirst();

			if (!optional.isEmpty()) {
				this.switcherAudioRoutingInfo = optional.get();
			}
		}

		{
			this.volumeControllerRoutingList = linkInformation.getVolumeControllerRoutingList().stream().filter((link) -> link.getInputList().stream().anyMatch((input) -> Objects.equals(input.getDeviceNumber(), this.deviceNumber))).toList();
		}
	}

	public List<ExecutionPlan> createExecutionPlan(Control control) {

		List<VideoSourceDeviceModel> videoSourceDeviceModelList = deviceModelInfo.getVideoSourceDeviceModelList();
		List<SoundVolumeDeviceModel> soundVolumeDeviceModelList = deviceModelInfo.getSoundVolumeDeviceModelList();

		List<MediaReceiverModel> mediaReceiverModelList = deviceModelInfo.getMediaReceiverModelList();
		List<MonitorDeviceModel> monitorDeviceModelList = deviceModelInfo.getMonitorDeviceModelList();
		List<MatrixSwitcherDeviceModel> matrixSwitcherDeviceModelList = deviceModelInfo.getMatrixSwitcherDeviceModelList();

		List<ExecutionPlan> executionPlanList = new ArrayList<ExecutionPlan>();

		Integer displayType = control.getDisplay_type();
		Integer displayNo = control.getDisplay_no();
		Integer displaySplitNo = control.getDisplay_split_no();
		Integer sourceNo = control.getSource_no();
		Integer inputSwitch = control.getInput_switch();
		Integer isVolumeControl = control.getIs_vol_control();

		try {
			if (inputSwitch == null) {
				log.info("inputSwitch=null");
				return null;
			}

			if (inputSwitch == 0) {
				log.info("inputSwitch={}", inputSwitch);
				return null;
			}

			VideoSourceDeviceModel videoSourceDeviceModel = videoSourceDeviceModelList.stream().filter((model) -> Objects.equals(model.getVideoSourceNumber(), sourceNo)).findFirst().orElse(null);

			if (videoSourceDeviceModel == null) {
				log.info("non sourceNo {}", sourceNo);
				return null;
			}

			MonitorDeviceModel monitorDeviceModel = monitorDeviceModelList.stream().filter((monitor) -> monitor.match(displayType, displayNo, displaySplitNo)).findFirst().orElse(null);

			if (monitorDeviceModel == null)
				return null;
			
			DeviceLinkItem videoInputLink = null;
			DeviceLinkItem videoOutputLink = null;

			if (this.switcherVideoRoutingInfo != null) {

				videoInputLink = this.switcherVideoRoutingInfo.getInputList().stream().filter((input) -> Objects.equals(input.getDeviceNumber(), videoSourceDeviceModel.getDeviceNumber())).findFirst().orElse(null);
				videoOutputLink = switcherVideoRoutingInfo.getOutputList().stream().filter((output) -> Objects.equals(output.getDeviceNumber(), monitorDeviceModel.getDeviceNumber())).findFirst().orElse(null);
			}
			
			DeviceLinkItem soundInputLink = null;
			DeviceLinkItem soundOutputLink = null;

			if (isVolumeControl != null && this.switcherAudioRoutingInfo != null) {

				soundInputLink = this.switcherAudioRoutingInfo.getInputList().stream().filter((input) -> Objects.equals(input.getDeviceNumber(), videoSourceDeviceModel.getDeviceNumber())).findFirst().orElse(null);

				DisplayGroupInfo displayGroupInfo = monitorDeviceModel.getDisplayGroupInfo();

				VolumeControllerRoutingInfo audioLinkInfo = this.volumeControllerRoutingList.stream().filter((link) -> link.getOutputList().stream().anyMatch((output) -> Objects.equals(output.getDeviceNumber(), displayGroupInfo.getDeviceNumber()))).findFirst().orElse(null);

				DeviceLinkItem audioOutputLink = audioLinkInfo.getOutputList().stream().filter((output) -> Objects.equals(output.getDeviceNumber(), displayGroupInfo.getDeviceNumber())).findFirst().orElse(null);

				soundOutputLink = this.switcherAudioRoutingInfo.getOutputList().stream().filter((output) -> Objects.equals(output.getDeviceNumber(), audioLinkInfo.getDeviceNumber()) && Objects.equals(output.getConnectorNumber(), audioOutputLink.getConnectorNumber())).findFirst().orElse(null);
			}
			
			final DeviceLinkItem exeVideoInputLink = videoInputLink;
			final DeviceLinkItem exeVideoOutputLink = videoOutputLink;
			
			final DeviceLinkItem exeSoundInputLink = soundInputLink;
			final DeviceLinkItem exeSoundOutputLink = soundOutputLink;
			
			
			ExecutionPlan plan = new ExecutionPlan() {

				@Override
				public List<String> transmit() {
					
					synchronized (TRANSMIT_LOCK_OBJECT) {
						
						List<String> errors = new ArrayList<String>();
						
						if (exeVideoInputLink != null && exeVideoOutputLink != null) {
							
							String format = "マトリックススイッチャー(映像)の切り替えに失敗しました。displayType={},displayNo={},displaySplitNo ={},sourceNo={},inputSwitch={}";
							Object[] params = new Object[] { displayType, displayNo, displaySplitNo, sourceNo, inputSwitch };
							
							try {
								boolean result = videoSwitch(exeVideoInputLink.getConnectorNumber(), exeVideoOutputLink.getConnectorNumber());
								if (!result) {
									errors.add(MessageFormatUtil.format(format, params));
								}
							} catch (Exception e) {
								log.error(format, params);
								log.error("MatrixSwitcherDeviceModel transmit videoSwitch", e);
								log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
								errors.add(MessageFormatUtil.format(format, params));
							}
						}
						
						if (exeSoundInputLink != null && exeSoundOutputLink != null) {
							
							String format = "マトリックススイッチャー(音声)の切り替えに失敗しました。displayType={},displayNo={},displaySplitNo ={},sourceNo={},inputSwitch={}";
							Object[] params = new Object[] { displayType, displayNo, displaySplitNo, sourceNo, inputSwitch };
							
							try {
								Integer inputConnectorNumber = exeSoundInputLink.getConnectorNumber();
								Integer outoutConnectorNumber = exeSoundOutputLink.getConnectorNumber();
								boolean result = audioSwitch(inputConnectorNumber, outoutConnectorNumber);
								if (!result) {
									errors.add(MessageFormatUtil.format(format, params));
								}
							} catch (Exception e) {
								log.error(format, params);
								log.error("MatrixSwitcherDeviceModel transmit audioSwitch", e);
								log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
								errors.add(MessageFormatUtil.format(format, params));
							}
						}
						
						return errors;
					}
				}
			};
			
			executionPlanList.add(plan);
			
			// TODO 機器構成の要素でエラーが発生した場合に、１回あたりの受信すべてをエラーにするか、活きている機器だけは処理しきるのか要相談
			//return executionPlanList;
			
		} catch (Exception e) {
			log.error("createExecutionPlan control error.", e);
			String format = "マトリックススイッチャーの切り替えに失敗しました。model={}, ip={}, displayType={}, displayNo={}, displaySplitNo={}, sourceNo={}, inputSwitch={}";
			Object[] params = new Object[] { this.productName, this.networkInfo.getIpAddress(), displayType, displayNo, displaySplitNo, sourceNo, inputSwitch };
			String message = MessageFormatUtil.format(format, params);
			log.error(message);
			log.error(MessageFormatUtil.format("DeviceModel: {}, IP: [{}]", productName, traceAccessInfo()));
			//throw new Exception(message, e);
		}

		return executionPlanList;
	}

	public abstract boolean videoSwitch(Integer inputConnectorNumber, Integer outoutConnectorNumber);

	/**
	 * 音声切替
	 * @param sourceNo ソース番号
	 * @param displayNo 表示盤番号
	 * @return 処理結果 true:成功, false:失敗
	 */
	public abstract boolean audioSwitch(Integer inputConnectorNumber, Integer outoutConnectorNumber);

	/**
	 * videoLinkInfoを取得します。
	 * @return videoLinkInfo
	 */
	public SwitcherVideoRoutingInfo getSwitcherVideoRoutingInfo() {
		return switcherVideoRoutingInfo;
	}

}
